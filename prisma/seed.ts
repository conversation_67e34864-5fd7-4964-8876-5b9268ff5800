import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
    const webinars = [];

    for (const webinar of webinars) {
        await prisma.webinar.createMany({
            data: webinars,
        });
    }

    console.log('Database has been seeded.');
}

main()
    .catch((e) => {
        console.error(e);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
    });
