{"name": "webinars", "version": "1.1.1", "description": "Сер<PERSON>и<PERSON> вебинаров", "author": "Денис Благовещенский", "license": "UNLICENSED", "private": true, "scripts": {"preinstall": "only-allow pnpm", "build": "nest build", "format": "prettier --config prettier.config.cjs --write \"src/**/*.ts\" \"tests/**/*.ts\"", "prelint": "pnpm prisma:gen", "lint": "eslint \"{src,tests}/**/*.ts\"", "lint:fix": "eslint \"{src,tests}/**/*.ts\" --fix", "start": "node dist/src/main.js", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main.js", "prisma:gen": "prisma generate", "db:push": "prisma db push", "seed:run": "ts-node prisma/seed.ts", "migrate:status": "migrate-mongo status", "migrate:up": "migrate-mongo up", "migrate:down": "migrate-mongo down", "bind-webhook": "node ./dist/src/scripts/bind-webhook.js", "pretest": "npm run prisma:gen", "test": "exit 0", "test:integration": "jest --config='tests/jest.config.js' --runInBand -b", "test:unit": "jest --config='tests/jest.unit.config.js'", "check-app1": "node dist/check-app.js", "check-app": "exit 0", "release": "semantic-release"}, "dependencies": {"@apollo/subgraph": "2.2.3", "@aws-sdk/client-s3": "^3.703.0", "@aws-sdk/lib-storage": "^3.705.0", "@aws-sdk/s3-request-presigner": "^3.703.0", "@nestjs/common": "^10.4.6", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.6", "@nestjs/cqrs": "^10.2.8", "@nestjs/event-emitter": "^2.1.1", "@nestjs/platform-express": "^10.4.6", "@nestjs/schedule": "^5.0.1", "@nestjs/terminus": "^10.2.3", "@prisma/client": "^6.1.0", "@skillspace/access": "1.0.5", "@skillspace/amqp-contracts": "^2.9.0", "@skillspace/common": "^1.2.0", "@skillspace/cqrs": "1.0.0", "@skillspace/graphql": "1.3.1", "@skillspace/grpc": "^1.7.0", "@skillspace/logger": "4.2.0", "@skillspace/tracing": "1.0.1", "@skillspace/utils": "0.1.1", "bson": "^6.9.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "express": "^5.1.0", "fp-ts": "^2.16.9", "graphql": "^16.9.0", "multer": "1.4.5-lts.1", "only-allow": "^1.2.1", "prisma": "^6.1.0", "rxjs": "7.8.1", "slugify": "^1.6.6", "zod": "^3.24.4"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/compat": "^1.2.2", "@eslint/js": "^9.23.0", "@golevelup/ts-jest": "^0.5.6", "@nestjs/cli": "^10.4.7", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.6", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/gitlab": "^13.2.4", "@skillspace/eslint-service": "^1.2.2", "@skillspace/hermes": "^1.0.2", "@testcontainers/mongodb": "^10.19.0", "@testcontainers/rabbitmq": "^10.18.0", "@types/eslint": "^9.6.1", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.9.0", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.13.0", "@typescript-eslint/parser": "^8.13.0", "conventional-changelog-cli": "2.2.2", "eslint": "^9.14.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^4.2.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.12.0", "graphql-tag": "^2.12.6", "husky": "^8.0.0", "jest": "^29.7.0", "prettier": "^3.3.3", "semantic-release": "24.2.3", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "tsconfig-paths": "4.2.0", "typescript": "^5.6.3", "typescript-eslint": "^8.30.1"}, "pnpm": {"overrides": {}}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}