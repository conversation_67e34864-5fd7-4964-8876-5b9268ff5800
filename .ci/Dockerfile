# Используем базовый образ Node.js
FROM node:20-alpine AS base

# Устанавливаем pnpm
RUN npm install -g pnpm@9.15.4
RUN pnpm config set global-bin-dir /usr/local/bin && \
    pnpm config set prefix /usr/local

# Устанавливаем @nestjs/cli глобально
RUN pnpm install -g @nestjs/cli
RUN npm install -g migrate-mongo@9.0.0

# Устанавливаем необходимые пакеты
RUN apk add --no-cache git

# Создаем пользователя и группу
RUN deluser --remove-home node && \
    addgroup -S node -g 1000 && \
    adduser -S -G node -u 1000 node

# Копируем файлы проекта
COPY ./ /app/
WORKDIR /app

# Создаем файл .npmrc с подстановкой реального токена во время сборки
# Обратите внимание на правильные URL для gitlab registry
ARG NPM_TOKEN_SKILLSPACE
# Настраиваем универсальную авторизацию для всех пакетов GitLab
RUN echo "@skillspace:registry=https://gitlab.sksp.site/api/v4/packages/npm/" > .npmrc && \
    echo "//gitlab.sksp.site/:_authToken=${NPM_TOKEN_SKILLSPACE}" >> .npmrc

# Для отладки - показать содержимое .npmrc без токена
RUN cat .npmrc | sed 's/glpat-[^ ]*$/[MASKED]/' || true

# Вывод переменных окружения для диагностики (без показа токена)
RUN echo "NPM_TOKEN is set: $([[ -n "${NPM_TOKEN_SKILLSPACE}" ]] && echo "YES" || echo "NO")"

# Устанавливаем зависимости с подробным логированием
RUN pnpm install

# Генерация Prisma клиента
RUN npx prisma generate

# Сборка приложения
RUN pnpm build

# Удаляем .npmrc с токеном из финального образа для безопасности
RUN rm -f .npmrc

# Устанавливаем переменную окружения NODE_ENV
ENV NODE_ENV=production
RUN pnpm prune --prod                      # вычистить dev-пакеты (необязательно, но уменьшает размер)

CMD ["node", "dist/main.js"]
