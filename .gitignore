# compiled output
/dist
/node_modules

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
src/_scripts/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
Taskfile.yaml

# IDE - VSCode
.vscode
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# env
/.env

# data
/.data
/init-mongo.js
