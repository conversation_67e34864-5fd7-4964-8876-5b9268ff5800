module.exports = {
    /**
     * @param db {import('mongodb').Db}
     * @param client {import('mongodb').MongoClient}
     * @returns {Promise<void>}
     */
    async up(db, client) {
        const streamsCollection = db.collection('streams');
        const webinarsCollection = db.collection('webinars');
        const total = await streamsCollection.countDocuments({ schoolUuid: { $exists: false } });
        console.log(`👌 Найдено ${total} потоков без schoolUuid`);

        const cursor = streamsCollection.find({ schoolUuid: { $exists: false } });

        while (await cursor.hasNext()) {
            const stream = await cursor.next();

            const webinar = await webinarsCollection.findOne(
                { _id: stream.webinarId },
                { projection: { schoolUuid: 1 } },
            );

            if (!webinar || !webinar.schoolUuid) {
                console.log(`❌ Не удалось получить schoolUuid для вебинара ${stream.webinarId}`);
                continue;
            }
            await streamsCollection.updateOne({ _id: stream._id }, { $set: { schoolUuid: webinar.schoolUuid } });
        }

        console.log(`👌 Обновлено ${total} потоков`);
        await cursor.close();
    },

    /**
     * Убираем schoolUuid из тех стримов, где он был установлен
     * @param db {import('mongodb').Db}
     * @param client {import('mongodb').MongoClient}
     * @returns {Promise<void>}
     */
    async down(db, client) {
        const streamsCollection = db.collection('streams');
        const cursor = streamsCollection.find({ schoolUuid: { $exists: true } });

        const total = await streamsCollection.countDocuments({ schoolUuid: { $exists: true } });
        console.log(`👌 Найдено ${total} потоков для очистки schoolUuid`);

        while (await cursor.hasNext()) {
            const stream = await cursor.next();
            await streamsCollection.updateOne({ _id: stream._id }, { $unset: { schoolUuid: '' } });
        }

        console.log(`👌 Обновлено ${total} потоков`);

        await cursor.close();
    },
};
