import { join } from 'node:path';
import { LogLevel } from '@skillspace/logger';
import { MongoDBContainer } from '@testcontainers/mongodb';
import { RabbitMQContainer } from '@testcontainers/rabbitmq';
import { config } from 'dotenv';

import { LiveDigitalClient } from '../../src/webinars/infrastructure/adapters/saas/live-digital/client/live-digital.client';

const TEST_SPACE_NAME = 'IntegrationTestSpace';
const TEST_SPACE_DESCRIPTION = '';

config({ path: join(process.cwd(), '.env.example') });

module.exports = async () => {
    // Создаем и запускаем контейнер MongoDB
    const mongoDbContainer = await new MongoDBContainer('mongo:7.0').withReuse().start();
    const baseUri = mongoDbContainer.getConnectionString();
    const mongoUri = `${baseUri}/webinars?directConnection=true&replicaSet=rs0`;

    // Создаем и запускаем контейнер RabbitMQ
    const rabbitMqContainer = await new RabbitMQContainer('rabbitmq:3.9-management').withReuse().start();
    const rabbitUri = rabbitMqContainer.getAmqpUrl();

    // Устанавливаем переменные окружения
    process.env.RMQ_URL = rabbitUri;
    process.env.DATABASE_URL = mongoUri;
    process.env.OTEL_DISABLED = 'true';
    process.env.LOG_LEVEL = LogLevel.Silent;

    process.env.RUN_SAAS_TESTS = 'false';

    if (process.env.RUN_SAAS_TESTS === 'true') {
        const liveDigital = new LiveDigitalClient();
        await liveDigital.deleteUnusedTestSpaces();

        const testSpace = await liveDigital.createSpace(TEST_SPACE_NAME, TEST_SPACE_DESCRIPTION);
        process.env.LIVE_DIGITAL_SPACE_ID = testSpace.id;
        global.__LIVE_DIGITAL_SPACE_ID__ = testSpace.id;
    }

    // Сохраняем ссылки на контейнеры в глобальном объекте
    global.__MONGO_CONTAINER__ = mongoDbContainer;
    global.__RABBITMQ_CONTAINER__ = rabbitMqContainer;

    console.log('MongoDB container started with URI:', process.env.DATABASE_URL);
    console.log('RabbitMQ container started with URL:', process.env.RMQ_URL);
};
