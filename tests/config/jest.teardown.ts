import { LiveDigitalClient } from '../../src/webinars/infrastructure/adapters/saas/live-digital/client/live-digital.client';

module.exports = async () => {
    //                                   # bytes |  KB  | MB   | GB
    const gbNow = process.memoryUsage().heapUsed / 1024 / 1024 / 1024;
    const gbRounded = Math.round(gbNow * 100) / 100;
    console.log(`<PERSON><PERSON> allocated ${gbRounded} GB`);

    if (global.__MONGO_CONTAINER__) {
        await global.__MONGO_CONTAINER__.stop();
    }

    if (global.__RABBITMQ_CONTAINER__) {
        await global.__RABBITMQ_CONTAINER__.stop();
    }

    if (process.env.RUN_SAAS_TESTS === 'true') {
        const liveDigital = new LiveDigitalClient();
        if (global.__LIVE_DIGITAL_SPACE_ID__) {
            await liveDigital.deleteSpace(global.__LIVE_DIGITAL_SPACE_ID__);
        }

        await liveDigital.deleteUnusedTestSpaces();
    }

    process.exit(0);
};
