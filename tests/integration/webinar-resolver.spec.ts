import { ParticipantRole, Prisma } from '@prisma/client';

import { ERROR_CODE } from '../../src/shared/webinar-service.error';
import { CardsViewMode, WebinarVisibilityPresentation } from '../../src/webinars/presentation/enum.presentation';
import {
    ACTION_WEBINAR_CREATE,
    ACTION_WEBINAR_MODIFY,
    ACTION_WEBINAR_REMOVE,
    ACTION_WEBINAR_VIEW,
    FAKE_ID,
} from './__data__/test-constants';
import {
    ADMIN_CONTEXT,
    ANOTHER_SCHOOL_UUID,
    canModifyStreamRes,
    canModifyWebinarRes,
    EMPLOYEE_CONTEXT,
    GUEST_CONTEXT,
    notModifyWebinarRes,
    OTHER_STUDENT_UUIDS,
    OTHER_STUDENTS,
    SCHOOL_UUID,
    SPEAKER_CONTEXT,
    STUDENT_CONTEXT,
    STUDENT_UUID,
    UNKNOWN_STUDENT_CONTEXT,
    webinarCreateInputMandatory,
} from './__data__/webinar.data';
import { testStreamsInput1, testStreamsInput2 } from './__data__/webinar-cards.data';
import {
    notifyAboutCreatedStream,
    notifyAboutRemovedStreams,
    notifyAboutRemovedWebinars,
    prisma,
    streamResolver,
    webinarResolver,
} from './test-setup';

const webinarUpdateStudentCapacityInput = {
    enableStudentLimit: true,
    maxStudentCapacity: 100,
};

export function formatDuration(milliseconds: number) {
    const hours = milliseconds / 3_600_000;
    return `${hours.toFixed(1)}ч`;
}

const cardsMapper = (cards) =>
    cards.map((c) => ({
        title: c.title,
        date: new Date(c.date).toISOString(),
        duration: formatDuration(c.duration),
    }));

const cardsMapperForSpeaker = (cards) =>
    cards.map((c) => ({
        title: c.title,
        date: new Date(c.date).toISOString(),
        duration: formatDuration(c.duration),
        isSpeaker: c.isSpeaker,
    }));

describe('Webinar Resolver', () => {
    /** WEBINARS */

    describe('Webinars', () => {
        afterEach(async () => {
            await Promise.all([
                prisma.webinar.deleteMany({}),
                prisma.stream.deleteMany({}),
                prisma.participant.deleteMany({}),
            ]);
        });

        describe('Получение вебинаров', () => {
            it('Должен получить вебинар для сотрудника', async () => {
                const createdWebinar = await webinarResolver.createWebinar(
                    ADMIN_CONTEXT,
                    SCHOOL_UUID,
                    webinarCreateInputMandatory,
                );

                const webinar = await webinarResolver.getOneWebinar(ADMIN_CONTEXT, SCHOOL_UUID, createdWebinar.id);
                expect(webinar).toEqual(canModifyWebinarRes);
            });

            it('Должен получить опубликованный вебинар для любого студента', async () => {
                const createdWebinar = await webinarResolver.createWebinar(ADMIN_CONTEXT, SCHOOL_UUID, {
                    ...webinarCreateInputMandatory,
                    visibility: WebinarVisibilityPresentation.public,
                });

                const webinar = await webinarResolver.getOneWebinar(
                    UNKNOWN_STUDENT_CONTEXT,
                    SCHOOL_UUID,
                    createdWebinar.id,
                );
                expect(webinar).toEqual({
                    ...notModifyWebinarRes,
                    visibility: 'public',
                });
            });

            it('Должен получить опубликованный вебинар для гостя', async () => {
                const createdWebinar = await webinarResolver.createWebinar(ADMIN_CONTEXT, SCHOOL_UUID, {
                    ...webinarCreateInputMandatory,
                    visibility: WebinarVisibilityPresentation.public,
                });

                const webinar = await webinarResolver.getOneWebinar(GUEST_CONTEXT, SCHOOL_UUID, createdWebinar.id);
                expect(webinar).toEqual({
                    ...notModifyWebinarRes,
                    visibility: 'public',
                });
            });

            it('Должен получить неопубликованный вебинар для сотрудника с разрешением на просмотр', async () => {
                const createdWebinar = await webinarResolver.createWebinar(
                    ADMIN_CONTEXT,
                    SCHOOL_UUID,
                    webinarCreateInputMandatory,
                );

                const webinar = await webinarResolver.getOneWebinar(EMPLOYEE_CONTEXT, SCHOOL_UUID, createdWebinar.id);
                expect(webinar).toEqual(notModifyWebinarRes);
            });

            it('Должен выбросить ошибку, если вебинар не найден', async () => {
                await expect(webinarResolver.getOneWebinar(ADMIN_CONTEXT, SCHOOL_UUID, FAKE_ID)).rejects.toThrow(
                    expect.objectContaining({
                        code: expect.stringContaining(ERROR_CODE.NOT_FOUND_ERROR),
                    }),
                );
            });
        });

        describe('Создание вебинара', () => {
            it('Должен создать вебинар если у сотрудника есть права создания', async () => {
                const webinar = await webinarResolver.createWebinar(
                    {
                        ...ADMIN_CONTEXT,
                        actions: [ACTION_WEBINAR_CREATE],
                    },
                    SCHOOL_UUID,
                    webinarCreateInputMandatory,
                );

                expect(webinar).toEqual(canModifyWebinarRes);
            });

            it('Должен запретить доступ на создание, если у сотрудника нет права создания', async () => {
                await expect(
                    webinarResolver.createWebinar(
                        {
                            ...ADMIN_CONTEXT,
                            actions: [ACTION_WEBINAR_VIEW, ACTION_WEBINAR_MODIFY, ACTION_WEBINAR_REMOVE],
                        },
                        SCHOOL_UUID,
                        webinarCreateInputMandatory,
                    ),
                ).rejects.toThrow();
            });

            // TODO: шпион не отслеживает вызов -> перенести в отдельный файл
            it.skip('Должен создать вебинар и уведомить статистику о созданных потоках', async () => {
                const webinar = await webinarResolver.createWebinar(
                    ADMIN_CONTEXT,
                    SCHOOL_UUID,
                    webinarCreateInputMandatory,
                );

                const webinarId = webinar.id;
                const streamId = webinar.streams[0].id;

                expect(notifyAboutCreatedStream).toHaveBeenCalledWith({
                    schoolId: SCHOOL_UUID,
                    webinar: { id: webinarId, name: 'Webinar Title' },
                    stream: { id: streamId, name: 'Stream Title' },
                });
            });
        });

        describe('Обновление вебинара', () => {
            it('Должен обновить ограничения по студентам', async () => {
                const created = await webinarResolver.createWebinar(
                    ADMIN_CONTEXT,
                    SCHOOL_UUID,
                    webinarCreateInputMandatory,
                );

                const webinar = await webinarResolver.updateWebinar(
                    ADMIN_CONTEXT,
                    SCHOOL_UUID,
                    created.id,
                    webinarUpdateStudentCapacityInput,
                );
                expect(webinar).toEqual({
                    ...canModifyWebinarRes,
                    ...webinarUpdateStudentCapacityInput,
                });
            });

            it('Должен обновить настройки уведомлений', async () => {
                const created = await webinarResolver.createWebinar(
                    ADMIN_CONTEXT,
                    SCHOOL_UUID,
                    webinarCreateInputMandatory,
                );

                const updateDto = {
                    studentNotifyFirst: 30 * 60 * 1000,
                    studentNotifySecond: 60 * 60 * 1000,
                };

                const webinar = await webinarResolver.updateWebinar(ADMIN_CONTEXT, SCHOOL_UUID, created.id, updateDto);
                expect(webinar).toEqual({
                    ...canModifyWebinarRes,
                    ...updateDto,
                });
            });

            it('Не должен обновлять вебинар, если послать пустой объект', async () => {
                const created = await webinarResolver.createWebinar(
                    ADMIN_CONTEXT,
                    SCHOOL_UUID,
                    webinarCreateInputMandatory,
                );

                const updateDto = {};

                const webinar = await webinarResolver.updateWebinar(ADMIN_CONTEXT, SCHOOL_UUID, created.id, updateDto);
                expect(webinar).toEqual(canModifyWebinarRes);
            });

            it('Должен обновить второе уведомление', async () => {
                const createdWebinar = await webinarResolver.createWebinar(ADMIN_CONTEXT, SCHOOL_UUID, {
                    ...webinarCreateInputMandatory,
                    studentNotifyFirst: 15 * 60 * 1000,
                    studentNotifySecond: undefined,
                });

                expect(createdWebinar).toMatchObject({
                    studentNotifyFirst: 15 * 60 * 1000,
                    studentNotifySecond: undefined,
                });

                const updateDto = {
                    studentNotifySecond: 60 * 60 * 1000,
                };

                const updatedWebinar = await webinarResolver.updateWebinar(
                    ADMIN_CONTEXT,
                    SCHOOL_UUID,
                    createdWebinar.id,
                    updateDto,
                );
                expect(updatedWebinar).toMatchObject({
                    studentNotifyFirst: 15 * 60 * 1000,
                    studentNotifySecond: 60 * 60 * 1000,
                });
            });

            it('Должен опубликовать вебинар ', async () => {
                const created = await webinarResolver.createWebinar(ADMIN_CONTEXT, SCHOOL_UUID, {
                    ...webinarCreateInputMandatory,
                    visibility: WebinarVisibilityPresentation.draft,
                });

                expect(created.visibility).toBe(WebinarVisibilityPresentation.draft);

                const updateDto = {
                    visibility: WebinarVisibilityPresentation.public,
                };

                const webinar = await webinarResolver.updateWebinar(ADMIN_CONTEXT, SCHOOL_UUID, created.id, updateDto);
                expect(webinar).toEqual({
                    ...canModifyWebinarRes,
                    ...updateDto,
                });
            });

            it('Должен опубликовать вебинар и оставить свойство требовать телефон при регистрации', async () => {
                const created = await webinarResolver.createWebinar(ADMIN_CONTEXT, SCHOOL_UUID, {
                    ...webinarCreateInputMandatory,
                    visibility: WebinarVisibilityPresentation.draft,
                    isPhoneRequiredOnRegistration: true,
                });

                expect(created.visibility).toBe(WebinarVisibilityPresentation.draft);
                expect(created.isPhoneRequiredOnRegistration).toBe(true);

                const updateDto = {
                    visibility: WebinarVisibilityPresentation.public,
                };

                const webinar = await webinarResolver.updateWebinar(ADMIN_CONTEXT, SCHOOL_UUID, created.id, updateDto);
                expect(webinar).toEqual({
                    ...canModifyWebinarRes,
                    ...updateDto,
                    isPhoneRequiredOnRegistration: true,
                    streams: [{ ...canModifyStreamRes, isPhoneRequiredOnRegistration: true }],
                });
            });

            it('Должен убрать вебинар из публикации', async () => {
                const created = await webinarResolver.createWebinar(ADMIN_CONTEXT, SCHOOL_UUID, {
                    ...webinarCreateInputMandatory,
                    visibility: WebinarVisibilityPresentation.public,
                });

                expect(created.visibility).toBe(WebinarVisibilityPresentation.public);

                const updateDto = {
                    visibility: WebinarVisibilityPresentation.draft,
                };

                const webinar = await webinarResolver.updateWebinar(ADMIN_CONTEXT, SCHOOL_UUID, created.id, updateDto);
                expect(webinar).toEqual({
                    ...canModifyWebinarRes,
                    ...updateDto,
                });
            });

            it('Должен выбросить ошибку при обновлении, если вебинар не найден', async () => {
                await expect(
                    webinarResolver.updateWebinar(
                        ADMIN_CONTEXT,
                        SCHOOL_UUID,
                        FAKE_ID,
                        webinarUpdateStudentCapacityInput,
                    ),
                ).rejects.toThrow();
            });

            it('Должен запретить доступ к изменению, если пользователь не имеет прав на редактирование', async () => {
                const created = await webinarResolver.createWebinar(
                    ADMIN_CONTEXT,
                    SCHOOL_UUID,
                    webinarCreateInputMandatory,
                );

                await expect(
                    webinarResolver.updateWebinar(
                        EMPLOYEE_CONTEXT,
                        SCHOOL_UUID,
                        created.id,
                        webinarUpdateStudentCapacityInput,
                    ),
                ).rejects.toThrow();
            });

            it('Должен запретить доступ к изменению, если пользователь имеет права, но он из другой школы', async () => {
                const created = await webinarResolver.createWebinar(
                    ADMIN_CONTEXT,
                    SCHOOL_UUID,
                    webinarCreateInputMandatory,
                );
                const webinarId = created.id;

                await expect(
                    webinarResolver.updateWebinar(
                        {
                            ...EMPLOYEE_CONTEXT,
                            actions: [ACTION_WEBINAR_MODIFY],
                        },
                        ANOTHER_SCHOOL_UUID,
                        webinarId,
                        webinarUpdateStudentCapacityInput,
                    ),
                ).rejects.toThrow();
            });

            it('Должен обновить вебинар, если пользователь не имеет прав на редактирование, но он его создал', async () => {
                const created = await webinarResolver.createWebinar(
                    ADMIN_CONTEXT,
                    SCHOOL_UUID,
                    webinarCreateInputMandatory,
                );

                const webinar = await webinarResolver.updateWebinar(
                    { ...ADMIN_CONTEXT, actions: [] },
                    SCHOOL_UUID,
                    created.id,
                    webinarUpdateStudentCapacityInput,
                );
                expect(webinar).toEqual({
                    ...canModifyWebinarRes,
                    ...webinarUpdateStudentCapacityInput,
                });
            });
        });

        describe('Удаление вебинара', () => {
            it('Должен удалить вебинар, связанные потоки и участников', async () => {
                const created = await webinarResolver.createWebinar(
                    ADMIN_CONTEXT,
                    SCHOOL_UUID,
                    webinarCreateInputMandatory,
                );

                const streamIds = created.streams.map((stream) => stream.id);
                const speakerUuids = created.streams.flatMap((stream) => stream.speakers);

                expect(streamIds.length).toBeGreaterThan(0);
                expect(speakerUuids.length).toBeGreaterThan(0);

                await webinarResolver.deleteWebinar(ADMIN_CONTEXT, SCHOOL_UUID, created.id);

                const webinar = await prisma.webinar.findUnique({
                    where: { id: created.id },
                });
                const streams = await prisma.stream.findMany({
                    where: { webinarId: created.id },
                });
                const speakers = await prisma.participant.findMany({
                    where: { streamId: { in: streamIds } },
                });

                expect(webinar).toBeNull();
                expect(streams.length).toBe(0);
                expect(speakers.length).toBe(0);
            });

            it('Должен запретить доступ на удаление, если пользователь не имеет прав на удаление или редактирование', async () => {
                const webinar = await webinarResolver.createWebinar(
                    ADMIN_CONTEXT,
                    SCHOOL_UUID,
                    webinarCreateInputMandatory,
                );

                await expect(
                    webinarResolver.deleteWebinar(
                        {
                            ...EMPLOYEE_CONTEXT,
                            actions: [ACTION_WEBINAR_CREATE, ACTION_WEBINAR_VIEW],
                        },
                        SCHOOL_UUID,
                        webinar.id,
                    ),
                ).rejects.toThrow();
            });

            it('Должен запретить доступ на удаление, если пользователь имеет права, но он из другой школы', async () => {
                const webinar = await webinarResolver.createWebinar(
                    ADMIN_CONTEXT,
                    SCHOOL_UUID,
                    webinarCreateInputMandatory,
                );

                await expect(
                    webinarResolver.deleteWebinar(
                        {
                            ...EMPLOYEE_CONTEXT,
                            actions: [ACTION_WEBINAR_MODIFY, ACTION_WEBINAR_REMOVE],
                        },
                        ANOTHER_SCHOOL_UUID,
                        webinar.id,
                    ),
                ).rejects.toThrow();
            });

            it('Должен удалить вебинар если пользователь не имеет прав на удаление, но он его создал', async () => {
                const created = await webinarResolver.createWebinar(
                    ADMIN_CONTEXT,
                    SCHOOL_UUID,
                    webinarCreateInputMandatory,
                );

                await webinarResolver.deleteWebinar({ ...ADMIN_CONTEXT, actions: [] }, SCHOOL_UUID, created.id);

                const webinar = await prisma.webinar.findUnique({
                    where: { id: created.id },
                });
                expect(webinar).toBeNull();
            });

            // TODO: не уведомляет об удаленных потоках?
            it.skip('Должен удалить вебинар и уведомить статистику об удаленных потоках и вебинарах', async () => {
                const created = await webinarResolver.createWebinar(
                    ADMIN_CONTEXT,
                    SCHOOL_UUID,
                    webinarCreateInputMandatory,
                );

                await webinarResolver.deleteWebinar(ADMIN_CONTEXT, SCHOOL_UUID, created.id);

                const removedStreamsIds = created.streams.map((stream) => stream.id);
                const removedWebinarsIds = [created.id];

                expect(notifyAboutRemovedWebinars).toHaveBeenCalledWith({
                    removedWebinarsIds,
                });
                expect(notifyAboutRemovedStreams).toHaveBeenCalledWith({
                    removedStreamsIds,
                });
            });
        });
    });

    describe('Каталог вебинаров', () => {
        beforeAll(async () => {
            const draftWebinar = await webinarResolver.createWebinar(ADMIN_CONTEXT, SCHOOL_UUID, {
                ...webinarCreateInputMandatory,
                title: 'Draft Webinar',
                streams: testStreamsInput1,
            });
            const privateWebinarSecond = await webinarResolver.createWebinar(ADMIN_CONTEXT, SCHOOL_UUID, {
                ...webinarCreateInputMandatory,
                title: 'Private Webinar Second',
                visibility: WebinarVisibilityPresentation.private,
                streams: testStreamsInput2,
            });
            const publicWebinarFirst = await webinarResolver.createWebinar(ADMIN_CONTEXT, SCHOOL_UUID, {
                ...webinarCreateInputMandatory,
                title: 'Public Webinar First',
                visibility: WebinarVisibilityPresentation.public,
                streams: testStreamsInput1,
            });
            const publicWebinarSecond = await webinarResolver.createWebinar(ADMIN_CONTEXT, SCHOOL_UUID, {
                ...webinarCreateInputMandatory,
                title: 'Public Webinar Second',
                visibility: WebinarVisibilityPresentation.public,
                streams: testStreamsInput2,
            });

            // add students
            const students: Prisma.ParticipantCreateManyInput[] = [];
            [...OTHER_STUDENTS, { email: STUDENT_CONTEXT.email, uuid: STUDENT_CONTEXT.id }].forEach(
                ({ uuid, email }) => {
                    [
                        ...draftWebinar.streams,
                        ...publicWebinarFirst.streams,
                        ...publicWebinarSecond.streams,
                        ...privateWebinarSecond.streams,
                    ].forEach((stream) => {
                        students.push({
                            streamId: stream.id,
                            userUuid: uuid,
                            email,
                            role: ParticipantRole.STUDENT,
                            isViewed: false,
                        });
                    });
                },
            );
            await prisma.participant.createMany({ data: students });
        }, 30000);

        it('Параметр пагинации должен быть опциональным', async () => {
            const cards = await webinarResolver.getWebinarCards(
                UNKNOWN_STUDENT_CONTEXT,
                SCHOOL_UUID,
                { mode: CardsViewMode.catalog, search: 'Second' },
                undefined,
                new Date('2024-10-15T12:30:00.000Z'),
            );
            expect(cards.data).toEqual([
                {
                    webinarId: expect.any(String),
                    title: 'Public Webinar Second',
                    description: 'Webinar Description',
                    coverUrl: null,
                    visibility: 'public',
                    isPhoneRequiredOnRegistration: false,
                    streamId: expect.any(String),
                    date: 1728993600000,
                    duration: 7200000,
                    hasRecord: false,
                    isRegistered: false,
                    isSpeaker: false,
                    isViewed: false,
                    isAllowedToJoin: false,
                    isLive: false,
                },
            ]);
        });

        it('Должен вернуть пустой массив, если поиск не дал результатов для студента', async () => {
            const cards = await webinarResolver.getWebinarCards(
                UNKNOWN_STUDENT_CONTEXT,
                SCHOOL_UUID,
                { mode: CardsViewMode.catalog, search: '543' },
                { skip: 0, take: 10 },
                new Date('2024-10-15T12:30:00.000Z'),
            );
            expect(cards.data).toEqual([]);
        });

        it('Должен вернуть пустой массив, если поиск не дал результатов для админа', async () => {
            const cards = await webinarResolver.getWebinarCards(
                EMPLOYEE_CONTEXT,
                SCHOOL_UUID,
                { mode: CardsViewMode.catalog, search: '543' },
                { skip: 0, take: 10 },
                new Date('2024-10-15T12:30:00.000Z'),
            );
            expect(cards.data).toEqual([]);
        });

        it('Должен получить карточки вебинаров для студента', async () => {
            const cards = await webinarResolver.getWebinarCards(
                UNKNOWN_STUDENT_CONTEXT,
                SCHOOL_UUID,
                { mode: CardsViewMode.catalog, search: 'Second' },
                { skip: 0, take: 10 },
                new Date('2024-10-15T12:30:00.000Z'),
            );
            expect(cards.data).toEqual([
                {
                    webinarId: expect.any(String),
                    title: 'Public Webinar Second',
                    description: 'Webinar Description',
                    coverUrl: null,
                    visibility: 'public',
                    isPhoneRequiredOnRegistration: false,
                    streamId: expect.any(String),
                    date: 1728993600000,
                    duration: 7200000,
                    hasRecord: false,
                    isRegistered: false,
                    isSpeaker: false,
                    isViewed: false,
                    isAllowedToJoin: false,
                    isLive: false,
                },
            ]);
        });

        it('Должен показать что вебинар имеет запись', async () => {
            const cards = await webinarResolver.getWebinarCards(
                UNKNOWN_STUDENT_CONTEXT,
                SCHOOL_UUID,
                { mode: CardsViewMode.catalog, search: 'Second' },
                { skip: 0, take: 10 },
                new Date('2024-10-15T12:30:00.000Z'),
            );

            const streamId = cards.data[0].streamId;

            await streamResolver.updateStream(ADMIN_CONTEXT, SCHOOL_UUID, streamId, {
                recordUrl: 'https://example.com/record.mp4',
                hasRecordUrl: true,
            });

            const cards3 = await webinarResolver.getWebinarCards(
                UNKNOWN_STUDENT_CONTEXT,
                SCHOOL_UUID,
                { mode: CardsViewMode.catalog, search: 'Second' },
                { skip: 0, take: 10 },
                new Date('2024-10-15T12:30:00.000Z'),
            );

            expect(cards3.data).toEqual([
                {
                    webinarId: expect.any(String),
                    title: 'Public Webinar Second',
                    description: 'Webinar Description',
                    coverUrl: null,
                    visibility: 'public',
                    isPhoneRequiredOnRegistration: false,
                    streamId: expect.any(String),
                    date: 1728993600000,
                    duration: 7200000,
                    hasRecord: true,
                    isRegistered: false,
                    isSpeaker: false,
                    isViewed: false,
                    isAllowedToJoin: false,
                    isLive: false,
                },
            ]);
        });

        afterAll(async () => {
            await Promise.all([
                prisma.stream.deleteMany({}),
                prisma.webinar.deleteMany({}),
                prisma.participant.deleteMany({}),
            ]);
        });

        it('Должен получить каталог вебинаров для спикера', async () => {
            const cards = await webinarResolver.getWebinarCards(
                SPEAKER_CONTEXT,
                SCHOOL_UUID,
                { mode: CardsViewMode.catalog },
                { skip: 0, take: 10 },
                new Date('2024-10-15T12:30:00.000Z'),
            );

            expect(cardsMapperForSpeaker(cards.data)).toEqual([
                // active
                {
                    title: 'Draft Webinar',
                    date: '2024-11-01T10:00:00.000Z',
                    duration: '2.0ч',
                    isSpeaker: true,
                },
                {
                    title: 'Public Webinar First',
                    date: '2024-11-01T10:00:00.000Z',
                    duration: '2.0ч',
                    isSpeaker: true,
                },
                // past
                {
                    title: 'Private Webinar Second',
                    date: '2024-10-15T12:00:00.000Z',
                    duration: '2.0ч',
                    isSpeaker: true,
                },
                {
                    title: 'Public Webinar Second',
                    date: '2024-10-15T12:00:00.000Z',
                    duration: '2.0ч',
                    isSpeaker: true,
                },
            ]);
        });

        it('Должен получить каталог вебинаров для студента', async () => {
            const cards = await webinarResolver.getWebinarCards(
                UNKNOWN_STUDENT_CONTEXT,
                SCHOOL_UUID,
                { mode: CardsViewMode.catalog },
                { skip: 0, take: 10 },
                new Date('2024-10-15T12:30:00.000Z'),
            );

            expect(cardsMapper(cards.data)).toEqual([
                // --> current date <--
                {
                    title: 'Public Webinar First',
                    date: '2024-11-01T10:00:00.000Z',
                    duration: '2.0ч',
                },
                {
                    title: 'Public Webinar Second',
                    date: '2024-10-15T12:00:00.000Z',
                    duration: '2.0ч',
                },
            ]);
        });

        it('Должен показывать каталог для гостя, если известно от какой он школы', async () => {
            const cards = await webinarResolver.getWebinarCards(
                GUEST_CONTEXT,
                SCHOOL_UUID,
                { mode: CardsViewMode.catalog },
                { skip: 0, take: 10 },
                new Date('2024-10-15T12:30:00.000Z'),
            );

            expect(cardsMapper(cards.data)).toEqual([
                // active
                {
                    title: 'Public Webinar First',
                    date: '2024-11-01T10:00:00.000Z',
                    duration: '2.0ч',
                },
                // past:
                {
                    title: 'Public Webinar Second',
                    date: '2024-10-15T12:00:00.000Z',
                    duration: '2.0ч',
                },
            ]);
        });

        it('Не должен показывать каталог для гостя, если не определена школа от которой он пришел', async () => {
            const SCHOOL_UUID_UNDEFINED = undefined as unknown as string;
            await expect(
                webinarResolver.getWebinarCards(
                    GUEST_CONTEXT,
                    SCHOOL_UUID_UNDEFINED,
                    { mode: CardsViewMode.catalog },
                    { skip: 0, take: 10 },
                    new Date('2024-10-15T12:30:00.000Z'),
                ),
            ).rejects.toThrow();
        });

        it('Должен получить каталог вебинаров для сотрудника, у которого есть право просмотра не опубликованных вебинаров', async () => {
            const cards = await webinarResolver.getWebinarCards(
                EMPLOYEE_CONTEXT,
                SCHOOL_UUID,
                { mode: CardsViewMode.catalog },
                { skip: 0, take: 10 },
                new Date('2024-10-15T12:30:00.000Z'),
            );

            expect(cardsMapper(cards.data)).toEqual([
                // --> current date <--
                {
                    title: 'Draft Webinar',
                    date: '2024-11-01T10:00:00.000Z',
                    duration: '2.0ч',
                },
                {
                    title: 'Public Webinar First',
                    date: '2024-11-01T10:00:00.000Z',
                    duration: '2.0ч',
                },
                {
                    title: 'Private Webinar Second',
                    date: '2024-10-15T12:00:00.000Z',
                    duration: '2.0ч',
                },
                {
                    title: 'Public Webinar Second',
                    date: '2024-10-15T12:00:00.000Z',
                    duration: '2.0ч',
                },
            ]);
        });

        it('Должен включить в каталог для спикера транслируемый вебинар', async () => {
            const cards = await webinarResolver.getWebinarCards(
                SPEAKER_CONTEXT,
                SCHOOL_UUID,
                {
                    mode: CardsViewMode.catalog,
                    search: 'Public Webinar Second',
                },
                { skip: 0, take: 10 },
                new Date('2024-10-15T12:30:00.000Z'),
            );

            expect(cardsMapper(cards.data)).toEqual([
                {
                    title: 'Public Webinar Second',
                    date: '2024-10-15T12:00:00.000Z',
                    duration: '2.0ч',
                },
            ]);
        });

        it('Должен отобрать в каталог вебинаров для студента только опубликованные вебинары', async () => {
            const cards = await webinarResolver.getWebinarCards(
                STUDENT_CONTEXT,
                SCHOOL_UUID,
                { mode: CardsViewMode.catalog },
                { skip: 0, take: 10 },
                new Date('2024-10-15T12:30:00.000Z'),
            );

            expect(cardsMapper(cards.data)).toEqual([
                // {
                //     title: 'Private Webinar Second',
                //     date: '2024-10-15T12:00:00.000Z',
                //     duration: '2.0ч',
                // },
                {
                    title: 'Public Webinar First',
                    date: '2024-11-01T10:00:00.000Z',
                    duration: '2.0ч',
                },
                {
                    title: 'Public Webinar Second',
                    date: '2024-10-15T12:00:00.000Z',
                    duration: '2.0ч',
                },
            ]);
        });

        it('Должен включить в каталог для спикера последние прошедшие вебинары', async () => {
            const cards = await webinarResolver.getWebinarCards(
                SPEAKER_CONTEXT,
                SCHOOL_UUID,
                { mode: CardsViewMode.catalog },
                { skip: 0, take: 10 },
                new Date('2025-11-20T12:00:00.000Z'),
            );

            expect(cardsMapper(cards.data)).toEqual([
                // --> current date <--
                {
                    title: 'Draft Webinar',
                    date: '2024-12-01T10:00:00.000Z',
                    duration: '2.0ч',
                },
                {
                    title: 'Public Webinar First',
                    date: '2024-12-01T10:00:00.000Z',
                    duration: '2.0ч',
                },
                {
                    title: 'Private Webinar Second',
                    date: '2024-10-15T12:00:00.000Z',
                    duration: '2.0ч',
                },
                {
                    title: 'Public Webinar Second',
                    date: '2024-10-15T12:00:00.000Z',
                    duration: '2.0ч',
                },
            ]);
        });

        it('Должен включить в список спикера ближайшие будущие вебинары, в том числе не публичные, где пользователь является спикером', async () => {
            const cards = await webinarResolver.getWebinarCards(
                SPEAKER_CONTEXT,
                SCHOOL_UUID,
                { mode: CardsViewMode.catalog },
                { skip: 0, take: 10 },
                new Date('2024-01-20T12:00:00.000Z'),
            );

            expect(cardsMapperForSpeaker(cards.data)).toEqual([
                {
                    title: 'Private Webinar Second',
                    date: '2024-09-15T12:00:00.000Z',
                    duration: '2.0ч',
                    isSpeaker: true,
                },
                {
                    title: 'Public Webinar Second',
                    date: '2024-09-15T12:00:00.000Z',
                    duration: '2.0ч',
                    isSpeaker: true,
                },
                {
                    title: 'Draft Webinar',
                    date: '2024-09-01T10:00:00.000Z',
                    duration: '2.0ч',
                    isSpeaker: true,
                },
                {
                    title: 'Public Webinar First',
                    date: '2024-09-01T10:00:00.000Z',
                    duration: '2.0ч',
                    isSpeaker: true,
                },
                // --> current date <--
            ]);
        });

        it('Должен использовать поиск', async () => {
            const cards = await webinarResolver.getWebinarCards(
                ADMIN_CONTEXT,
                SCHOOL_UUID,
                { mode: CardsViewMode.catalog, search: 'private' },
                { skip: 0, take: 10 },
                new Date('2024-01-20T12:00:00.000Z'),
            );

            expect(cardsMapper(cards.data)).toEqual([
                {
                    title: 'Private Webinar Second',
                    date: '2024-09-15T12:00:00.000Z',
                    duration: '2.0ч',
                },
            ]);
        });

        it('Должен оставить в списке для спикера только активные вебинары', async () => {
            const cards = await webinarResolver.getWebinarCards(
                SPEAKER_CONTEXT,
                SCHOOL_UUID,
                {
                    mode: CardsViewMode.catalog,
                    isActive: true,
                },
                { skip: 0, take: 10 },
                new Date('2024-11-20T12:00:00.000Z'),
            );

            expect(cardsMapper(cards.data)).toEqual([
                {
                    title: 'Draft Webinar',
                    date: '2024-12-01T10:00:00.000Z',
                    duration: '2.0ч',
                },
                {
                    title: 'Public Webinar First',
                    date: '2024-12-01T10:00:00.000Z',
                    duration: '2.0ч',
                },
            ]);
        });

        it('Должен оставить в списке для спикера только прошедшие вебинары', async () => {
            const cards = await webinarResolver.getWebinarCards(
                SPEAKER_CONTEXT,
                SCHOOL_UUID,
                {
                    mode: CardsViewMode.catalog,
                    isActive: false,
                    // onlyAssigned: true,
                },
                { skip: 0, take: 10 },
                new Date('2024-11-20T12:00:00.000Z'),
            );

            expect(cardsMapper(cards.data)).toEqual([
                {
                    title: 'Private Webinar Second',
                    date: '2024-10-15T12:00:00.000Z',
                    duration: '2.0ч',
                },
                {
                    title: 'Public Webinar Second',
                    date: '2024-10-15T12:00:00.000Z',
                    duration: '2.0ч',
                },
            ]);
        });

        it('Должен показать отсортированные будущие вебинары, где пользователь участвует как спикер', async () => {
            const cards = await webinarResolver.getWebinarCards(
                SPEAKER_CONTEXT,
                SCHOOL_UUID,
                {
                    mode: CardsViewMode.catalog,
                    onlyAssigned: true,
                    isActive: true,
                },
                { skip: 0, take: 10 },
                new Date('2024-10-15T10:00:00.000Z'),
            );
            expect(cardsMapper(cards.data)).toEqual([
                {
                    title: 'Private Webinar Second',
                    date: '2024-10-15T12:00:00.000Z',
                    duration: '2.0ч',
                },
                {
                    title: 'Public Webinar Second',
                    date: '2024-10-15T12:00:00.000Z',
                    duration: '2.0ч',
                },
                {
                    title: 'Draft Webinar',
                    date: '2024-11-01T10:00:00.000Z',
                    duration: '2.0ч',
                },
                {
                    title: 'Public Webinar First',
                    date: '2024-11-01T10:00:00.000Z',
                    duration: '2.0ч',
                },
                // --> current date <--
            ]);
        });

        it('Должен показать отсортированные прошлые вебинары, где пользователь участвует как спикер', async () => {
            const cards = await webinarResolver.getWebinarCards(
                SPEAKER_CONTEXT,
                SCHOOL_UUID,
                {
                    mode: CardsViewMode.catalog,
                    onlyAssigned: true,
                    isActive: false,
                },
                { skip: 0, take: 10 },
                new Date('2024-12-15T10:00:00.000Z'),
            );

            expect(cardsMapper(cards.data)).toEqual([
                // --> current date <--
                {
                    title: 'Draft Webinar',
                    date: '2024-12-01T10:00:00.000Z',
                    duration: '2.0ч',
                },
                {
                    title: 'Public Webinar First',
                    date: '2024-12-01T10:00:00.000Z',
                    duration: '2.0ч',
                },
                {
                    title: 'Private Webinar Second',
                    date: '2024-10-15T12:00:00.000Z',
                    duration: '2.0ч',
                },
                {
                    title: 'Public Webinar Second',
                    date: '2024-10-15T12:00:00.000Z',
                    duration: '2.0ч',
                },
            ]);
        });
    });

    describe.skip('Библиотека студента', () => {
        beforeAll(async () => {
            // const draftWebinar = await webinarResolver.createWebinar(adminContext, {
            //     ...webinarCreateInputMandatory,
            //     title: 'Draft Webinar',
            //     streams: testStreamsInput1,
            // });
            // const privateWebinar = await webinarResolver.createWebinar(adminContext, {
            //     ...webinarCreateInputMandatory,
            //     title: 'Private Webinar One',
            //     visibility: WebinarVisibilityPresentation.private,
            //     streams: testStreamsInput1,
            // });
            // const publicWebinarFirst = await webinarResolver.createWebinar(adminContext, {
            //     ...webinarCreateInputMandatory,
            //     title: 'Public Webinar First',
            //     visibility: WebinarVisibilityPresentation.public,
            //     streams: testStreamsInput1,
            // });
            const publicWebinarSecond = await webinarResolver.createWebinar(ADMIN_CONTEXT, SCHOOL_UUID, {
                ...webinarCreateInputMandatory,
                title: 'Public Webinar Second',
                visibility: WebinarVisibilityPresentation.public,
                streams: testStreamsInput1,
            });

            // add students
            const students = [];
            [...OTHER_STUDENT_UUIDS, STUDENT_UUID].forEach((uuid) => {
                [
                    // ...draftWebinar.streams,
                    // ...publicWebinarFirst.streams,
                    ...publicWebinarSecond.streams,
                    // ...privateWebinar.streams,
                ].forEach((stream) => {
                    students.push({
                        streamId: stream.id,
                        userUuid: uuid,
                        role: ParticipantRole.STUDENT,
                    });
                });
            });
            await prisma.participant.createMany({ data: students });
        }, 30000);

        afterAll(async () => {
            await Promise.all([
                prisma.stream.deleteMany({}),
                prisma.webinar.deleteMany({}),
                prisma.participant.deleteMany({}),
            ]);
        });

        it.only('Должен получить список вебинаров', async () => {
            const cards = await webinarResolver.getWebinarCards(
                STUDENT_CONTEXT,
                SCHOOL_UUID,
                { mode: CardsViewMode.library },
                undefined,
                new Date('2024-10-15T12:30:00.000Z'),
            );
            console.log(cardsMapper(cards.data));
            // expect(cardsMapper(cards.data)).toEqual([]);
        });
    });
});
