import { parse } from 'node:path';
import { Injectable } from '@nestjs/common';
import slugify from 'slugify';

import { IFileUpload } from '../../../src/shared/gql-file-upload-convert';
import { IFileStorageAdapter } from '../../../src/webinars/application/adapters/saas/file-storage-adapter.interface';
import { MOCK_STORAGE_BUCKET, MOCK_STORAGE_ENDPOINT } from '../__data__/test-constants';

@Injectable()
export class StorageServiceMock implements IFileStorageAdapter {
    private readonly bucket = MOCK_STORAGE_BUCKET;

    async getSignedUrl(key: string, expiresIn?: number): Promise<string> {
        return `${MOCK_STORAGE_ENDPOINT}/${this.bucket}/${key}?mockSignedUrl=true&expiresIn=${expiresIn || 3600}`;
    }

    async deleteFile(key: string): Promise<void> {
        return;
    }

    async deleteFiles(listOfKeys: string[]): Promise<void> {
        return;
    }

    async getFileList(prefix: string): Promise<string[]> {
        return [`${prefix}/mockFile1.txt`, `${prefix}/mockFile2.txt`];
    }

    async uploadFile(params: { Key: string; ContentType: string; Body: Buffer }): Promise<void> {
        // Симуляция загрузки файла
        return;
    }

    async uploadWebinarCover(params: {
        webinarId: string;
        cover: IFileUpload;
    }): Promise<{ key: string; name: string }> {
        const { buffer, filename, mimetype } = params.cover;
        const { name, ext } = parse(filename);

        const key = this.getWebinarCoverKey({
            webinarId: params.webinarId,
            name,
            ext,
        });
        await this.uploadFile({
            Key: key,
            ContentType: mimetype,
            Body: buffer,
        });

        return { key, name };
    }

    async uploadStreamCover(params: {
        webinarId: string;
        streamId: string;
        cover: IFileUpload;
    }): Promise<{ key: string; name: string }> {
        const { buffer, filename, mimetype } = params.cover;
        const { name, ext } = parse(filename);

        const key = this.getStreamCoverKey({
            webinarId: params.webinarId,
            streamId: params.streamId,
            name,
            ext,
        });
        await this.uploadFile({
            Key: key,
            ContentType: mimetype,
            Body: buffer,
        });

        return { key, name };
    }

    getUrl(key: string): string {
        return `${MOCK_STORAGE_ENDPOINT}/${this.bucket}/${key}`;
    }

    async removeWebinarCovers(webinarId: string): Promise<void> {
        const prefix = `/webinar/${webinarId}/cover/`;
        const coverKeys = await this.getFileList(prefix);
        await this.deleteFiles(coverKeys);
    }

    async removeStreamCovers(params: { webinarId: string; streamId: string }): Promise<void> {
        const prefix = `/webinar/${params.webinarId}/stream/${params.streamId}/cover/`;
        const coverKeys = await this.getFileList(prefix);
        await this.deleteFiles(coverKeys);
    }

    async removeAllWebinarFiles(webinarId: string): Promise<void> {
        const prefix = `/webinar/${webinarId}/`;
        const fileKeys = await this.getFileList(prefix);
        await this.deleteFiles(fileKeys);
    }

    private getWebinarCoverKey({ webinarId, name, ext }): string {
        return `/webinar/${webinarId}/cover/${this.slugifyFileName(name, ext)}`;
    }

    private getStreamCoverKey({ webinarId, streamId, name, ext }): string {
        return `/webinar/${webinarId}/stream/${streamId}/cover/${this.slugifyFileName(name, ext)}`;
    }

    private slugifyFileName(name: string, ext: string): string {
        const sluggedName = slugify(name, {
            replacement: '_',
            lower: true,
            strict: true,
            locale: 'en',
            trim: true,
        });
        return sluggedName + ext;
    }
}
