import { setTimeout } from 'node:timers/promises';

import {
    IEditorServiceAdapter,
    Page,
    PageOwner,
    RemoveManyPagesResult,
    RemovePageResult,
} from '../../../src/webinars/application/adapters/microservices/editor-service-adapter.interface';

export class EditorServiceAdapterMock implements IEditorServiceAdapter {
    public async createPage(owner: PageOwner): Promise<Page> {
        await setTimeout();
        return { pageId: '123', ...owner };
    }

    public async removePage(pageId: string): Promise<RemovePageResult> {
        await setTimeout();
        void pageId;
        return { success: true };
    }

    public async createManyPages(owners: PageOwner[]): Promise<Page[]> {
        await setTimeout();
        return owners.map((owner) => ({ pageId: '123', ...owner }));
    }

    public async removeManyPages(pageIds: string[]): Promise<RemoveManyPagesResult> {
        await setTimeout();
        return {
            success: true,
            removedPageIds: pageIds,
            errors: [],
        };
    }
}
