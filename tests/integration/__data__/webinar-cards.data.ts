import { StreamCreateInput } from '../../../src/webinars/presentation/input/stream-create.input';
import { ALL_SPEAKER_UUIDS, OTHER_SPEAKER_UUIDS, streamCreateInput } from './webinar.data';

export const testStreamsInput1: StreamCreateInput[] = [
    {
        // Stream_4
        date: new Date('2024-12-01T10:00:00.000Z').valueOf(),
    },
    {
        // Stream_3,
        date: new Date('2024-11-01T10:00:00.000Z').valueOf(),
    },
    {
        // Stream_2
        date: new Date('2024-10-01T10:00:00.000Z').valueOf(),
        speakers: OTHER_SPEAKER_UUIDS,
    },
    {
        // Stream_1
        date: new Date('2024-09-01T10:00:00.000Z').valueOf(),
    },
]
    .sort((a, b) => a.date - b.date)
    .map((stream, index) => ({
        ...streamCreateInput,
        title: streamCreateInput.title + ' ' + index,
        ...stream,
    }));

export const testStreamsInput2: StreamCreateInput[] = [
    {
        // Stream_2
        date: new Date('2024-10-15T12:00:00.000Z').valueOf(),
        speakers: ALL_SPEAKER_UUIDS,
    },
    {
        // Stream_1
        date: new Date('2024-09-15T12:00:00.000Z').valueOf(),
    },
]
    .sort((a, b) => a.date - b.date)
    .map((stream, index) => ({
        ...streamCreateInput,
        title: streamCreateInput.title + ' ' + index,
        ...stream,
    }));
