import { UserContext } from '@skillspace/access';

import {
    RegistrationTypePresentation,
    StreamTypePresentation,
} from '../../../src/webinars/presentation/enum.presentation';
import { StreamCreateInput } from '../../../src/webinars/presentation/input/stream-create.input';
import { WebinarExternalCreateInput } from '../../../src/webinars/presentation/input/webinar-create.input';
import {
    ACTION_WEBINAR_CREATE,
    ACTION_WEBINAR_MODIFY,
    ACTION_WEBINAR_REMOVE,
    ACTION_WEBINAR_VIEW,
    DATE_NOW,
    TWO_HOURS,
} from './test-constants';

export const SCHOOL_UUID = '2ba88031-d461-4ac0-b1d5-62ead203ea76';
export const ANOTHER_SCHOOL_UUID = '2ba88032-d461-4ac0-b1d5-62ead203ea77';

export const ADMIN_UUID = '66701666-406f-4adb-934d-19bf96a3be80';
export const SPEAKER_UUID = '66701666-406f-4adb-934d-19bf96a3be81';
export const STUDENT_UUID = '66701666-406f-4adb-934d-19bf96a3be82';
export const EMPLOYEE_UUID = '66701666-406f-4adb-934d-19bf96a3be83';

export const ADMIN_CONTEXT = {
    id: ADMIN_UUID,
    email: '<EMAIL>',
    name: 'Admin',
    unionAuthKey: 'admin-union-auth-key',
    role: 'ROLE_EMPLOYEE' as const,
    actions: [ACTION_WEBINAR_CREATE, ACTION_WEBINAR_MODIFY, ACTION_WEBINAR_VIEW, ACTION_WEBINAR_REMOVE],
};

export const EMPLOYEE_CONTEXT = {
    id: EMPLOYEE_UUID,
    email: '<EMAIL>',
    name: 'Admin',
    unionAuthKey: 'admin-union-auth-key',
    role: 'ROLE_EMPLOYEE' as const,
    actions: [ACTION_WEBINAR_VIEW],
};

export const SPEAKER_CONTEXT = {
    id: SPEAKER_UUID,
    email: '<EMAIL>',
    name: 'Speaker',
    unionAuthKey: 'speaker-union-auth-key',
    role: 'ROLE_EMPLOYEE' as const,
    actions: [],
};

export const GUEST_CONTEXT = {
    id: undefined,
    email: undefined,
    name: undefined,
    unionAuthKey: undefined,
    role: undefined,
    actions: undefined,
} as unknown as UserContext;

export const WITHOUT_ACTIONS_CONTEXT: UserContext = {
    id: SPEAKER_UUID,
    email: '<EMAIL>',
    name: 'Speaker',
    unionAuthKey: 'speaker-union-auth-key',
    role: 'ROLE_EMPLOYEE' as const,
} as unknown as UserContext;

export const STUDENT_CONTEXT = {
    id: STUDENT_UUID,
    email: '<EMAIL>',
    name: 'Student',
    unionAuthKey: 'student-union-auth-key',
    role: 'ROLE_STUDENT' as const,
    actions: [],
};

export const UNKNOWN_STUDENT_CONTEXT = {
    id: '66701636-406f-4adb-933d-19bf96a3be82',
    email: '<EMAIL>',
    name: 'Student',
    unionAuthKey: 'student-union-auth-key',
    role: 'ROLE_STUDENT' as const,
    actions: [],
};

export const ALL_SPEAKER_UUIDS = [
    SPEAKER_UUID,
    '1b962ed1-ade4-48f9-a4bf-61757b617c44',
    'dbb1a050-5724-41ca-9d75-575d753fb29c',
];
export const OTHER_SPEAKER_UUIDS = ['1b962ed1-ade4-48f9-a4bf-61757b617c44', 'dbb1a050-5724-41ca-9d75-575d753fb29c'];

export const OTHER_STUDENT_UUIDS = ['1b962ed1-ade4-48f9-a4bf-61757b617c45', 'dbb1a050-5724-41ca-9d75-575d753fb39c'];
export const OTHER_STUDENTS = [
    {
        uuid: '1b962ed1-ade4-48f9-a4bf-61757b617c45',
        email: '<EMAIL>',
    },
    {
        uuid: 'dbb1a050-5724-41ca-9d75-575d753fb39c',
        email: '<EMAIL>',
    },
];

export const streamCreateInput: StreamCreateInput = {
    title: 'Stream Title',
    date: DATE_NOW,
    duration: TWO_HOURS,
    streamType: StreamTypePresentation.external,
    externalStreamUrl: 'https://example.com/stream',
    speakers: ALL_SPEAKER_UUIDS,
};

export const streamDefaultValues = {
    coverUrl: undefined,
    recordUrl: undefined,
};

export const expectedStreamResult = {
    title: 'Stream Title',
    date: DATE_NOW,
    duration: TWO_HOURS,
    isPhoneRequiredOnRegistration: false,
    streamType: StreamTypePresentation.external,
    externalStreamUrl: 'https://example.com/stream',
    speakers: ALL_SPEAKER_UUIDS,
    id: expect.any(String),
    countRegistered: 0,
    countViewed: 0,
    coverUrl: null,
    pages: ['123'],
    // record
    hasRecordUrl: false,
    hasRecord: false,
    recordUrl: undefined,
    autoRecord: false,
    savedRecordUrl: undefined,
    // webinar
    webinarId: expect.any(String),
    webinarDescription: 'Webinar Description',
    webinarTitle: 'Webinar Title',
    integrationCode: null,
    registrationType: 'byLanding',
};

export const webinarCreateInputMandatory: WebinarExternalCreateInput = {
    title: 'Webinar Title',
    description: 'Webinar Description',
    // cover: COVER_FILE_UPLOAD,
    registrationType: RegistrationTypePresentation.byLanding,
    registrationForEntry: false,
    registrationForView: true,
    useDefaultLanding: true,
    streams: [streamCreateInput],
};

export const webinarDefaultValues = {
    type: 'external',
    useAttendancePoints: false,
    attendancePoints: 0,
    useNotifications: true,
    notifications: [900000],
    visibility: 'draft',
    enableStudentLimit: false,
    maxStudentCapacity: 0,
    useDefaultLanding: true,
    externalLandingUrl: '',
    integrationCode: '',
};

export const canModifyStreamRes = {
    ...expectedStreamResult,
    isRegistered: false,
    isSpeaker: false,
    canModify: true,
    canRemove: true,
    isAllowedToJoin: false,
    isLive: false,
};

export const canModifyWebinarRes = {
    // значения по умолчанию
    type: 'external',
    useAttendancePoints: false,
    attendancePoints: 0,
    useNotifications: true,
    studentNotifyFirst: 900000,
    studentNotifySecond: undefined,
    visibility: 'draft',
    isPhoneRequiredOnRegistration: false,
    enableStudentLimit: false,
    maxStudentCapacity: 0,
    externalLandingUrl: '',
    integrationCode: '',
    title: 'Webinar Title',
    description: 'Webinar Description',
    // coverUrl: MOCK_WEBINAR_COVER_URL, TODO: проверить мок
    coverUrl: null,
    registrationType: RegistrationTypePresentation.byLanding,
    registrationForEntry: false,
    registrationForView: true,
    useDefaultLanding: true,
    id: expect.any(String),
    schoolUuid: SCHOOL_UUID,
    createdBy: ADMIN_UUID,
    streams: [canModifyStreamRes],
    countRegistered: 0,
    countViewed: 0,
    canModify: true,
    canRemove: true,
};

export const notModifyStreamRes = {
    ...expectedStreamResult,
    isRegistered: false,
    isSpeaker: false,
    canModify: false,
    canRemove: false,
    isAllowedToJoin: false,
    isLive: false,
};

export const notModifyWebinarRes = {
    ...canModifyWebinarRes,
    streams: [notModifyStreamRes],
    canModify: false,
    canRemove: false,
};
