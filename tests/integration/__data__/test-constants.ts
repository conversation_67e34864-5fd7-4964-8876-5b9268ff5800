import { S3Config } from '../../../src/webinars/infrastructure/adapters/saas/s3-storage/s3.config';

export const FAKE_ID = '672c5bb28b1216c97974ff34';
export const DATE_NOW = new Date('2020-01-01T13:00:00.000Z').getTime();
export const TWO_HOURS = 2 * 60 * 60 * 1000;

export const ACTION_WEBINAR_VIEW = 'ACTION_WEBINAR_VIEW';
export const ACTION_WEBINAR_CREATE = 'ACTION_WEBINAR_CREATE';
export const ACTION_WEBINAR_REMOVE = 'ACTION_WEBINAR_REMOVE';
export const ACTION_WEBINAR_MODIFY = 'ACTION_WEBINAR_MODIFY';

export const MOCK_COVER_FILE_NAME = 'mockFile.jpg';
export const MOCK_STORAGE_BUCKET = S3Config.bucket;
export const MOCK_STORAGE_ENDPOINT = S3Config.endpoint;
export const MOCK_WEBINAR_COVER_URL = `${S3Config.endpoint}/${S3Config.bucket}/webinars/${MOCK_COVER_FILE_NAME}`;

export const CAPTCHA = 'dD0xNzM0NTA4MTU0O2k9NzguMTc3LjE3NS4yMTE7RD0yMDkxOENDQTBCNU';
