import { Readable } from 'node:stream';

import { IFileUpload } from '../../../src/shared/gql-file-upload-convert';
import { MOCK_COVER_FILE_NAME } from './test-constants';

export const COVER_FILE_UPLOAD = {
    promise: {
        filename: MOCK_COVER_FILE_NAME,
        mimetype: 'image/jpeg',
        createReadStream: () => {
            const stream = new Readable({
                read() {
                    this.push(Buffer.alloc(1024 * 300)); // Отправляем 300 KB данных
                    this.push(null); // Сообщаем об окончании потока
                },
            });
            return stream;
        },
    },
} as unknown as IFileUpload;
