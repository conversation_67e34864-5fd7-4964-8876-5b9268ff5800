import { setTimeout } from 'node:timers/promises';

import { KinescopeClient } from '../../../src/webinars/infrastructure/adapters/saas/kinescope/client/kinescope.client';

const VIDEO_URL = 'https://www.youtube.com/shorts/w7e4jqHdQKA';

describe.skip('Kinescope Integration Tests', () => {
    let kinescope: KinescopeClient;

    let tempProjectId: string | undefined;
    let tempFolderId: string | undefined;
    let tempVideoId: string | undefined;

    beforeAll(() => {
        kinescope = new KinescopeClient();
    });

    describe('Projects', () => {
        afterAll(async () => {
            if (tempProjectId) {
                await kinescope.deleteProject(tempProjectId);
                tempProjectId = undefined;
            }
        });

        it('Должен создать проект', async () => {
            const projectName = 'TempTestProject1';
            const createdProject = await kinescope.createProject(projectName);
            tempProjectId = createdProject.id;

            expect(createdProject).toHaveProperty('id');
            expect(typeof createdProject.id).toBe('string');

            console.log({ createdProjectId: createdProject.id });

            const fetchedProject = await kinescope.getProject(tempProjectId);
            expect(fetchedProject.name).toBe(projectName);
        });

        it('Должен удалить проект', async () => {
            const projectName = 'TempTestProjectForDeletion';
            const createdProject = await kinescope.createProject(projectName);
            const projectId = createdProject.id;

            await kinescope.getProject(projectId);
            await kinescope.deleteProject(projectId);

            const removed = await kinescope.getProject(projectId);
            expect(removed).toBeNull();
        });
    });

    describe('Folders', () => {
        beforeAll(async () => {
            const projectName = 'TempTestProjectForFolders';
            const createdProject = await kinescope.createProject(projectName);
            tempProjectId = createdProject.id;
        });

        afterAll(async () => {
            if (tempProjectId) {
                await kinescope.deleteProject(tempProjectId);
                tempProjectId = undefined;
            }
        });

        it('Должен создать папку внутри проекта', async () => {
            const folderName = 'TempTestFolder';
            const createdFolder = await kinescope.createFolder(tempProjectId, folderName);
            tempFolderId = createdFolder.id;

            expect(createdFolder).toHaveProperty('id');
            expect(typeof createdFolder.id).toBe('string');

            const fetchedFolder = await kinescope.getFolder(tempProjectId, tempFolderId);
            expect(fetchedFolder.name).toBe(folderName);
        });

        it('Должен удалить папку', async () => {
            const folderName = 'TempTestFolderForDeletion';
            const createdFolder = await kinescope.createFolder(tempProjectId, folderName);

            const folder = await kinescope.getFolder(tempProjectId, createdFolder.id);
            expect(folder.name).toBe(folderName);

            await kinescope.deleteFolder(tempProjectId, createdFolder.id);

            const removed = await kinescope.getFolder(tempProjectId, createdFolder.id);
            expect(removed).toBeNull();
        });
    });

    describe('Videos', () => {
        beforeAll(async () => {
            const projectName = 'TempTestProjectForVideos';
            const createdProject = await kinescope.createProject(projectName);
            tempProjectId = createdProject.id;

            const folderName = 'TempTestFolderForVideos';
            const createdFolder = await kinescope.createFolder(tempProjectId, folderName);
            tempFolderId = createdFolder.id;
        });

        afterAll(async () => {
            if (tempProjectId) {
                await kinescope.deleteProject(tempProjectId);
                tempProjectId = undefined;
            }
        });

        afterEach(async () => {
            if (tempVideoId) {
                await kinescope.deleteVideo(tempVideoId);
                tempVideoId = undefined;
            }
        });

        it('Должен начать загрузку видео по URL', async () => {
            const title = 'Test Video Title';
            const description = 'Test Video Description';
            await kinescope.uploadVideoByUrl(tempFolderId, { videoUrl: VIDEO_URL, title, description });

            const videosInFolder = await kinescope.getVideos(tempProjectId, { folderId: tempFolderId });

            tempVideoId = videosInFolder[0].id;

            expect(videosInFolder.length).toBe(1);

            await setTimeout(15000);
            const video = await kinescope.getVideo(tempVideoId);

            expect(video).toMatchObject({
                id: videosInFolder[0].id,
                title: expect.any(String),
                description: expect.any(String),
            });
        }, 20000);

        it('Должен удалить видео', async () => {
            const title = 'Test Video Title For Deletion';
            const description = 'Test Video Description For Deletion';

            await kinescope.uploadVideoByUrl(tempFolderId, { videoUrl: VIDEO_URL, title, description });

            const videosInFolder = await kinescope.getVideos(tempProjectId, { folderId: tempFolderId });
            expect(videosInFolder.length).toBe(1);

            await kinescope.deleteVideo(videosInFolder[0].id);

            const updatedVideosInFolder = await kinescope.getVideos(tempProjectId, { folderId: tempFolderId });
            expect(updatedVideosInFolder.length).toBe(0);
        });
    });
});
