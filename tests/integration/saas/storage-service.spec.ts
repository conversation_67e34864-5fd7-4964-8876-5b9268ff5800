import { INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';

import { AppModule } from '../../../src/app.module';

describe.skip('Storage Integration test', () => {
    let app: INestApplication;
    // let storageService: IStorageService;

    beforeAll(async () => {
        const builder = Test.createTestingModule({
            imports: [AppModule],
        });
        const build = await builder.compile();
        const testApp = build.createNestApplication();
        app = await testApp.init();

        // storageService = app.get<IStorageService>(STORAGE_SERVICE);
    });

    afterAll(async () => {
        await app.close();
    });

    it.todo('Should upload and delete one file');
});
