import { setTimeout } from 'node:timers/promises';

import {
    DEFAULT_CREATE_ROOM_ARGS,
    LiveDigitalClient,
} from '../../../src/webinars/infrastructure/adapters/saas/live-digital/client/live-digital.client';
import {
    DEV_LIVE_DIGITAL_SPACE_ID,
    LIVE_DIGITAL_SPACE_IDS,
    liveDigitalConfig,
    PROD_LIVE_DIGITAL_SPACE_ID,
} from '../../../src/webinars/infrastructure/adapters/saas/live-digital/live-digital.config';
import {
    StreamTypePresentation,
    WebinarVisibilityPresentation,
} from '../../../src/webinars/presentation/enum.presentation';
import { CAPTCHA } from '../__data__/test-constants';
import {
    ADMIN_CONTEXT,
    GUEST_CONTEXT,
    SCHOOL_UUID,
    streamCreateInput,
    UNKNOWN_STUDENT_CONTEXT,
    webinarCreateInputMandatory,
} from '../__data__/webinar.data';
import { prisma, studentResolver, webinarResolver } from '../test-setup';

const TEST_SPACE_NAME = 'IntegrationTestSpace';
const TEST_SPACE_DESCRIPTION = '';

const GUEST_EMAIL_1 = '<EMAIL>';
// const GUEST_UUID_1 = 'e256978b-bc95-4e93-a8f2-1501eb295555';

const embeddedStreamParams = {
    ...streamCreateInput,
    streamType: StreamTypePresentation.internal,
};

export function formatDuration(milliseconds: number) {
    const hours = milliseconds / 3_600_000;
    return `${hours.toFixed(1)}ч`;
}

// Группа, которая создается до начала тестов и прописывается в env LIVE_DIGITAL_SPACE_ID
const TEST_SPACE_ID = liveDigitalConfig.spaceId;

it.skip('test jest setup', () => expect(1).toBe(1));

if (process.env.RUN_SAAS_TESTS === 'true') {
    describe('Webinar Provider', () => {
        describe('Integration Test', () => {
            let webinarId: string | undefined;
            let streamId: string | undefined;

            // let webinarByInvitationId: string | undefined;
            // let streamByInvitationId: string | undefined;

            let studentRegisterInput: {
                streamId: string;
                email: string;
                name: string;
                timezone: string;
            };

            // const aLiveDigital = new LiveDigitalClient();

            beforeEach(async () => {
                const webinar = await webinarResolver.createWebinar(ADMIN_CONTEXT, SCHOOL_UUID, {
                    ...webinarCreateInputMandatory,
                    visibility: WebinarVisibilityPresentation.public,
                    streams: [embeddedStreamParams],
                });
                webinarId = webinar.id;
                streamId = webinar.streams[0].id;

                studentRegisterInput = {
                    streamId,
                    email: GUEST_EMAIL_1,
                    name: 'John Doe',
                    timezone: 'Europe/Moscow',
                };

                // const webinarByInvitation = await webinarResolver.createWebinar(ADMIN_CONTEXT, SCHOOL_UUID, {
                //     ...webinarCreateInputMandatory,
                //     visibility: WebinarVisibilityPresentation.public,
                //     registrationType: RegistrationTypePresentation.byInvitation,
                //     streams: [embeddedStreamParams],
                // });
                // webinarByInvitationId = webinarByInvitation.id;
                // streamByInvitationId = webinarByInvitation.streams[0].id;
            });

            afterEach(async () => {
                if (webinarId) {
                    await webinarResolver.deleteWebinar(ADMIN_CONTEXT, SCHOOL_UUID, webinarId);
                }
                await prisma.participant.deleteMany({});
            });

            it('Должен впустить зарегистрированного студента на открытый вебинар', async () => {
                await studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA);

                const response = await studentResolver.joinWebinar(
                    UNKNOWN_STUDENT_CONTEXT,
                    SCHOOL_UUID,
                    streamId,
                    GUEST_EMAIL_1,
                    new Date(streamCreateInput.date + 15000),
                );

                const url = new URL(response.externalStreamUrl);
                const accessToken = url.searchParams.get('accessToken');
                expect(accessToken).not.toBeNull();

                // const pathParts = url.pathname.split('/').filter((part) => part);
                // const roomAlias = pathParts[1];

                if (accessToken) {
                    // Разделение токена на части
                    const tokenParts = accessToken.split('.');
                    expect(tokenParts.length).toBe(3); // Токен должен состоять из трех частей

                    // Декодирование payload
                    const payloadBase64 = tokenParts[1]; // Вторая часть - это payload
                    const payloadJson = Buffer.from(payloadBase64, 'base64').toString('utf-8');
                    const payload = JSON.parse(payloadJson);

                    expect(payload.aud).toBe('user');
                    //  {
                    //     sub: '67c58cce61db224c8929c7d1',
                    //     aud: 'user',
                    //     type: 'accessToken',
                    //     cId: '60cc442ecb9468521ea7c3b2',
                    //     jti: 'McPKGur9cvq3HEuPkXxii',
                    //     gId: 'tmp_67c58cce61db224c8929c7d1',
                    //     eid: '66701636-406f-4adb-933d-19bf96a3be82',
                    //     tmp: true,
                    //     iat: 1740999886,
                    //     exp: 1741431886
                    //   }

                    expect(payload).toHaveProperty('exp'); // Время истечения
                    const expiresAt = new Date(payload.exp * 1000);

                    // console.log(`Токен выдан: ${issuedAt.toISOString()}`);
                    // console.log(`Токен истекает: ${expiresAt.toISOString()}`);

                    expect(Date.now() + 85_000).toBeLessThan(expiresAt.getTime());
                }
                // 'https://edu.livedigital.space/room/C7lQ8o1BXF?participantName=Student&accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2N2M1NmJmNmY3ZmFhMzcxMjM5MjIxNGYiLCJhdWQiOiJ1c2VyIiwidHlwZSI6ImFjY2Vzc1Rva2VuIiwiY0lkIjoiNjBjYzQ0MmVjYjk0Njg1MjFlYTdjM2IyIiwianRpIjoiOVN4ckVzOVY2cUxieVpqcXUzRURTIiwiZ0lkIjoidG1wXzY3YzU2YmY2ZjdmYWEzNzEyMzkyMjE0ZiIsImVpZCI6IjY2NzAxNjM2LTQwNmYtNGFkYi05MzNkLTE5YmY5NmEzYmU4MiIsInRtcCI6dHJ1ZSwiaWF0IjoxNzQwOTkxNDc4LCJleHAiOjE3NDE0MjM0Nzh9.JqeideU02Hwxhm30AJmu10c0_W8gHKzLUgRwCZk54ak&email=student43%40ya.ru&externalMeetingId=67c56bf7507cdfe966acc8bc'

                // console.log(response);
                // expect(response).toEqual({});
            }, 20_000);
        });

        describe('Live Digital Tests', () => {
            let liveDigital: LiveDigitalClient;

            // Группа, которая создается во время тестирования LiveDigital
            let tempSpaceId: string | undefined;
            const tempSpaceName = 'TempTestSpace';
            const tempSpaceDescription = 'temporary test space';

            let tempRoomId: string | undefined;
            const tempRoomName = 'TempTestRoom';

            beforeAll(() => {
                liveDigital = new LiveDigitalClient();
            });

            describe('Spaces', () => {
                afterEach(async () => {
                    if (tempSpaceId) {
                        await liveDigital.deleteSpace(tempSpaceId);
                        tempSpaceId = undefined;
                    }
                });

                it('Должен создать группу (space)', async () => {
                    const listSpacesBefore = await liveDigital.getSpaces();
                    const listIdsBefore = listSpacesBefore.items.map((space) => space.id);
                    expect(listIdsBefore).toEqual(LIVE_DIGITAL_SPACE_IDS);

                    // Создание
                    const createdSpace = await liveDigital.createSpace(tempSpaceName, tempSpaceDescription);
                    tempSpaceId = createdSpace.id;

                    expect(createdSpace).toHaveProperty('id');
                    expect(typeof createdSpace.id).toBe('string');

                    const listSpacesAfter = await liveDigital.getSpaces();
                    const listIdsAfter = listSpacesAfter.items.map((space) => space.id);
                    expect(listIdsAfter).toEqual([...LIVE_DIGITAL_SPACE_IDS, tempSpaceId]);
                });

                it('Должен удалить группу (space)', async () => {
                    const createdSpace = await liveDigital.createSpace(tempSpaceName, tempSpaceDescription);

                    const listSpacesBefore = await liveDigital.getSpaces();
                    expect(listSpacesBefore.items.map((space) => space.id)).toEqual([
                        ...LIVE_DIGITAL_SPACE_IDS,
                        createdSpace.id,
                    ]);

                    // Удаление
                    await liveDigital.deleteSpace(createdSpace.id);

                    const listSpacesAfter = await liveDigital.getSpaces();
                    expect(listSpacesAfter.items.map((space) => space.id)).toEqual(LIVE_DIGITAL_SPACE_IDS);
                });

                it('Должен получить список групп', async () => {
                    const createdSpace = await liveDigital.createSpace(tempSpaceName, tempSpaceDescription);
                    tempSpaceId = createdSpace.id;

                    const listSpaces = await liveDigital.getSpaces();
                    const items = listSpaces.items.map((space) => ({
                        id: space.id,
                        description: space.description,
                        name: space.name,
                        isPublic: space.isPublic,
                    }));
                    expect(items).toEqual([
                        {
                            id: PROD_LIVE_DIGITAL_SPACE_ID,
                            name: expect.any(String),
                            description: expect.any(String),
                            isPublic: false,
                        },
                        {
                            id: DEV_LIVE_DIGITAL_SPACE_ID,
                            name: expect.any(String),
                            description: expect.any(String),
                            isPublic: false,
                        },
                        {
                            id: TEST_SPACE_ID,
                            name: TEST_SPACE_NAME,
                            description: TEST_SPACE_DESCRIPTION,
                            isPublic: false,
                        },
                        {
                            id: expect.any(String),
                            name: tempSpaceName,
                            description: tempSpaceDescription,
                            isPublic: false,
                        },
                    ]);
                });
            });

            describe('Rooms', () => {
                beforeAll(async () => {
                    const createdSpace = await liveDigital.createSpace(tempSpaceName, tempSpaceDescription);
                    tempSpaceId = createdSpace.id;
                });

                afterAll(async () => {
                    if (tempSpaceId) {
                        await liveDigital.deleteSpace(tempSpaceId);
                        tempSpaceId = undefined;
                    }
                });

                afterEach(async () => {
                    if (tempRoomId) {
                        await liveDigital.deleteRoom({ spaceId: tempSpaceId, roomId: tempRoomId });
                        tempRoomId = undefined;
                    }
                });

                it('Должен создать комнату', async () => {
                    const createdRoom = await liveDigital.createRoom(tempSpaceId, tempRoomName);
                    tempRoomId = createdRoom.id;

                    expect(createdRoom).toMatchObject({
                        id: expect.any(String),
                        alias: expect.any(String),
                        ...DEFAULT_CREATE_ROOM_ARGS,
                    });
                });

                it('Должен получить комнату вебинара по ID', async () => {
                    const createdRoom = await liveDigital.createRoom(tempSpaceId, tempRoomName);
                    tempRoomId = createdRoom.id;

                    const room = await liveDigital.getRoomById({
                        spaceId: tempSpaceId,
                        roomId: createdRoom.id,
                    });

                    expect(room).toMatchObject({
                        id: expect.any(String),
                        alias: expect.any(String),
                        ...DEFAULT_CREATE_ROOM_ARGS,
                    });
                });

                it('Должен удалить комнату', async () => {
                    const createdRoom = await liveDigital.createRoom(tempSpaceId, tempRoomName);
                    const rooms = await liveDigital.getSpaceRooms(tempSpaceId);
                    expect(rooms.items.length).toEqual(1);

                    await liveDigital.deleteRoom({ spaceId: tempSpaceId, roomId: createdRoom.id });

                    const roomsAfter = await liveDigital.getSpaceRooms(tempSpaceId);
                    expect(roomsAfter.items.length).toEqual(0);
                });

                it('Должен получить список комнат группы', async () => {
                    const createdRoom = await liveDigital.createRoom(tempSpaceId, tempRoomName);
                    tempRoomId = createdRoom.id;

                    const rooms = await liveDigital.getSpaceRooms(tempSpaceId);
                    expect(rooms).toEqual({
                        items: [
                            {
                                id: expect.any(String),
                                alias: expect.any(String),
                                name: tempRoomName,
                            },
                        ],
                        webinars: 1,
                        total: 1,
                    });
                });
            });

            describe.skip('Access', () => {
                beforeAll(async () => {
                    const createdSpace = await liveDigital.createSpace(tempSpaceName, tempSpaceDescription);
                    const createdRoom = await liveDigital.createRoom(createdSpace.id, tempRoomName);
                    tempSpaceId = createdSpace.id;
                    tempRoomId = createdRoom.id;
                });

                afterAll(async () => {
                    if (tempSpaceId) {
                        await liveDigital.deleteSpace(tempSpaceId);
                        tempSpaceId = undefined;
                        tempRoomId = undefined;
                    }
                });

                it('Получить токен для доступа к вебинару для гостя', async () => {
                    const user = {
                        username: 'Jon Doe',
                        email: '<EMAIL>',
                        role: 'user' as const,
                        externalUserId: '67890',
                        externalMeetingId: '73643645',
                    };

                    // Генерация ссылки
                    const result = await liveDigital.generateAccessLink(
                        { spaceId: tempSpaceId, roomId: tempRoomId },
                        user,
                    );

                    // Проверка наличия accessUrl
                    expect(result).toHaveProperty('accessUrl');
                    expect(typeof result.accessUrl).toBe('string');
                    expect(result.accessUrl).toContain('https');

                    // Извлечение токена из URL
                    const url = new URL(result.accessUrl);
                    const accessToken = url.searchParams.get('accessToken');
                    expect(accessToken).not.toBeNull();

                    if (accessToken) {
                        // Разделение токена на части
                        const tokenParts = accessToken.split('.');
                        expect(tokenParts.length).toBe(3); // Токен должен состоять из трех частей

                        // Декодирование payload
                        const payloadBase64 = tokenParts[1]; // Вторая часть - это payload
                        const payloadJson = Buffer.from(payloadBase64, 'base64').toString('utf-8');
                        const payload = JSON.parse(payloadJson);

                        expect(payload).toHaveProperty('exp'); // Время истечения
                        const expiresAt = new Date(payload.exp * 1000);

                        // console.log(`Токен выдан: ${issuedAt.toISOString()}`);
                        // console.log(`Токен истекает: ${expiresAt.toISOString()}`);

                        expect(Date.now() + 85_000).toBeLessThan(expiresAt.getTime());
                    }
                });

                it('Получить токен для доступа к вебинару для модератора', async () => {
                    const user = {
                        username: 'Don Jackson',
                        email: '<EMAIL>',
                        role: 'moderator' as const,
                        externalUserId: '003',
                        externalMeetingId: '73633647',
                    };

                    // Генерация ссылки
                    const result = await liveDigital.generateAccessLink(
                        { spaceId: tempSpaceId, roomId: tempRoomId },
                        user,
                    );

                    // Проверка наличия accessUrl
                    expect(result).toHaveProperty('accessUrl');
                    expect(typeof result.accessUrl).toBe('string');
                    expect(result.accessUrl).toContain('https');

                    // Извлечение токена из URL
                    const url = new URL(result.accessUrl);
                    const accessToken = url.searchParams.get('accessToken');
                    expect(accessToken).not.toBeNull();
                });
            });

            describe('Records', () => {
                beforeAll(async () => {
                    const createdSpace = await liveDigital.createSpace(tempSpaceName, tempSpaceDescription);
                    const createdRoom = await liveDigital.createRoom(createdSpace.id, tempRoomName);
                    tempSpaceId = createdSpace.id;
                    tempRoomId = createdRoom.id;
                });

                afterAll(async () => {
                    if (tempSpaceId) {
                        await liveDigital.deleteSpace(tempSpaceId);
                        tempSpaceId = undefined;
                        tempRoomId = undefined;
                    }
                });

                // TODO: подумать как инициировать начало вебинара
                it.skip('Должен начать запись', async () => {
                    const room = { spaceId: tempSpaceId, roomId: tempRoomId };

                    const user = {
                        username: 'Jon Doe',
                        email: '<EMAIL>',
                        role: 'moderator' as const,
                        externalUserId: '67890',
                        externalMeetingId: '73643645',
                    };

                    const result = await liveDigital.generateAccessLink(
                        { spaceId: tempSpaceId, roomId: tempRoomId },
                        user,
                    );
                    const url = result.accessUrl;
                    console.log(url);
                    // join webinar

                    await liveDigital.startRecord(room);
                    await setTimeout(20_000);
                    await liveDigital.stopRecord(room);
                    const records = liveDigital.getRecords(room);
                    console.log({ records: JSON.stringify(records) });
                }, 70_000);
            });
        });
    });
}
