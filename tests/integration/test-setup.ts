import 'reflect-metadata';

import { INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { AmqpManager } from '@skillspace/amqp-contracts';
import { createLogger } from '@skillspace/logger';

import { AppModule } from '../../src/app.module';
import { setupGlobalMiddlewares } from '../../src/bootstrap-setup';
import { PrismaService } from '../../src/core/prisma/prisma.service';
import { INotificationServiceAdapter } from '../../src/webinars/application/adapters/microservices/notification-service-adapter.interface';
import { IStatisticsServiceAdapter } from '../../src/webinars/application/adapters/microservices/statistics-service-adapter.interface';
import { IUserRegistryAdapter } from '../../src/webinars/application/adapters/microservices/user-registry-adapter.interface';
import { ICaptchaServiceAdapter } from '../../src/webinars/application/adapters/saas/captcha-service-adapter.interface';
import { IWebinarProviderAdapter } from '../../src/webinars/application/adapters/saas/webinar-provider-adapter.interface';
import {
    CAPTCHA_SERVICE_ADAPTER,
    EDITOR_SERVICE_ADAPTER,
    FILE_STORAGE_ADAPTER,
    NOTIFICATION_SERVICE_ADAPTER,
    STATISTICS_SERVICE_ADAPTER,
    USER_REGISTRY_ADAPTER,
    WEBINAR_PROVIDER_ADAPTER,
} from '../../src/webinars/injects';
import { StreamsResolver } from '../../src/webinars/presentation/stream.resolver';
import { StudentsResolver } from '../../src/webinars/presentation/students.resolver';
import { UserIdentityConsumer } from '../../src/webinars/presentation/user-identity.consumer';
import { WebinarsResolver } from '../../src/webinars/presentation/webinar.resolver';
import { CaptchaServiceMock } from './__mock__/captcha-service.mock';
import { EditorServiceAdapterMock } from './__mock__/editor-service-adapter.mock';
import { StorageServiceMock } from './__mock__/storage-service.mock';

let app: INestApplication;
let prisma: PrismaService;

let webinarResolver: WebinarsResolver;
let streamResolver: StreamsResolver;
let studentResolver: StudentsResolver;
let webinarProvider: IWebinarProviderAdapter;

let statisticService: IStatisticsServiceAdapter;
let notificationService: INotificationServiceAdapter;
let userService: IUserRegistryAdapter;

let userIdentityConsumer: UserIdentityConsumer;
let captchaService: ICaptchaServiceAdapter;

let amqpConnection: AmqpManager;
let messagePublishSpy: jest.SpyInstance;

let notifyAboutCreatedStream: jest.SpyInstance;
let notifyAboutRemovedStreams: jest.SpyInstance;
let notifyAboutRemovedWebinars: jest.SpyInstance;

let notifyAboutNewStudents: jest.SpyInstance;
let notifyAboutRegisteredStudents: jest.SpyInstance;
let notifyAboutStudentActivity: jest.SpyInstance;
let notifyStudentByEmail: jest.SpyInstance;

let validateCaptchaSpy: jest.SpyInstance;

beforeAll(async () => {
    const testModule = Test.createTestingModule({
        imports: [AppModule],
    });

    const moduleFixture = await testModule
        .overrideProvider(FILE_STORAGE_ADAPTER)
        .useClass(StorageServiceMock)
        .overrideProvider(CAPTCHA_SERVICE_ADAPTER)
        .useClass(CaptchaServiceMock)
        .overrideProvider(EDITOR_SERVICE_ADAPTER)
        .useClass(EditorServiceAdapterMock)
        .compile();

    app = moduleFixture.createNestApplication({ logger: await createLogger() });
    setupGlobalMiddlewares(app);
    await app.init();

    webinarResolver = app.get<WebinarsResolver>(WebinarsResolver);
    streamResolver = app.get<StreamsResolver>(StreamsResolver);
    studentResolver = app.get<StudentsResolver>(StudentsResolver);
    userIdentityConsumer = app.get<UserIdentityConsumer>(UserIdentityConsumer);

    webinarProvider = app.get<IWebinarProviderAdapter>(WEBINAR_PROVIDER_ADAPTER);
    captchaService = app.get<ICaptchaServiceAdapter>(CAPTCHA_SERVICE_ADAPTER);

    statisticService = app.get<IStatisticsServiceAdapter>(STATISTICS_SERVICE_ADAPTER);
    notificationService = app.get<INotificationServiceAdapter>(NOTIFICATION_SERVICE_ADAPTER);
    userService = app.get<IUserRegistryAdapter>(USER_REGISTRY_ADAPTER);

    amqpConnection = app.get<AmqpManager>(AmqpManager);

    prisma = app.get(PrismaService);
    if (!prisma) {
        throw new Error('❌ PrismaService не найден');
    }
    await prisma.$connect();
}, 30000);

beforeEach(() => {
    jest.clearAllMocks();

    notifyAboutNewStudents = jest.spyOn(userService, 'notifyMonolithAboutNewStudents');

    notifyStudentByEmail = jest.spyOn(notificationService, 'notifyStudentByEmail');

    validateCaptchaSpy = jest.spyOn(captchaService, 'validateCaptcha');

    notifyAboutRegisteredStudents = jest.spyOn(statisticService, 'notifyAboutRegisteredStudents');
    notifyAboutStudentActivity = jest.spyOn(statisticService, 'notifyAboutStudentActivity');
    notifyAboutCreatedStream = jest.spyOn(statisticService, 'notifyAboutCreatedStream');
    notifyAboutRemovedStreams = jest.spyOn(statisticService, 'notifyAboutRemovedStreams');
    notifyAboutRemovedWebinars = jest.spyOn(statisticService, 'notifyAboutRemovedWebinars');

    messagePublishSpy = jest.spyOn(amqpConnection, 'publish');
});

afterAll(async () => {
    await app.close();
    await prisma.$disconnect();
}, 20000);

export {
    amqpConnection,
    app,
    captchaService,
    statisticService,
    notificationService,
    userService,
    messagePublishSpy,
    notifyAboutCreatedStream,
    notifyAboutNewStudents,
    notifyAboutRegisteredStudents,
    notifyAboutRemovedStreams,
    notifyAboutRemovedWebinars,
    notifyAboutStudentActivity,
    notifyStudentByEmail,
    prisma,
    streamResolver,
    studentResolver,
    userIdentityConsumer,
    validateCaptchaSpy,
    webinarProvider,
    webinarResolver,
};
