import { ERROR_CODE } from '../../src/shared/webinar-service.error';
import { WebinarVisibilityPresentation } from '../../src/webinars/presentation/enum.presentation';
import { ACTION_WEBINAR_VIEW, FAKE_ID } from './__data__/test-constants';
import {
    ADMIN_CONTEXT,
    canModifyStreamRes,
    GUEST_CONTEXT,
    notModifyStreamRes,
    SCHOOL_UUID,
    streamCreateInput,
    UNKNOWN_STUDENT_CONTEXT,
    webinarCreateInputMandatory,
} from './__data__/webinar.data';
import { app, prisma, statisticService, streamResolver, webinarResolver } from './test-setup';

it('should be defined', () => {
    expect(app).toBeDefined();
});

describe('Stream Resolver', () => {
    let webinarId: string;

    beforeEach(async () => {
        const webinar = await webinarResolver.createWebinar(ADMIN_CONTEXT, SCHOOL_UUID, {
            ...webinarCreateInputMandatory,
            visibility: WebinarVisibilityPresentation.public,
        });
        webinarId = webinar.id;
    });

    afterEach(async () => {
        await Promise.all([
            prisma.stream.deleteMany({}),
            prisma.webinar.deleteMany({}),
            prisma.participant.deleteMany({}),
        ]);
        jest.clearAllMocks();
    });

    describe('Создание потока', () => {
        it('Должен создать поток в вебинаре', async () => {
            const stream = await streamResolver.createStream(ADMIN_CONTEXT, SCHOOL_UUID, webinarId, streamCreateInput);
            expect(stream).toEqual(canModifyStreamRes);
        });

        it('Должен выбросить ошибку при создании, если вебинар потока не найден', async () => {
            await expect(
                streamResolver.createStream(ADMIN_CONTEXT, SCHOOL_UUID, FAKE_ID, streamCreateInput),
            ).rejects.toThrow();
        });

        it('Должен создать поток и уведомить статистику', async () => {
            const notifyAboutCreatedStream: jest.SpyInstance = jest.spyOn(statisticService, 'notifyAboutCreatedStream');

            const stream = await streamResolver.createStream(ADMIN_CONTEXT, SCHOOL_UUID, webinarId, streamCreateInput);
            expect(stream).toEqual(canModifyStreamRes);
            expect(notifyAboutCreatedStream).toHaveBeenCalledWith({
                schoolId: SCHOOL_UUID,
                webinar: { id: webinarId, name: 'Webinar Title' },
                stream: { id: stream.id, name: 'Stream Title' },
            });
        });
    });

    describe('Получение потока', () => {
        it('Должен получить поток', async () => {
            const streamCreated = await streamResolver.createStream(
                ADMIN_CONTEXT,
                SCHOOL_UUID,
                webinarId,
                streamCreateInput,
            );
            const stream = await streamResolver.getOneStream(ADMIN_CONTEXT, SCHOOL_UUID, streamCreated.id);

            expect(stream).toEqual(canModifyStreamRes);
        });

        it('Должен получить поток опубликованного вебинара без ссылок на запись и трансляцию', async () => {
            const streamCreated = await streamResolver.createStream(
                ADMIN_CONTEXT,
                SCHOOL_UUID,
                webinarId,
                streamCreateInput,
            );
            const stream = await streamResolver.getOneStream(UNKNOWN_STUDENT_CONTEXT, SCHOOL_UUID, streamCreated.id);
            expect(stream).toEqual({
                ...notModifyStreamRes,
                recordUrl: null,
                externalStreamUrl: null,
            });
        });

        it('Должен получить поток опубликованного вебинара для гостя', async () => {
            const streamCreated = await streamResolver.createStream(
                ADMIN_CONTEXT,
                SCHOOL_UUID,
                webinarId,
                streamCreateInput,
            );
            const stream = await streamResolver.getOneStream(GUEST_CONTEXT, SCHOOL_UUID, streamCreated.id);
            expect(stream).toEqual({
                ...notModifyStreamRes,
                recordUrl: null,
                externalStreamUrl: null,
            });
        });

        it('Должен получить поток неопубликованного вебинара для пользователя с разрешением на просмотр', async () => {
            const createdWebinar = await webinarResolver.createWebinar(
                ADMIN_CONTEXT,
                SCHOOL_UUID,
                webinarCreateInputMandatory,
            );

            const stream = await streamResolver.getOneStream(
                { ...UNKNOWN_STUDENT_CONTEXT, actions: [ACTION_WEBINAR_VIEW] },
                SCHOOL_UUID,
                createdWebinar.streams[0].id,
            );
            expect(stream).toEqual({
                ...notModifyStreamRes,
                recordUrl: null,
                externalStreamUrl: null,
            });
        });

        it('Должен выбросить ошибку о том, что поток не найден', async () => {
            await expect(streamResolver.getOneStream(ADMIN_CONTEXT, SCHOOL_UUID, FAKE_ID)).rejects.toThrow(
                expect.objectContaining({
                    code: expect.stringContaining(ERROR_CODE.NOT_FOUND_ERROR),
                }),
            );
        });
    });

    describe('Обновление потока', () => {
        it('Не должен обновлять поток если не отправлять input', async () => {
            const streamCreated = await streamResolver.createStream(
                ADMIN_CONTEXT,
                SCHOOL_UUID,
                webinarId,
                streamCreateInput,
            );
            const stream = await streamResolver.updateStream(ADMIN_CONTEXT, SCHOOL_UUID, streamCreated.id, {});
            expect(stream).toEqual(canModifyStreamRes);
        });

        it('Должен изменить настройки потока', async () => {
            const streamCreated = await streamResolver.createStream(
                ADMIN_CONTEXT,
                SCHOOL_UUID,
                webinarId,
                streamCreateInput,
            );
            const streamUpdateInput = {
                title: 'Advanced Node Concepts',
                date: Date.now(),
                duration: 45000,
                // streamType: 'external',
                externalStreamUrl: 'https://example.com/updated-stream',
                speakers: ['1b962ed1-ade4-48f9-a4bf-61757b617c44'],
            };
            const stream = await streamResolver.updateStream(
                ADMIN_CONTEXT,
                SCHOOL_UUID,
                streamCreated.id,
                streamUpdateInput,
            );
            expect(stream).toEqual({
                ...canModifyStreamRes,
                ...streamUpdateInput,
                isAllowedToJoin: true,
                isLive: true,
            });
        });

        it('Должен изменить настройки отображения записи', async () => {
            const streamCreated = await streamResolver.createStream(
                ADMIN_CONTEXT,
                SCHOOL_UUID,
                webinarId,
                streamCreateInput,
            );
            expect(streamCreated).toMatchObject({
                recordUrl: undefined,
                hasRecordUrl: false,
            });

            const updateDto = {
                recordUrl: 'https://example.com/record.mp4',
                hasRecordUrl: true,
            };
            const updated = await streamResolver.updateStream(ADMIN_CONTEXT, SCHOOL_UUID, streamCreated.id, updateDto);
            expect(updated).toMatchObject(updateDto);

            const updated2 = await streamResolver.updateStream(ADMIN_CONTEXT, SCHOOL_UUID, streamCreated.id, {
                recordUrl: null,
                hasRecordUrl: false,
            });

            expect(updated2).toMatchObject({
                recordUrl: null,
                hasRecordUrl: false,
            });
        });
    });

    describe('Удаление потока', () => {
        it('Должен удалить поток и спикеров', async () => {
            const createdStream = await streamResolver.createStream(
                ADMIN_CONTEXT,
                SCHOOL_UUID,
                webinarId,
                streamCreateInput,
            );
            const streamId = createdStream.id;
            expect(createdStream.speakers.length).toBeGreaterThan(0);

            await streamResolver.deleteStream(ADMIN_CONTEXT, SCHOOL_UUID, createdStream.id);

            const speakers = await prisma.participant.findMany({
                where: { streamId },
            });
            expect(speakers.length).toBe(0);

            const streams = await prisma.stream.findMany({
                where: { id: streamId },
            });
            expect(streams.length).toBe(0);
        });

        it('Должен выбросить ошибку при попытке удалить единственный поток вебинара', async () => {
            const webinar = await webinarResolver.getOneWebinar(ADMIN_CONTEXT, SCHOOL_UUID, webinarId);
            expect(webinar.streams.length).toBe(1);
            const streamId = webinar.streams[0].id;

            await expect(streamResolver.deleteStream(ADMIN_CONTEXT, SCHOOL_UUID, streamId)).rejects.toThrow();
        });

        it('Должен выбросить ошибку, если поток не найден, при попытке удалить', async () => {
            await expect(streamResolver.deleteStream(ADMIN_CONTEXT, SCHOOL_UUID, FAKE_ID)).rejects.toThrow(
                expect.objectContaining({
                    code: expect.stringContaining(ERROR_CODE.NOT_FOUND_ERROR),
                }),
            );
        });

        it('Должен удалить поток и уведомить статистику', async () => {
            const notifyAboutRemovedStreams: jest.SpyInstance = jest.spyOn(
                statisticService,
                'notifyAboutRemovedStreams',
            );

            const createdStream = await streamResolver.createStream(
                ADMIN_CONTEXT,
                SCHOOL_UUID,
                webinarId,
                streamCreateInput,
            );
            const streamId = createdStream.id;
            expect(createdStream.speakers.length).toBeGreaterThan(0);

            await streamResolver.deleteStream(ADMIN_CONTEXT, SCHOOL_UUID, createdStream.id);

            expect(notifyAboutRemovedStreams).toHaveBeenCalledWith({
                removedStreamsIds: [streamId],
            });
        });
    });
});
