import { randomUUID } from 'node:crypto';
import { RequestWebinarsRegistrationDetailsContract } from '@skillspace/amqp-contracts';

import { ERROR_CODE } from '../../src/shared/webinar-service.error';
import {
    CardsViewMode,
    RegistrationTypePresentation,
    WebinarVisibilityPresentation,
} from '../../src/webinars/presentation/enum.presentation';
import { EVENT_TYPE_ENUM } from '../../src/webinars/presentation/user-identity.consumer';
import { CAPTCHA } from './__data__/test-constants';
import {
    ADMIN_CONTEXT,
    EMPLOYEE_CONTEXT,
    GUEST_CONTEXT,
    SCHOOL_UUID,
    streamCreateInput,
    STUDENT_CONTEXT,
    UNKNOWN_STUDENT_CONTEXT,
    webinarCreateInputMandatory,
} from './__data__/webinar.data';
import {
    app,
    messagePublishSpy,
    notifyAboutNewStudents,
    notifyAboutRegisteredStudents,
    notifyAboutStudentActivity,
    prisma,
    streamResolver,
    studentResolver,
    userIdentityConsumer,
    userService,
    validateCaptchaSpy,
    webinarResolver,
} from './test-setup';

const GUEST_EMAIL_1 = '<EMAIL>';
const GUEST_UUID_1 = 'e256978b-bc95-4e93-a8f2-1501eb295555';
const GUEST_EMAIL_2 = '<EMAIL>';
const GUEST_UUID_2 = '7270905e-44ef-4245-9beb-0ab963ef67e1';

const UPDATE_STUDENTS_IDENTITY_MESSAGE: RequestWebinarsRegistrationDetailsContract = {
    timestamp: Date.now(),
    requestUuid: randomUUID(),
    payload: {
        type: EVENT_TYPE_ENUM.STUDENT_IDS_PROVIDED,
        streamId: '13123',
        students: [
            {
                email: GUEST_EMAIL_1,
                uuid: GUEST_UUID_1,
                registerToken: null,
                school: {
                    uuid: 'c58be143-5627-4862-8e97-239843d8ec7c',
                    name: 'Название проекта',
                    email: '<EMAIL>',
                    whiteLabel: {
                        hideMobileAppLinks: false,
                        isWhiteLabel: false,
                        schoolLogoUrl: null,
                        primaryColor: '#17b198',
                    },
                    domain: '995d03.dev4.sksp.site',
                },
            },
            {
                email: GUEST_EMAIL_2,
                uuid: GUEST_UUID_2,
                registerToken: null,
                school: {
                    uuid: 'c58be143-5627-4862-8e97-239843d8ec7c',
                    name: 'Название проекта',
                    email: '<EMAIL>',
                    whiteLabel: {
                        hideMobileAppLinks: false,
                        isWhiteLabel: false,
                        schoolLogoUrl: null,
                        primaryColor: '#17b198',
                    },
                    domain: '995d03.dev4.sksp.site',
                },
            },
        ],
    },
};

const STREAM_RESPONSE = {
    countRegistered: undefined,
    countViewed: undefined,
    coverUrl: null,
    date: 1577883600000,
    duration: 7200000,
    isPhoneRequiredOnRegistration: false,
    externalStreamUrl: 'https://example.com/stream',
    hasRecordUrl: false,
    id: expect.any(String),
    isRegistered: true,
    isSpeaker: false,
    hasRecord: false,
    recordUrl: null,
    savedRecordUrl: null,
    autoRecord: false,
    speakers: [
        '66701666-406f-4adb-934d-19bf96a3be81',
        '1b962ed1-ade4-48f9-a4bf-61757b617c44',
        'dbb1a050-5724-41ca-9d75-575d753fb29c',
    ],
    streamType: 'external',
    title: 'Stream Title',
    webinarId: expect.any(String),
    webinarDescription: 'Webinar Description',
    webinarTitle: 'Webinar Title',
    integrationCode: null,
    registrationType: 'byLanding',
    canModify: false,
    canRemove: false,
    isAllowedToJoin: false,
    isLive: false,
    pages: ['123'],
};

const studentRegisteredResult = {
    email: GUEST_EMAIL_1,
    name: 'John Doe',
    timezone: 'Europe/Moscow',
    uuid: null,
    phoneNumber: null,
};

it('should be defined', () => {
    expect(app).toBeDefined();
});

describe('Students Resolver', () => {
    let webinarId: string;
    let streamId: string;
    let webinarByInvitationId: string;
    let streamByInvitationId: string;

    let studentRegisterInput: {
        streamId: string;
        email: string;
        name: string;
        timezone: string;
    };

    beforeEach(async () => {
        jest.clearAllMocks();
        const webinar = await webinarResolver.createWebinar(ADMIN_CONTEXT, SCHOOL_UUID, {
            ...webinarCreateInputMandatory,
            visibility: WebinarVisibilityPresentation.public,
        });
        const webinarByInvitation = await webinarResolver.createWebinar(ADMIN_CONTEXT, SCHOOL_UUID, {
            ...webinarCreateInputMandatory,
            visibility: WebinarVisibilityPresentation.public,
            registrationType: RegistrationTypePresentation.byInvitation,
        });
        webinarId = webinar.id;
        streamId = webinar.streams[0].id;
        webinarByInvitationId = webinarByInvitation.id;
        streamByInvitationId = webinarByInvitation.streams[0].id;
        studentRegisterInput = {
            streamId,
            email: GUEST_EMAIL_1,
            name: 'John Doe',
            timezone: 'Europe/Moscow',
        };
        UPDATE_STUDENTS_IDENTITY_MESSAGE.payload['streamId'] = streamId;
    });

    afterEach(async () => {
        webinarId = '';
        streamId = '';
        await Promise.all([
            prisma.webinar.deleteMany({}),
            prisma.stream.deleteMany({}),
            prisma.participant.deleteMany({}),
        ]);
    });

    describe('Интеграция с монолитом', () => {
        it('Должен при регистрации студента запросить у монолита идентификатор пользователя', async () => {
            const result = await studentResolver.registerStudent(
                GUEST_CONTEXT,
                SCHOOL_UUID,
                studentRegisterInput,
                CAPTCHA,
            );
            expect(result.success).toBe(true);

            expect(messagePublishSpy).toHaveBeenCalledTimes(1);
            expect(notifyAboutNewStudents).toHaveBeenCalledWith({
                type: 'STUDENTS_REGISTERED',
                streamId,
                registeredStudents: [
                    {
                        schoolUuid: SCHOOL_UUID,
                        email: GUEST_EMAIL_1,
                        name: 'John Doe',
                        phoneNumber: undefined,
                        timezone: 'Europe/Moscow',
                    },
                ],
            });
        });

        it('Должен обновить идентификатор зарегистрированного пользователя при получении сообщения от монолита', async () => {
            await studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA);
            await userIdentityConsumer.handleMessage(UPDATE_STUDENTS_IDENTITY_MESSAGE);

            const result = await studentResolver.checkRegistration({
                streamId,
                userEmail: GUEST_EMAIL_1,
            });
            expect(result).toEqual({
                ...studentRegisteredResult,
                uuid: GUEST_UUID_1,
            });
        });

        it('Не должен запрашивать у монолита идентификатор при повторной регистрации', async () => {
            await studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA);
            await userIdentityConsumer.handleMessage(UPDATE_STUDENTS_IDENTITY_MESSAGE);

            jest.clearAllMocks();
            const notifyAboutNewStudents = jest.spyOn(userService, 'notifyMonolithAboutNewStudents');

            await studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA);
            expect(notifyAboutNewStudents).toHaveBeenCalledTimes(0);
        });
    });

    describe('Приглашение студентов', () => {
        it('Должен считать приглашенных пользователей в статистике потока или вебинара', async () => {
            const inviteStudentsDto = {
                streamId,
                emails: [GUEST_EMAIL_1, GUEST_EMAIL_2],
            };

            const result = await studentResolver.inviteStudents(ADMIN_CONTEXT, SCHOOL_UUID, inviteStudentsDto);
            expect(result.success).toBe(true);
            // операция должна быть идемпотентной
            await studentResolver.inviteStudents(ADMIN_CONTEXT, SCHOOL_UUID, inviteStudentsDto);

            // считаем их зарегистрированными
            const webinar = await webinarResolver.getOneWebinar(ADMIN_CONTEXT, SCHOOL_UUID, webinarId);
            expect(webinar).toMatchObject({
                countRegistered: 2,
                countViewed: 0,
            });

            const stream = await streamResolver.getOneStream(ADMIN_CONTEXT, SCHOOL_UUID, streamId);
            expect(stream).toMatchObject({
                countRegistered: 2,
                countViewed: 0,
            });
        });

        it('Должен проверить, что у приглашенных студентов не хватает данных', async () => {
            const inviteStudentsDto = {
                streamId,
                emails: [GUEST_EMAIL_1, GUEST_EMAIL_2],
            };

            await studentResolver.inviteStudents(ADMIN_CONTEXT, SCHOOL_UUID, inviteStudentsDto);

            const result = await studentResolver.checkRegistration({
                userEmail: GUEST_EMAIL_1,
                streamId,
            });
            expect(result).toEqual({
                email: GUEST_EMAIL_1,
                name: null,
                uuid: null,
                timezone: null,
                phoneNumber: null,
            });
        });

        it('Должен зарегистрировать приглашенного студента', async () => {
            const inviteStudentsDto = {
                streamId,
                emails: [GUEST_EMAIL_1, GUEST_EMAIL_2],
            };

            await studentResolver.inviteStudents(ADMIN_CONTEXT, SCHOOL_UUID, inviteStudentsDto);
            await studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA);

            const result = await studentResolver.checkRegistration({
                userEmail: GUEST_EMAIL_1,
                streamId,
            });
            expect(result).toEqual({
                email: GUEST_EMAIL_1,
                name: 'John Doe',
                uuid: null,
                timezone: 'Europe/Moscow',
                phoneNumber: null,
            });
        });

        it('Не должен регистрировать студента которого не приглашали, если регистрация по приглашениям', async () => {
            await webinarResolver.updateWebinar(ADMIN_CONTEXT, SCHOOL_UUID, webinarId, {
                registrationType: RegistrationTypePresentation.byInvitation,
            });
            await expect(
                studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA),
            ).rejects.toThrow();
        });

        it.todo('Должен запросить дополнительные данные у приглашенных студентов при регистрации');

        it('Должен после приглашения новых студентов запросить у монолита их идентификаторы', async () => {
            const inviteStudentsDto = {
                streamId,
                emails: [GUEST_EMAIL_1, GUEST_EMAIL_2],
            };

            await studentResolver.inviteStudents(ADMIN_CONTEXT, SCHOOL_UUID, inviteStudentsDto);

            expect(notifyAboutNewStudents).toHaveBeenCalledWith({
                type: 'STUDENTS_REGISTERED',
                streamId,
                registeredStudents: [
                    { schoolUuid: SCHOOL_UUID, email: GUEST_EMAIL_1 },
                    { schoolUuid: SCHOOL_UUID, email: GUEST_EMAIL_2 },
                ],
            });

            expect(notifyAboutRegisteredStudents).toHaveBeenCalledTimes(0);
        });

        it('Не должен уведомлять статистику о приглашенных студентах пока не получены идентификаторы', async () => {
            await studentResolver.inviteStudents(ADMIN_CONTEXT, SCHOOL_UUID, {
                streamId,
                emails: [GUEST_EMAIL_1, GUEST_EMAIL_2],
            });
            expect(notifyAboutRegisteredStudents).toHaveBeenCalledTimes(0);
        });

        // TODO: не понятно, почему не отслеживает вызов
        it('Должен уведомить статистику о приглашенных пользователях, когда они получат идентификаторы', async () => {
            const inviteStudentsDto = {
                streamId,
                emails: [GUEST_EMAIL_1, GUEST_EMAIL_2],
            };

            await studentResolver.inviteStudents(ADMIN_CONTEXT, SCHOOL_UUID, inviteStudentsDto);
            await userIdentityConsumer.handleMessage(UPDATE_STUDENTS_IDENTITY_MESSAGE);

            expect(messagePublishSpy).toHaveBeenCalledTimes(1);
            // должен отследить 3 вызова -> уведомление монолита, потом - статистика, приглашение

            // expect(notifyAboutRegisteredStudents).toHaveBeenCalledWith({
            //     webinarId,
            //     schoolId: SCHOOL_UUID,
            //     streamId,
            //     studentsIds: [GUEST_UUID_1, GUEST_UUID_2],
            // });
        });

        // TODO: не понятно, почему не отслеживает вызов
        it('Должен отправить приглашения студентам, после того, как они получать идентификаторы и ссылку от монолита', async () => {
            const inviteStudentsDto = {
                streamId,
                emails: [GUEST_EMAIL_1, GUEST_EMAIL_2],
            };

            await studentResolver.inviteStudents(ADMIN_CONTEXT, SCHOOL_UUID, inviteStudentsDto);
            await userIdentityConsumer.handleMessage(UPDATE_STUDENTS_IDENTITY_MESSAGE);

            expect(messagePublishSpy).toHaveBeenCalledTimes(1);
            // должен отследить 3 вызова -> уведомление монолита, потом - статистика, приглашение

            // expect(notifyStudentByEmail).toHaveBeenCalledWith({
            //     recipients: ['<EMAIL>'],
            //     subject: 'Вебинар: Webinar Title',
            //     body: {
            //         redirectUrl: 'https://example.com',
            //         html: '<p>Вы записаны на вебинар "Webinar Title", который состоится 1 января 2020 г.</p>',
            //         schoolSlug: null,
            //     },
            //     template: 'notification',
            // });
        });
    });

    describe('Регистрация студента', () => {
        // TODO: когда включена капча
        it.skip('Должен проверить капчу при регистрации гостя', async () => {
            await studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA);

            expect(validateCaptchaSpy).toHaveBeenCalledWith(CAPTCHA, undefined);
        });

        // TODO: когда включена капча
        it.skip('Не должен проверять капчу если пользователь авторизован', async () => {
            await studentResolver.registerStudent(
                STUDENT_CONTEXT,
                SCHOOL_UUID,
                { ...studentRegisterInput, email: STUDENT_CONTEXT.email },
                CAPTCHA,
            );
            expect(validateCaptchaSpy).toHaveBeenCalledTimes(0);
        });

        // TODO: когда включена капча
        it.skip('Должен выбросить ошибку когда при регистрации гостя неt капчи', async () => {
            await expect(
                studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput),
            ).rejects.toThrow(
                expect.objectContaining({
                    code: expect.stringContaining(ERROR_CODE.BAD_REQUEST_ERROR),
                    message: expect.stringContaining('Для регистрации необходима капча'),
                }),
            );
        });

        it('Должен сохранить данные зарегистрированного пользователя и предоставить их при проверке', async () => {
            await studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA);

            const result = await studentResolver.checkRegistration({
                streamId,
                userEmail: GUEST_EMAIL_1,
            });
            expect(result).toEqual(studentRegisteredResult);
        });

        it('Не должен показывать вебинар в библиотеке, если пользователь не зарегистрирован', async () => {
            const cardsBefore = await webinarResolver.getWebinarCards(
                UNKNOWN_STUDENT_CONTEXT,
                SCHOOL_UUID,
                {
                    mode: CardsViewMode.library,
                },
                { skip: 0, take: 10 },
                new Date('2024-11-20T12:00:00.000Z'),
            );
            expect(cardsBefore).toEqual({ data: [], total: 0 });
        });

        it('Должен зарегистрировать студента на вебинар и показать ему библиотеку с вебинаром', async () => {
            await studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA);

            const cards = await webinarResolver.getWebinarCards(
                {
                    ...UNKNOWN_STUDENT_CONTEXT,
                    email: studentRegisterInput.email,
                },
                SCHOOL_UUID,
                {
                    mode: CardsViewMode.library,
                },
                { skip: 0, take: 10 },
                new Date('2024-11-20T12:00:00.000Z'),
            );
            expect(cards).toEqual({
                data: [
                    {
                        webinarId,
                        title: 'Webinar Title',
                        description: 'Webinar Description',
                        visibility: 'public',
                        isPhoneRequiredOnRegistration: false,
                        coverUrl: null,
                        streamId,
                        date: 1577883600000,
                        duration: 7200000,
                        hasRecord: false,
                        isRegistered: true,
                        isSpeaker: false,
                        isViewed: false,
                        isAllowedToJoin: false,
                        isLive: false,
                    },
                ],
                total: 1,
            });

            await streamResolver.updateStream(ADMIN_CONTEXT, SCHOOL_UUID, streamId, {
                recordUrl: 'https://example.com/record.mp4',
                hasRecordUrl: false,
            });

            const cardsUpdated = await webinarResolver.getWebinarCards(
                {
                    ...UNKNOWN_STUDENT_CONTEXT,
                    email: studentRegisterInput.email,
                },
                SCHOOL_UUID,
                {
                    mode: CardsViewMode.library,
                },
                { skip: 0, take: 10 },
                new Date('2024-11-20T12:00:00.000Z'),
            );
            expect(cardsUpdated).toEqual({
                data: [
                    {
                        webinarId,
                        title: 'Webinar Title',
                        description: 'Webinar Description',
                        visibility: 'public',
                        isPhoneRequiredOnRegistration: false,
                        coverUrl: null,
                        streamId,
                        date: 1577883600000,
                        duration: 7200000,
                        hasRecord: true,
                        isRegistered: true,
                        isSpeaker: false,
                        isViewed: false,
                        isAllowedToJoin: false,
                        isLive: false,
                    },
                ],
                total: 1,
            });
        });

        it('Должен считать зарегистрированного пользователя в вебинаре и потоке', async () => {
            await studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA);
            // операция должна быть идемпотентной
            await studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA);

            const webinar = await webinarResolver.getOneWebinar(ADMIN_CONTEXT, SCHOOL_UUID, webinarId);
            expect(webinar).toMatchObject({
                countRegistered: 1,
                countViewed: 0,
            });

            const stream = await streamResolver.getOneStream(ADMIN_CONTEXT, SCHOOL_UUID, streamId);
            expect(stream).toMatchObject({
                countRegistered: 1,
                countViewed: 0,
            });
        });

        // TODO: не понятно, почему не отслеживает вызовы
        it('Должен уведомить статистику о зарегистрированном пользователе после получения идентификатора', async () => {
            await studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA);
            await userIdentityConsumer.handleMessage(UPDATE_STUDENTS_IDENTITY_MESSAGE);

            expect(messagePublishSpy).toHaveBeenCalledTimes(1);
            // должен отследить 3 вызова -> уведомление монолита, потом - статистика, приглашение

            // expect(notifyAboutRegisteredStudents).toHaveBeenCalledWith({
            //     webinarId,
            //     schoolId: SCHOOL_UUID,
            //     streamId,
            //     studentsIds: [GUEST_UUID_1],
            // });
        });
    });

    describe('Вход на вебинар', () => {
        it('Должен впустить зарегистрированного студента на транслируемый вебинар', async () => {
            await studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA);

            const response = await studentResolver.joinWebinar(
                UNKNOWN_STUDENT_CONTEXT,
                SCHOOL_UUID,
                streamId,
                GUEST_EMAIL_1,
                new Date(streamCreateInput.date + 15000),
            );

            expect(response).toEqual(STREAM_RESPONSE);
        });

        it.skip('Должен потребовать регистрации у гостя при входе на вебинар доступный по приглашению', async () => {
            await expect(
                studentResolver.joinWebinar(
                    GUEST_CONTEXT,
                    SCHOOL_UUID,
                    streamByInvitationId,
                    '<EMAIL>',
                    new Date(streamCreateInput.date + 15000),
                ),
            ).rejects.toThrow(
                expect.objectContaining({
                    code: expect.stringContaining(ERROR_CODE.FORBIDDEN_ERROR),
                    message: expect.stringContaining('Вебинар доступен только по приглашению'),
                }),
            );
        });

        it('Должен впустить на транслируемый вебинар сотрудника', async () => {
            const joinedResult = await studentResolver.joinWebinar(
                EMPLOYEE_CONTEXT,
                SCHOOL_UUID,
                streamId,
                EMPLOYEE_CONTEXT.email,
                new Date(streamCreateInput.date + 15000),
            );

            expect(joinedResult).toEqual({ ...STREAM_RESPONSE, isRegistered: false });
        });

        it('Не должен требовать регистрацию у гостя при входе на транслируемый вебинар', async () => {
            await studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA);

            const joinedResult = await studentResolver.joinWebinar(
                GUEST_CONTEXT,
                SCHOOL_UUID,
                streamId,
                GUEST_EMAIL_1,
                new Date(streamCreateInput.date + 15000),
            );

            expect(joinedResult).toEqual(STREAM_RESPONSE);
        });
    });

    describe('Вход на вебинар: Статистика', () => {
        it('Должен уведомить статистику о входе зарегистрированного студента на транслируемый вебинар', async () => {
            await studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA);
            await userIdentityConsumer.handleMessage(UPDATE_STUDENTS_IDENTITY_MESSAGE);
            jest.clearAllMocks();

            await studentResolver.joinWebinar(
                UNKNOWN_STUDENT_CONTEXT,
                SCHOOL_UUID,
                streamId,
                GUEST_EMAIL_1,
                new Date(streamCreateInput.date + 15000),
            );

            expect(notifyAboutStudentActivity).toHaveBeenCalledWith({
                studentId: GUEST_UUID_1,
                streamId,
                score: 0,
                visitingStatus: 'visit',
                completed: true,
            });
        });

        // TODO: visitingStatus: 'visit',
        it.skip('Должен уведомить статистику о просмотре записи студентом', async () => {
            await studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA);

            // TODO: проработать вариант, когда у студента нет идентификатора (будет ошибка при отправке статистики userId = null)
            await userIdentityConsumer.handleMessage(UPDATE_STUDENTS_IDENTITY_MESSAGE);
            jest.clearAllMocks();

            await studentResolver.joinWebinar(
                UNKNOWN_STUDENT_CONTEXT,
                SCHOOL_UUID,
                streamId,
                GUEST_EMAIL_1,
                new Date(streamCreateInput.date + streamCreateInput.duration + 15000),
            );

            expect(notifyAboutStudentActivity).toHaveBeenCalledTimes(1);
            expect(notifyAboutStudentActivity).toHaveBeenCalledWith({
                studentId: GUEST_UUID_1,
                streamId,
                score: 0,
                visitingStatus: 'record',
                completed: true,
            });
        });

        it('Не должен собирать статистику при входе сотрудника на транслируемый вебинар', async () => {
            await studentResolver.joinWebinar(
                EMPLOYEE_CONTEXT,
                SCHOOL_UUID,
                streamId,
                EMPLOYEE_CONTEXT.email,
                new Date(streamCreateInput.date + 15000),
            );

            expect(notifyAboutStudentActivity).toHaveBeenCalledTimes(0);
        });

        it('Не должен уведомить статистику о входе, если студент не получил идентификатор к моменту входа на вебинар', async () => {
            await studentResolver.registerStudent(GUEST_CONTEXT, SCHOOL_UUID, studentRegisterInput, CAPTCHA);

            await studentResolver.joinWebinar(
                UNKNOWN_STUDENT_CONTEXT,
                SCHOOL_UUID,
                streamId,
                GUEST_EMAIL_1,
                new Date(streamCreateInput.date + 15000),
            );

            // Этот метод выбрасывает ошибку, если userUuid === null
            expect(notifyAboutStudentActivity).toHaveBeenCalledTimes(0);
        });
    });
});
