import { INestApplication } from '@nestjs/common';
import { AuthService } from '@skillspace/access';
import { DocumentNode, print } from 'graphql';
import gql from 'graphql-tag';
import { Server } from 'http';
import * as request from 'supertest';

type JSONPrimitive = string | number | boolean | null;
type JSONArray = JSONValue[];
type JSONValue = JSONPrimitive | JSONObject | JSONArray;

export interface JSONObject {
    [member: string]: JSONValue;
}

export interface UserContext {
    actions: string[];
    role: 'ROLE_EMPLOYEE' | 'ROLE_STUDENT' | 'ROLE_GUEST';
    schoolId: string | undefined;
    userId: string | undefined;
    userName: string | undefined;
    userEmail: string | undefined;
    unionAuthKey: string | undefined;
}

export class GqlTestSession {
    private agent: request.SuperTest<request.Test>;

    constructor(
        private readonly server: Server,
        private readonly token: string,
    ) {
        this.agent = request(this.server) as unknown as request.SuperTest<request.Test>;
    }

    private async gql<T>(query: DocumentNode, variables?: Record<string, any>): Promise<T> {
        const queryString = print(query);

        const response = await this.agent
            .post('/graphql')
            .set('Authorization', `Bearer ${this.token}`)
            .set('Content-Type', 'application/json')
            .send({ query: queryString, variables });

        if (response.body.errors) {
            throw new Error(`GraphQL Error: ${JSON.stringify(response.body.errors)}`);
        }

        return response.body.data as T;
    }

    public async find(): Promise<{}> {
        const query = gql`
            query {}
        `;
        return this.gql<{}>(query);
    }

    public async create(input: JSONObject): Promise<{}> {
        const mutation = gql`
            mutation () {}
        `;
        return this.gql<{}>(mutation, { dto: input });
    }
}

export async function createGqlTestSession(app: INestApplication, context: UserContext): Promise<GqlTestSession> {
    const authService = app.get<AuthService>(AuthService);
    const token = await authService.signAuthorizationToken(context);
    const server: Server = app.getHttpServer();
    return new GqlTestSession(server, token);
}
