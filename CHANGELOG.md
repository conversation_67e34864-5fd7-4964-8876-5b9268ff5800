## <small>1.1.1 (2025-05-22)</small>

* fix: Изменен тип сервиса на NodePort в конфигураци<PERSON> He<PERSON> ([760284a](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/760284a))

## 1.1.0 (2025-05-20)

* Merge branch 'webinar-path-phone' into 'main' ([4916c26](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/4916c26))
* feat: Патч регистрации с телефоном ([3b35f68](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/3b35f68))

## <small>1.0.7 (2025-05-19)</small>

* Merge branch 'add-express' into 'main' ([cec340e](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/cec340e))
* fix: Добавил в зависимости Express ([3534f24](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/3534f24))

## <small>1.0.6 (2025-05-19)</small>

* Merge branch 'fix-close-room-conditions' into 'main' ([c3b30fd](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/c3b30fd))
* Update .gitlab-ci.yml file ([194a978](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/194a978))
* fix: закрытие комнаты только после запланированного времени окончания ([3f187df](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/3f187df))

## <small>1.0.5 (2025-04-30)</small>

* Merge branch 'ci-fix' into 'main' ([97ddf3c](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/97ddf3c))
* fix: исправлена опечатка в README.md для сервиса webinar ([67e9efa](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/67e9efa))

## <small>1.0.4 (2025-04-30)</small>

* Merge branch 'ci-fix' into 'main' ([937eeb2](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/937eeb2))
* fix: упрощена команда установки зависимостей в Dockerfile для вебинара ([3df22c7](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/3df22c7))

## <small>1.0.3 (2025-04-30)</small>

* Merge branch 'ci-fix' into 'main' ([d0bb65c](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d0bb65c))
* fix: упрощена команда установки зависимостей в Dockerfile для вебинара ([b6989bc](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/b6989bc))

## <small>1.0.2 (2025-04-30)</small>

* Edit Dockerfile ([20c896e](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/20c896e))
* Edit Dockerfile ([41602b4](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/41602b4))
* Edit Dockerfile ([d36a036](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d36a036))
* Edit Dockerfile ([a26ecf0](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/a26ecf0))
* Merge branch 'ci-fix' into 'main' ([d55e791](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d55e791))
* Merge branch 'main' into ci-fix ([6584b32](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6584b32))
* fix: обновлен Dockerfile для оптимизации сборки и запуска приложения ([19b649d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/19b649d))

## <small>1.0.1 (2025-04-30)</small>

* Merge branch 'ci-fix' into 'main' ([a065d7d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/a065d7d))
* Merge branch 'ci-fix' into 'main' ([d183dd8](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d183dd8))
* Merge branch 'ci-fix' into 'main' ([fb28478](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/fb28478))
* Merge branch 'ci-fix' into 'main' ([73eb092](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/73eb092))
* fix: добавлена информация о завершении файла README.md в разделе webinar ([0464092](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0464092))
* fix: исправлена опечатка в README.md файла вебинара ([f86d4c8](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/f86d4c8))
* fix: исправлена опечатка в README.md файла вебинара ([129036b](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/129036b))
* fix: исправлена опечатка в README.md файла вебинара ([676fc75](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/676fc75))
* fix: исправлена опечатка в README.md файла вебинара ([42deeec](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/42deeec))
* fix: исправлена опечатка в README.md файла вебинара ([85b5a32](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/85b5a32))
* fix: обновлена структура README в сервисе webinar ([e05a861](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e05a861))
* fix: удалены ci конфигурации и добавлены шаблоны skillspace/main.gitlab-ci.yml ([2730af9](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/2730af9))

## 1.0.0 (2025-04-30)

* 1.0.0 ([ddcd9da](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/ddcd9da))
* 123123 ([31b0ca8](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/31b0ca8))
* 123123 ([753eb36](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/753eb36))
* 123123 ([c73bf51](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/c73bf51))
* 1231231 ([c35135d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/c35135d))
* 123123123 ([887fc3c](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/887fc3c))
* add broker module ([255b3fd](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/255b3fd))
* add canModify flags ([9cdc43d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/9cdc43d))
* add check registration endpoint and flags into stream ([a71801d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/a71801d))
* add comment ([41caf26](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/41caf26))
* add console output ([cf08e02](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/cf08e02))
* add console output ([3c26a27](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/3c26a27))
* add console output in webinar cards ([2ac9cc2](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/2ac9cc2))
* add CPU resource limits for microservices ([21ebcf9](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/21ebcf9))
* add custom error ([0b5ec28](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0b5ec28))
* add field in output ([76829e9](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/76829e9))
* add flags in stream output ([f8a1642](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/f8a1642))
* add gql tags ([3661a37](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/3661a37))
* add init-mongo.js ([e42f2aa](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e42f2aa))
* add mock coverUrl ([a6fbf97](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/a6fbf97))
* add models ([da00d49](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/da00d49))
* add permissions ([abab6e5](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/abab6e5))
* add search ([e57d1f5](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e57d1f5))
* add webinar cards test ([c6f3557](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/c6f3557))
* build ([98e35c5](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/98e35c5))
* change app config ([247d24a](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/247d24a))
* change notifications input, fix tests ([568ac69](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/568ac69))
* change presentation enums ([4e2b6c6](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/4e2b6c6))
* change sort ([a11cf7a](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/a11cf7a))
* changelog ([f6f2ae5](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/f6f2ae5))
* check stream permissions ([b856ae4](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/b856ae4))
* CI/CD ([02daea6](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/02daea6))
* clean code ([3e6435b](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/3e6435b))
* comment contracts SHOULD BE REVERTED ([879d8f3](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/879d8f3))
* comment permissions ([0a32fda](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0a32fda))
* comment PermissionsGuard injection ([e773531](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e773531))
* commit ([33dcab2](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/33dcab2))
* complete ([d7c235f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d7c235f))
* complete delete stream and webinars ([cb8b74a](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/cb8b74a))
* complete getCatalog ([f3176cb](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/f3176cb))
* complete library ([75a4e01](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/75a4e01))
* complete models ([fe31c99](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/fe31c99))
* complete pipeline ([0024441](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0024441))
* complete webinar catalog ([8189e43](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/8189e43))
* completed ([059184d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/059184d))
* count registered viewed ([4502027](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/4502027))
* cover ([1ca0904](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/1ca0904))
* cover ([1a6b8ac](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/1a6b8ac))
* create input ([d2ea08c](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d2ea08c))
* create update get webinar completed ([d87ca32](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d87ca32))
* create update stream resolver ([b1e9005](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/b1e9005))
* create, update, remove handlers ([4cd47af](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/4cd47af))
* describe graphql input output ([b49ee72](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/b49ee72))
* done ([e8e0c46](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e8e0c46))
* draft ([35acab2](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/35acab2))
* draft ([2987c89](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/2987c89))
* Edit .gitlab-ci.yml ([8717d0e](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/8717d0e))
* Edit .gitlab-ci.yml ([80a3735](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/80a3735))
* enum mapper ([28cc4e9](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/28cc4e9))
* enum mapper ([0a0defa](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0a0defa))
* filter active records ([fbef21d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/fbef21d))
* first working test ([413dc0d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/413dc0d))
* fix ([8e803d7](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/8e803d7))
* fix ([ba4fa97](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/ba4fa97))
* fix and test registration integration ([3f9c6ed](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/3f9c6ed))
* fix build application ([a3e7bf2](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/a3e7bf2))
* fix button ([1fd2636](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/1fd2636))
* fix cards list result ([05e430a](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/05e430a))
* fix catalog filter ([b93c763](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/b93c763))
* fix check registration ([5f6643b](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/5f6643b))
* fix cover url ([208357a](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/208357a))
* fix cover url in cards ([0663875](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0663875))
* fix coverUrl ([cf714f0](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/cf714f0))
* fix createReadStream ([4554e58](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/4554e58))
* fix docker files names ([b5d858d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/b5d858d))
* fix get cards resolver ([9943683](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/9943683))
* fix get webinar ([4010c2f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/4010c2f))
* fix getting school uuid in resolver ([d95df5b](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d95df5b))
* fix getting webinar cards pipeline ([78a9f91](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/78a9f91))
* fix global validation ([01acda0](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/01acda0))
* fix health controller ([da160ce](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/da160ce))
* fix import ([2d22cad](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/2d22cad))
* fix imports in tests ([186b70a](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/186b70a))
* fix interfaces ([d36b9a4](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d36b9a4))
* fix join stream event handler invocation ([6466d36](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6466d36))
* fix join stream statistics ([35c5209](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/35c5209))
* fix join webinar ([d6beadf](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d6beadf))
* fix json format ([8b7ef4d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/8b7ef4d))
* fix merge ([f7bee10](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/f7bee10))
* fix notification mapping ([d950133](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d950133))
* fix notifications update ([297bb4f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/297bb4f))
* fix nullable ([14d0de2](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/14d0de2))
* fix pagination ([711005f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/711005f))
* fix pagination ([e4a2eee](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e4a2eee))
* fix record nullable ([be60e68](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/be60e68))
* fix resolver ([90bd3a9](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/90bd3a9))
* fix sort cards ([07f6754](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/07f6754))
* fix sort catalog ([861ec4a](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/861ec4a))
* fix stream test response ([b896303](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/b896303))
* fix stream update ([e724655](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e724655))
* fix stream update ([a2a3e46](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/a2a3e46))
* fix stream update input ([e0caa93](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e0caa93))
* fix test data ([d035489](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d035489))
* fix tests ([99e384c](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/99e384c))
* fix tests ([0b3b9f5](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0b3b9f5))
* fix tests ([7161b4b](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/7161b4b))
* fix tests and code ([efd38ec](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/efd38ec))
* fix total for webinar cards ([1ff7ec1](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/1ff7ec1))
* fix track handlers ([538e599](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/538e599))
* fix update of second notification ([ec93ebd](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/ec93ebd))
* fix update stream ([00611d7](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/00611d7))
* fix update with empty object ([c74ca1c](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/c74ca1c))
* fix upload ([6737835](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6737835))
* fix validation ([edb16e6](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/edb16e6))
* fixes ([6f9cf98](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6f9cf98))
* fixes ([facde1d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/facde1d))
* format error in resolver ([044f96f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/044f96f))
* get stream permissions ([c744ba8](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/c744ba8))
* get webinar and stream permission ([31f916e](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/31f916e))
* get webinar card view permission ([24d4409](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/24d4409))
* gitignore добавил исключение ([6f1f991](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6f1f991))
* graphql update create webinar ([8adc6ec](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/8adc6ec))
* http ([4dc524f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/4dc524f))
* init graphql subgraph ([b97e920](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/b97e920))
* init mutation in resolver ([925b61b](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/925b61b))
* init project with prisma ([be226b7](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/be226b7))
* init stream resolver ([913a131](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/913a131))
* init tests ([0cd99a3](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0cd99a3))
* install  graphql-upload ([a5c69a1](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/a5c69a1))
* install dependencies ([4a155c7](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/4a155c7))
* install golevelup lib ([818f96e](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/818f96e))
* install graphql dependencies ([96a99a2](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/96a99a2))
* install s3 lib ([b5bd8c2](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/b5bd8c2))
* invited ([7339537](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/7339537))
* join webinar refactored ([83a8935](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/83a8935))
* main ([fb8a4fd](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/fb8a4fd))
* make AmqpBrokerModule global ([e7e499c](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e7e499c))
* make cover optional ([fc04a10](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/fc04a10))
* make registration idempotent ([5d046fc](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/5d046fc))
* Merge branch 'amqp-integration' ([acb87b7](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/acb87b7))
* Merge branch 'api-prefix-webhook' ([b25bf01](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/b25bf01))
* Merge branch 'BUGPROD-411-has-record' into 'main' ([59f18ad](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/59f18ad))
* Merge branch 'ci-fix-2' into 'main' ([6ce0a79](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6ce0a79))
* Merge branch 'ci-fix-29-04-2025' into 'main' ([c35ef2d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/c35ef2d))
* Merge branch 'ci-fix' into 'main' ([8efb937](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/8efb937))
* Merge branch 'ci-fix' into 'main' ([bdc6a1a](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/bdc6a1a))
* Merge branch 'ci-fix' into 'main' ([4d0a1e4](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/4d0a1e4))
* Merge branch 'ci-fix' into 'main' ([3cf9ba2](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/3cf9ba2))
* Merge branch 'ci-fix' into 'main' ([def9e89](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/def9e89))
* Merge branch 'ci-fix' into 'main' ([411374c](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/411374c))
* Merge branch 'ci-fix' into 'main' ([dea2c35](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/dea2c35))
* Merge branch 'ci-fix' into 'main' ([ce36007](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/ce36007))
* Merge branch 'contract-integration' into development ([93960f6](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/93960f6))
* Merge branch 'development' into 'main' ([52aa8d5](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/52aa8d5))
* Merge branch 'development' into 'main' ([57891e8](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/57891e8))
* Merge branch 'feature/add-container-resources-deploy' into 'main' ([213077b](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/213077b))
* Merge branch 'feature/add-container-resources-deploy' into 'main' ([c1c512f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/c1c512f))
* Merge branch 'feature/TASK-134-live-digital-integration' into 'main' ([07117dd](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/07117dd))
* Merge branch 'file-storage' into development ([9c9f533](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/9c9f533))
* Merge branch 'fix-get-lib' into 'main' ([fe6c2ff](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/fe6c2ff))
* Merge branch 'fix-webinar-cards-search' into development ([70c7152](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/70c7152))
* Merge branch 'fixes' into 'main' ([f04a44e](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/f04a44e))
* Merge branch 'front-permissions' into development ([fb75e95](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/fb75e95))
* Merge branch 'hotfix/docker-compose' into 'main' ([87eccdb](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/87eccdb))
* Merge branch 'init-project' into development ([10c27b5](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/10c27b5))
* Merge branch 'join-webinar-refactoring' into development ([a45c77a](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/a45c77a))
* Merge branch 'join-webinar' into development ([bff0fec](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/bff0fec))
* Merge branch 'logger' into 'main' ([d4334d7](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d4334d7))
* Merge branch 'main' into ci-fix ([c98ec52](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/c98ec52))
* Merge branch 'message-validtion' into development ([4fff555](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/4fff555))
* Merge branch 'permissions' into development ([f48930c](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/f48930c))
* Merge branch 'pnpm' into 'main' ([01205bd](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/01205bd))
* Merge branch 'refactor-properties' into development ([38c249b](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/38c249b))
* Merge branch 'refactor' into 'main' ([f4a4a14](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/f4a4a14))
* Merge branch 'refactoring' into development ([b55204d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/b55204d))
* Merge branch 'refactoring' into development ([6f7b37f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6f7b37f))
* Merge branch 'statistics-integration' into development ([87561dc](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/87561dc))
* Merge branch 'student-registration' ([12a8cd8](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/12a8cd8))
* Merge branch 'test-init' into development ([c25b633](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/c25b633))
* Merge branch 'visibility' into 'main' ([ad009c3](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/ad009c3))
* Merge branch 'webinar-fix-patch' into 'main' ([99f74f7](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/99f74f7))
* Merge branch 'with-mock' into development ([e6096f0](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e6096f0))
* Merge remote-tracking branch 'origin/development' into development ([3611b01](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/3611b01))
* Merge remote-tracking branch 'origin/development' into development ([8d11f46](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/8d11f46))
* Merge remote-tracking branch 'origin/development' into filter-catalog ([b8062eb](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/b8062eb))
* Merge remote-tracking branch 'origin/development' into statistics-integration ([fb8fa6e](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/fb8fa6e))
* Merge remote-tracking branch 'origin/development' into statistics-integration ([57552f8](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/57552f8))
* mock user decorator for development ([7a2429b](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/7a2429b))
* notify analytics about stream ([91b1355](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/91b1355))
* organize structure of folders ([de8d487](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/de8d487))
* package.json ([ca6fba4](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/ca6fba4))
* path ([9a53b27](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/9a53b27))
* pnpm@9.15.4 ([ef2772b](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/ef2772b))
* prisma schema ([5a4aa30](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/5a4aa30))
* prisma schema relations ([66301be](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/66301be))
* queues bindings ([9cd4b4a](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/9cd4b4a))
* refactor code ([0c02748](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0c02748))
* refactor domain events ([acfcb55](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/acfcb55))
* refactor file upload ([13aec0a](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/13aec0a))
* refactor input params ([6dd4f7a](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6dd4f7a))
* refactor register student ([f131b23](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/f131b23))
* refactor sync data ([95c5bb1](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/95c5bb1))
* refactor sync user data ([02e614b](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/02e614b))
* refactor tests ([ab130a6](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/ab130a6))
* remove comments ([06689fa](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/06689fa))
* remove console log ([18e96a0](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/18e96a0))
* remove mock ([2bb526d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/2bb526d))
* rename ([e4973d4](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e4973d4))
* rename files ([1738b70](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/1738b70))
* rename publisher ([14f21ad](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/14f21ad))
* rename storage source ([816659e](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/816659e))
* rename webinar to external ([b529c30](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/b529c30))
* restore amqp mock ([eb534d9](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/eb534d9))
* save url in stream ([04611a5](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/04611a5))
* save url in webinar ([5ba3f83](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/5ba3f83))
* separate webinar pipeline ([83107d0](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/83107d0))
* set maxFileSize ([0a30b23](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0a30b23))
* set memory limits for containers ([9174ce6](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/9174ce6))
* some fixes ([d945e19](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d945e19))
* some fixes and refactoring ([9dc88b3](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/9dc88b3))
* Statistic Record ([279d430](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/279d430))
* student registered test ([6003482](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6003482))
* student tests ([1f28b05](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/1f28b05))
* temporary fix ([9ea1bb6](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/9ea1bb6))
* test ([e5fa1a8](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e5fa1a8))
* test settings ([8fa61dc](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/8fa61dc))
* test student resolver ([f86f1f1](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/f86f1f1))
* tests ([641369d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/641369d))
* Update .example.env ([499166c](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/499166c))
* Update .gitlab-ci.yml file ([ef43b5f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/ef43b5f))
* update contract ([e23ea2f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e23ea2f))
* update contracts ([5d0461c](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/5d0461c))
* update cpu limit from 0.05 to 0.5 ([6420158](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6420158))
* update create remove webinar permissions ([96430cf](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/96430cf))
* update enum presentation ([bb8540e](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/bb8540e))
* update lib ([d3ab13b](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d3ab13b))
* update lib ([70d00c9](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/70d00c9))
* update lib ([a9e7dd2](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/a9e7dd2))
* update mappers ([0ede390](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0ede390))
* update memory limit from 512Mi to 1Gi ([4b7fefa](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/4b7fefa))
* update test settings ([96c58ce](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/96c58ce))
* update test settings ([307d0d8](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/307d0d8))
* update with user classes ([da3cb0c](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/da3cb0c))
* use partial type in graphql ([bede776](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/bede776))
* user model ([a5b848f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/a5b848f))
* v 1.0.5 Поднял версию ([3a38ab6](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/3a38ab6))
* v.1.0.6 Фикс выдачи пустого списка в библиотеке ([5ef1018](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/5ef1018))
* v0.0.2 Запуск сервиса вебинаров ([9f2c318](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/9f2c318))
* v1.0.2 Отключил проверку капчи ([6c7e401](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6c7e401))
* v1.0.3 Добавил в карточки вебинаров тег видимости ([ecf5ba9](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/ecf5ba9))
* v1.0.4 -  Фиксы для вебинаров ([0f2af00](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0f2af00))
* v1.0.6 Логгер, трассировка и роуты проверки сервиса ([f825912](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/f825912))
* v1.1.0 Релиз интеграции LiveDigital ([989ab9f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/989ab9f))
* value objects ([a42ba38](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/a42ba38))
* version ([27f628c](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/27f628c))
* view webinar permission ([ed9b0fe](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/ed9b0fe))
* webinar commands ([6666d05](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6666d05))
* webinar queries ([2f23b68](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/2f23b68))
* webinar tests are working ([617b9e2](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/617b9e2))
* webinar types ([7cf9c27](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/7cf9c27))
* working application ([fb5c731](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/fb5c731))
* working build ([9203401](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/9203401))
* working webinar part ([14de62d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/14de62d))
* доступы ([1fc33d9](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/1fc33d9))
* Загрузка ([8f69255](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/8f69255))
* Загрузка ([49a24ed](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/49a24ed))
* Загрузка s3 ([7790385](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/7790385))
* Загрузка файлов ([d41bba3](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d41bba3))
* Загрузка файлов ([b5f32da](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/b5f32da))
* Картинки ([57a63bf](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/57a63bf))
* Картинки ([8dd067f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/8dd067f))
* Лог ([46b5539](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/46b5539))
* Лог ([6780dca](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6780dca))
* Обновки ([b80eaf4](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/b80eaf4))
* Обновки ([46c1d3e](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/46c1d3e))
* Обновки ([9bdd74a](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/9bdd74a))
* Обновки ([f602196](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/f602196))
* Обновки ([1825535](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/1825535))
* Обновки ([c212b05](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/c212b05))
* Обновки ([bf85917](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/bf85917))
* получение вебинаров ([0e23118](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0e23118))
* получение вебинаров ([265c4f9](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/265c4f9))
* получение вебинаров ([36be6b7](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/36be6b7))
* получение вебинаров ([8b3b359](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/8b3b359))
* получение вебинаров ([21076d3](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/21076d3))
* получение вебинаров ([10563e4](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/10563e4))
* релиз ([98bd180](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/98bd180))
* релиз ([287af04](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/287af04))
* релизg ([9bc8c5e](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/9bc8c5e))
* схема ([c7c9ef4](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/c7c9ef4))
* схема ([ce09a14](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/ce09a14))
* схема ([868a963](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/868a963))
* схема ([111a0f1](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/111a0f1))
* схема ([6e44c8e](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6e44c8e))
* схема ([42c3b21](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/42c3b21))
* схема ([d5730c5](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d5730c5))
* схема ([36cf0a8](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/36cf0a8))
* схема ([492cc2e](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/492cc2e))
* схема ([8cc471b](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/8cc471b))
* фикс ([8d3955e](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/8d3955e))
* Фиксы ([a6ca5bd](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/a6ca5bd))
* Фиксы ([722ce18](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/722ce18))
* Фиксы ([ae4548a](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/ae4548a))
* fix(ci): добавлен новый stage для проверки работы Docker-in-Docker в .gitlab-ci.yml и обновлены наст ([33f2981](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/33f2981))
* fix(ci): добавлен новый stage для тестирования с использованием Docker-in-Docker в .gitlab-ci.yml, о ([dd479c6](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/dd479c6))
* fix(ci): добавлены настройки Docker в .gitlab-ci для тестирования и обновлены зависимости ([da211a0](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/da211a0))
* fix(ci): добавлены правила для запуска тестов в .gitlab-ci.yml и сделаны изменения в зависимости от  ([51ddda0](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/51ddda0))
* fix(ci): обновлены версии Alpine в .gitlab-ci.yml для улучшения совместимости с Docker ([4a3fcf7](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/4a3fcf7))
* fix(ci): обновлены версии PNPM и Docker в .gitlab-ci.yml для улучшения совместимости и упрощения кон ([ee16a60](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/ee16a60))
* fix(ci): обновлены зависимости между этапами в .gitlab-ci.yml для улучшения последовательности выпол ([4e9e217](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/4e9e217))
* fix(ci): обновлены настройки Docker в .gitlab-ci.yml для улучшения тестирования и добавлены новые пе ([fd14a92](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/fd14a92))
* fix(ci): обновлены настройки Docker в .gitlab-ci.yml для упрощения конфигурации и улучшения доступно ([26e7083](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/26e7083))
* fix(ci): обновлены настройки Docker в .gitlab-ci.yml и добавлен файл конфигурации для Jest ([30cd0c2](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/30cd0c2))
* fix(ci): обновлены настройки Docker в .gitlab-ci.yml и удален файл конфигурации jest.env.ts для упро ([39d41cb](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/39d41cb))
* fix(ci): обновлены настройки Docker в .gitlab-ci.yml, изменен способ подключения к Docker и улучшены ([4d2ffc6](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/4d2ffc6))
* fix(ci): обновлены настройки Docker в .gitlab-ci.yml, исправлен путь к Docker и улучшена конфигураци ([0f7029f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0f7029f))
* fix(ci): обновлены настройки Docker и добавлен новый stage для тестирования с использованием Docker  ([7a777d6](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/7a777d6))
* fix(ci): обновлены настройки тестирования в .gitlab-ci.yml, добавлены проверки работы Docker и устан ([471d377](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/471d377))
* fix(ci): обновлены настройки тестирования в .gitlab-ci.yml, изменен образ на Node.js и упрощены кома ([f065e6e](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/f065e6e))
* fix(ci): обновлены настройки тестов в GitLab CI для использования Docker вместо зависимостей Alpine ([e7b058b](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e7b058b))
* fix(ci): удалена проверка доступности Docker из .gitlab-ci.yml для упрощения конфигурации ([e327613](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e327613))
* fix(ci): удалены закомментированные строки и упрощены настройки тестирования в .gitlab-ci.yml для по ([35bae16](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/35bae16))
* fix(ci): удалены лишние переменные окружения из конфигурации Docker в .gitlab-ci.yml для упрощения н ([6b061b3](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6b061b3))
* fix(ci): упрощены настройки CI в .gitlab-ci.yml, удалены лишние stages и добавлены новые зависимости ([7dd1c70](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/7dd1c70))
* fix(ci): упрощены настройки Docker в .gitlab-ci.yml и добавлены новые переменные для тестирования ([cdf42d6](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/cdf42d6))
* fix(ci): упрощены настройки Docker в .gitlab-ci.yml, добавлены задержки для инициализации демона и о ([4e9567c](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/4e9567c))
* fix: fix ([bc02a5d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/bc02a5d))
* fix: fix ([b670c11](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/b670c11))
* fix(tests): заменён PrismaClient на сервис PrismaService в тестовой настройке интеграции ([7e60637](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/7e60637))
* fix(webinar): исправил вебхук для dev ([bbb6c08](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/bbb6c08))
* fix(webinar): исправил контроллер вебхука ([511080d](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/511080d))
* fix(webinar): исправил контроллер вебхука ([89a825f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/89a825f))
* fix(webinar): обновлен .gitlab-ci для поддержки переменных и корректной настройки окружения ([7be6ca4](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/7be6ca4))
* fix: добавлена команда prisma:gen в pipeline для генерации схемы ([3669f12](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/3669f12))
* fix: добавлены конфигурации eslint, prettier, husky, commitlint и CI/CD для webinar-service ([aaa4ed3](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/aaa4ed3))
* fix: закомментирована секция needs в release в .gitlab-ci.yml ([0a0a540](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0a0a540))
* fix: исправлены значения переменных в .gitlab-ci.yml для корректного окружения ([6fb75f3](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6fb75f3))
* fix: исправлены комментарии и разкомментированы шаги сборки в .gitlab-ci.yml ([211b2a0](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/211b2a0))
* fix: обновлен скрипт test и добавлен новый скрипт test1 в package.json ([0be27af](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0be27af))
* fix: обновлен токен аутентификации в .gitlab-ci.yml для работы с GitLab Registry ([bdd6fb6](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/bdd6fb6))
* fix: обновлена версия node до 20 в .gitlab-ci.yml ([4f25c2f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/4f25c2f))
* fix: обновлена секция needs в .gitlab-ci.yml для задания build-check ([6623ad2](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/6623ad2))
* fix: чек ([cacb782](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/cacb782))
* fix: чек ([93c78a3](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/93c78a3))
* fix: чек ([bfecce4](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/bfecce4))
* feat: 123 ([325c7c2](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/325c7c2))
* feat: test 123 ([e4eb356](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e4eb356))
* feat: test123 ([094ce58](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/094ce58))
* feat: test123 ([2374360](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/2374360))
* feat: test123123 ([bc3750f](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/bc3750f))
* feat: test123123123 ([5c77792](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/5c77792))
* feat: test123123123123 ([f485816](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/f485816))
* feat(webinar): добавлен .env.test и обновлена конфигурация для тестов в .gitlab-ci.yml ([125aee6](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/125aee6))
* feat: добавлена копия .env.example в .env в pipeline для webinar ([5652806](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/5652806))
* feat: обновлены зависимости на модули skillspace и удалены устаревшие библиотеки, добавлены примеры  ([66f5f11](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/66f5f11))
* test: переработаны тесты интеграции для улучшения читаемости и форматирования кода ([038466a](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/038466a))
* chore(deps): добавлены переменные окружения в GitLab CI и обновлена версия amqp-contracts ([27ab939](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/27ab939))
* chore(deps): обновлена версия @skillspace/amqp-contracts до 2.6.0 и исправлен скрипт check-app ([e2b3cc6](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/e2b3cc6))
* chore: добавлена копия .env.example в .env в CI конфигурацию ([ddc16c0](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/ddc16c0))
* ci: добавлен запуск команды prisma:gen в pipeline ([211f4bc](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/211f4bc))
* ci: добавлена команда генерации prisma в GitLab CI ([51e2d97](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/51e2d97))
* ci: настройки для ci ([012f0f5](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/012f0f5))
* ci: настройки для ci ([d151f43](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/d151f43))
* ci: настройки для ci ([570e113](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/570e113))
* ci: оптимизировал код LiveDigital client, добавил NODE_ENV в тестовую сборку ([0ff87f3](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/0ff87f3))
* refactor: отформатирован код в core.module.ts и обновлен package.json ([2e1ba28](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/2e1ba28))
* draft: remove stream and webinar event handlers ([cb07514](https://gitlab.sksp.site/skillspace/microservices/webinars-service/commit/cb07514))

# 1.1.0
#### 15.04.2025

- функциональность встроенных вебинаров

# 1.0.9
#### 12.03.2025

- фикс сортировки и кнопки в уведомлении

# 1.0.8
#### 12.02.2025

1.pnpm

# 1.0.7
#### 28.01.2025

1. Обновил пакеты логирования и трассировки
2. Разделил роуты для проверки сервиса

# 1.0.6
#### 21.01.2025

1. Фикс бага с выдачей пустого списка в библиотеке

# 1.0.5
#### 21.01.2025

1. Поднял версию

# 1.0.4
#### 16.01.2025

1. Фикс бага с отображением доступности записи вебинара
2. Отключил пагинацию
3. Фикс бага с выдачей пустого списка при поиске в каталоге

# 1.0.3
#### 27.12.2024

1. Добавил в карточки вебов свойство visibility

# 1.0.2
#### 27.12.2024

1. Отключил проверку капчи

# 1.0.1
#### 27.12.2024

Фикс docker-compose.yaml

# 1.0.0
#### 25.12.2024

Релиз
