import '@skillspace/tracing';

import { NestFactory } from '@nestjs/core';
import { createLogger, LoggerService } from '@skillspace/logger';

import { AppModule } from './app.module';
import { setupGlobalMiddlewares } from './bootstrap-setup';
import { appConfig } from './configs/app.config';

async function bootstrap() {
    const app = await NestFactory.create(AppModule, { logger: await createLogger() });
    setupGlobalMiddlewares(app);

    await app.listen(appConfig.port);

    const logger = app.get(LoggerService);
    logger.log(`Webinars service started on port ${appConfig.port}: `);
}

void bootstrap();
