import { ValidationError } from 'class-validator';

export function getValidationErrorMessages(errors: ValidationError[]): string[] {
    return errors.reduce((messages: string[], error: ValidationError) => {
        if (error.constraints) {
            const constraintValues = Object.values(error.constraints);
            messages.push(...constraintValues);
        }
        if (error.children) {
            messages.push(...getValidationErrorMessages(error.children));
        }
        return messages;
    }, []);
}
