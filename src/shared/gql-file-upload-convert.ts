import { FileUpload } from '@skillspace/graphql';

export interface IFileUpload {
    filename: string;
    mimetype: string;
    encoding: string;
    buffer: Buffer;
}

export class GetFileUploadConvert {
    static async convert(fileUploads: FileUpload[]): Promise<IFileUpload[]> {
        // console.log(fileUploads, fileUploads.length)

        return Promise.all(
            fileUploads.map(async (file) => {
                const stream = file.createReadStream();
                const chunks: Buffer[] = [];

                await new Promise<Buffer>((resolve, reject) => {
                    stream.on('data', (chunk) => {
                        if (chunk instanceof Buffer) {
                            chunks.push(chunk);
                        }
                    });

                    stream.on('end', () => {
                        resolve(Buffer.concat(chunks));
                    });

                    stream.on('error', reject);
                });

                const buffer = Buffer.concat(chunks);

                return {
                    filename: file.filename,
                    mimetype: file.mimetype,
                    encoding: file.encoding,
                    buffer,
                };
            }),
        );
    }
}
