import { SaasProviderEnum } from '@prisma/client';

import { liveDigitalConfig } from '../webinars/infrastructure/adapters/saas/live-digital/live-digital.config';

export interface IRoomConfig {
    provider: SaasProviderEnum;
    spaceId: string;
    storageProvider: SaasProviderEnum;
}

export const roomConfig: IRoomConfig = {
    provider: SaasProviderEnum.LiveDigital,
    spaceId: liveDigitalConfig.spaceId,
    storageProvider: SaasProviderEnum.Kinescope,
};
