import { Inject, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { StorageStateEnum } from '@prisma/client';
import { CustomError, ERROR_CODE } from '@skillspace/utils';

import { UserFactory } from '../../domain/models/user/user.factory';
import { WebinarUserParams } from '../../domain/models/user/user-abstract';
import { StreamId } from '../../domain/objects/stream-id';
import { IStreamRepository } from '../../domain/repositories/stream-repository.interface';
import { IWebinarRepository } from '../../domain/repositories/webinar-repository.interface';
import { STREAM_REPOSITORY, VIDEO_STORAGE_ADAPTER, WEBINAR_REPOSITORY } from '../../injects';
import { IVideoStorageAdapter } from '../adapters/saas/video-storage-adapter.interface';
import { VideoLinkResult } from './result/get-video-link.result';

export class GetVideoLinkQuery implements IQuery {
    constructor(
        public readonly params: {
            streamId: string;
            userParams: WebinarUserParams;
        },
    ) {}
}

@QueryHandler(GetVideoLinkQuery)
export class GetVideoLinkHandler implements IQueryHandler<GetVideoLinkQuery> {
    private readonly logger = new Logger(GetVideoLinkHandler.name);

    constructor(
        @Inject(STREAM_REPOSITORY)
        private streamRepo: IStreamRepository,
        @Inject(WEBINAR_REPOSITORY)
        private webinarRepo: IWebinarRepository,
        @Inject(VIDEO_STORAGE_ADAPTER)
        private videoStorage: IVideoStorageAdapter,
    ) {}

    async execute(query: GetVideoLinkQuery): Promise<VideoLinkResult> {
        const { streamId, userParams } = query.params;

        const anUser = UserFactory.create(userParams);
        const aStreamId = StreamId.create(streamId);

        const aStream = await this.streamRepo.getStream(aStreamId, anUser.params.email);

        if (!aStream) {
            throw new CustomError('Поток вебинара не найден при запросе ссылки на видео', {
                code: ERROR_CODE.NOT_FOUND_ERROR,
                details: { streamId: aStreamId.unwrap() },
            });
        }

        if (!aStream.isInternal) {
            throw new CustomError('Скачивание видео поддерживается только для интегрированных вебинаров', {
                code: ERROR_CODE.BAD_REQUEST_ERROR,
                details: { streamId },
            });
        }

        const aWebinar = await this.webinarRepo.getWebinar(aStream.aWebinarId);

        if (!(anUser.isEmployee() && aWebinar.schoolUuid === anUser.params.schoolUuid)) {
            throw new CustomError('Недостаточно прав для получения ссылки на видео', {
                code: ERROR_CODE.FORBIDDEN_ERROR,
                details: { user: anUser.toLog() },
            });
        }

        const storageState = aStream.params.storageState;
        if (storageState !== StorageStateEnum.finished) {
            throw new CustomError('Видео находится в процессе обработки', {
                code: ERROR_CODE.BAD_REQUEST_ERROR,
                details: { streamId },
            });
        }

        this.logger.log( { streamId, embed: aStream.embedRecordUrl }, 'Запрашиваем ссылку на скачивание видео');

        const downloadLink = await this.videoStorage.getDownloadVideoLink(aStream);
        return new VideoLinkResult(downloadLink);
    }
}
