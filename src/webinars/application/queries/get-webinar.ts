import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHand<PERSON> } from '@nestjs/cqrs';

import { CustomError, ERROR_CODE } from '../../../shared/webinar-service.error';
import { WebinarUser } from '../../domain/models/user/user-abstract';
import { WebinarId } from '../../domain/objects/webinar-id';
import { IWebinarRepository } from '../../domain/repositories/webinar-repository.interface';
import { WEBINAR_REPOSITORY } from '../../injects';
import { WebinarQueryResult } from './result/webinar-query.result';

export class GetWebinarQuery implements IQuery {
    constructor(
        public readonly anUser: WebinarUser,
        public readonly webinarId: string,
    ) {}
}

@QueryHandler(GetWebinarQuery)
export class GetWebinarHandler implements IQueryHandler<GetWebinarQuery> {
    constructor(
        @Inject(WEBINAR_REPOSITORY)
        private webinarRepo: IWebinarRepository,
    ) {}

    async execute(query: GetWebinarQuery): Promise<WebinarQueryResult> {
        const aWebinarId = WebinarId.create(query.webinarId);
        const webinarId = aWebinarId.unwrap();
        const anUser = query.anUser;

        const aWebinar = await this.webinarRepo.getWebinar(aWebinarId);
        if (!aWebinar) {
            throw new CustomError('Вебинар не найден', {
                code: ERROR_CODE.NOT_FOUND_ERROR,
                details: { webinarId },
            });
        }

        if (!anUser.hasPermissionToView(aWebinar)) {
            throw new CustomError('Недостаточно прав для просмотра вебинара', {
                code: ERROR_CODE.FORBIDDEN_ERROR,
                details: {
                    webinarId,
                    user: anUser.toLog(),
                },
            });
        }

        return new WebinarQueryResult(anUser, aWebinar);
    }
}
