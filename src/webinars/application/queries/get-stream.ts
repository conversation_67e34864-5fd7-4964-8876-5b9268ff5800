import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ry<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';

import { UserFactory } from '../../domain/models/user/user.factory';
import { WebinarUserParams } from '../../domain/models/user/user-abstract';
import { StreamId } from '../../domain/objects/stream-id';
import { IParticipantRepository } from '../../domain/repositories/participant-repository.interface';
import { IStreamRepository } from '../../domain/repositories/stream-repository.interface';
import { IWebinarRepository } from '../../domain/repositories/webinar-repository.interface';
import { PARTICIPANT_REPOSITORY, STREAM_REPOSITORY, WEBINAR_REPOSITORY } from '../../injects';
import { StreamQueryResult } from './result/stream-query.result';

export class GetStreamQuery implements IQuery {
    constructor(
        public readonly userParams: WebinarUserParams,
        public readonly aStreamId: StreamId,
    ) {}
}

@QueryHandler(GetStreamQuery)
export class GetStreamHandler implements IQueryHandler<GetStreamQuery> {
    constructor(
        @Inject(PARTICIPANT_REPOSITORY)
        private participantRepo: IParticipantRepository,
        @Inject(STREAM_REPOSITORY)
        private streamRepo: IStreamRepository,
        @Inject(WEBINAR_REPOSITORY)
        private webinarRepo: IWebinarRepository,
    ) {}

    async execute(query: GetStreamQuery): Promise<StreamQueryResult> {
        const { aStreamId, userParams: userCreateParams } = query;

        const anUser = UserFactory.create(userCreateParams);
        const aStream = await this.streamRepo.getStream(aStreamId, anUser.params.email);
        const studentsCount = await this.participantRepo.getStudentCountsForStream(aStreamId);
        const aWebinarParams = await this.webinarRepo.getWebinarParams(aStream.aWebinarId);

        return new StreamQueryResult(
            anUser,
            aWebinarParams,
            {
                ...aStream.params,
                // ...studentsCount,
                id: aStream.anId.unwrap(),
                date: aStream.params.date,
                participants: [...aStream.params.participants, ...aStream.students],
            },
            studentsCount,
        );
    }
}
