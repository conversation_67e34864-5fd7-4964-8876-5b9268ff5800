import { IQueryResult } from '@nestjs/cqrs';

export interface IStudentRegistrationParams {
    uuid?: string;
    email: string;
    name?: string;
    timezone?: string;
    phoneNumber?: string;
}

export class StudentRegistrationQueryResult implements IQueryResult {
    public readonly uuid?: string;
    public readonly email: string;
    public readonly name?: string;
    public readonly timezone?: string;
    public readonly phoneNumber?: string;

    constructor(student: IStudentRegistrationParams) {
        this.uuid = student?.uuid;
        this.email = student.email;
        this.name = student?.name;
        this.timezone = student?.timezone;
        this.phoneNumber = student?.phoneNumber;
    }
}
