import { IQueryResult } from '@nestjs/cqrs';
import { ParticipantRole, StreamType, WebinarVisibility } from '@prisma/client';

import { RoomParams, RoomStateEnum } from '../../../domain/models/stream/stream-room';
import { WebinarUser } from '../../../domain/models/user/user-abstract';
import { CoverImage, UploadedImageParams } from '../../../domain/objects/cover-image';
import {
    mapToWebinarVisibilityPresentation,
    WebinarVisibilityPresentation,
} from '../../../presentation/enum.presentation';

export interface IWebinarCardQueryResultParams {
    streamType: StreamType;
    webinarId: string;
    title: string;
    description: string;
    coverImage: UploadedImageParams | null;
    visibility: WebinarVisibility;
    isPhoneRequiredOnRegistration: boolean;

    streamId: string;
    date: number;
    duration: number;

    recordUrl: string;
    room: RoomParams;
    savedRecordUrl: string;

    isRegistered: boolean;
    isSpeaker: boolean;
    isViewed: boolean;

    participants: {
        userUuid: string;
        email?: string;
        role: ParticipantRole;
    }[];
}

export class WebinarCardQueryResult implements IQueryResult {
    webinarId: string;
    title: string;
    description: string;
    coverUrl: string;
    visibility: WebinarVisibilityPresentation;
    isPhoneRequiredOnRegistration: boolean;

    streamId: string;
    date: number;
    duration: number;
    hasRecord: boolean;

    isRegistered: boolean;
    isSpeaker: boolean;
    isViewed: boolean;

    isAllowedToJoin: boolean;
    isLive: boolean;

    constructor(anUser: WebinarUser, card: IWebinarCardQueryResultParams) {
        const currentDate = Date.now();
        const startDate = card.date;
        const isLive =
            card.streamType === StreamType.INTERNAL
                ? card.room.state === RoomStateEnum.started
                : startDate < currentDate && currentDate < startDate + card.duration;

        this.webinarId = card.webinarId;
        this.title = card.title;
        this.description = card.description;
        this.visibility = mapToWebinarVisibilityPresentation(card.visibility);
        this.isPhoneRequiredOnRegistration = card.isPhoneRequiredOnRegistration;
        this.coverUrl = card?.coverImage ? CoverImage.from({ coverImage: card.coverImage }).url : null;
        this.streamId = card.streamId;
        this.date = card.date;
        this.duration = card.duration;
        this.isRegistered = card.isRegistered;
        this.isSpeaker = card.isSpeaker;
        this.isViewed = !!card.isViewed;
        this.hasRecord = !!card.recordUrl || !!card.savedRecordUrl;
        this.isAllowedToJoin = anUser.isAllowedToJoinStream({ ...card, date: new Date(card.date) });
        this.isLive = isLive;
    }
}
