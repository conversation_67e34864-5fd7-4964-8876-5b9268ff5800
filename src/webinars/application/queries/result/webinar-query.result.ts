import { IQueryResult } from '@nestjs/cqrs';

import { WebinarUser } from '../../../domain/models/user/user-abstract';
import { WebinarModel } from '../../../domain/models/webinar/webinar.model';
import { CoverImage } from '../../../domain/objects/cover-image';
import {
    mapToRegistrationTypePresentation,
    mapToWebinarTypePresentation,
    mapToWebinarVisibilityPresentation,
    RegistrationTypePresentation,
    WebinarTypePresentation,
    WebinarVisibilityPresentation,
} from '../../../presentation/enum.presentation';
import { StreamQueryResult } from './stream-query.result';

export class WebinarQueryResult implements IQueryResult {
    id: string;
    type: WebinarTypePresentation;
    schoolUuid: string;
    createdBy: string;
    title: string;
    description: string;
    coverUrl: string;
    useAttendancePoints: boolean;
    attendancePoints: number;
    useNotifications: boolean;
    studentNotifyFirst: number;
    studentNotifySecond: number;
    visibility: WebinarVisibilityPresentation;
    enableStudentLimit: boolean;
    maxStudentCapacity: number;
    // registration
    registrationType: RegistrationTypePresentation;
    registrationForEntry: boolean;
    registrationForView: boolean;
    isPhoneRequiredOnRegistration: boolean;
    // landing
    useDefaultLanding: boolean;
    externalLandingUrl?: string;
    integrationCode: string;
    // calculated
    countRegistered: number;
    countViewed: number;
    // permissions
    canModify: boolean;
    canRemove: boolean;

    streams: StreamQueryResult[];

    constructor(anUser: WebinarUser, aWebinar: WebinarModel) {
        this.id = aWebinar.id;
        this.type = mapToWebinarTypePresentation(aWebinar.params.type);
        this.schoolUuid = aWebinar.params.schoolUuid;
        this.createdBy = aWebinar.params.createdBy;
        this.title = aWebinar.params.title;
        this.description = aWebinar.params.description;
        this.coverUrl = CoverImage.from({
            coverImage: aWebinar.params.coverImage,
        }).url;
        this.useAttendancePoints = aWebinar.params.useAttendancePoints;
        this.attendancePoints = aWebinar.params.attendancePoints;
        this.useNotifications = aWebinar.params.useNotifications;
        this.studentNotifyFirst = aWebinar.params.notifications[0];
        this.studentNotifySecond = aWebinar.params.notifications[1];
        this.visibility = mapToWebinarVisibilityPresentation(aWebinar.params.visibility);
        this.enableStudentLimit = aWebinar.params.enableStudentLimit;
        this.maxStudentCapacity = aWebinar.params.maxStudentCapacity;
        // registration
        this.registrationType = mapToRegistrationTypePresentation(aWebinar.params.registrationType);
        this.registrationForEntry = aWebinar.params.registrationForEntry;
        this.registrationForView = aWebinar.params.registrationForView;
        this.isPhoneRequiredOnRegistration = aWebinar.params.isPhoneRequiredOnRegistration;
        // landing
        this.useDefaultLanding = aWebinar.params.useDefaultLanding;
        this.externalLandingUrl = aWebinar.params.externalLandingUrl ?? '';
        this.integrationCode = aWebinar.params.integrationCode ?? '';
        // calculated
        this.countRegistered = aWebinar.params.streams.reduce((acc, stream) => acc + stream.countRegistered, 0);
        this.countViewed = aWebinar.params.streams.reduce((acc, stream) => acc + stream.countViewed, 0);
        // permissions
        this.canModify = anUser.hasPermissionToModify(aWebinar);
        this.canRemove = anUser.hasPermissionToRemove(aWebinar);

        this.streams = aWebinar.params.streams
            .map((streamParams) => new StreamQueryResult(anUser, aWebinar, streamParams))
            .sort((a, b) => b.date - a.date);
    }
}
