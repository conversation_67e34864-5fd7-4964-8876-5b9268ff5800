import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ParticipantRole } from '@prisma/client';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';
import { StreamId } from '../../domain/objects/stream-id';
import { StudentRegistrationQueryResult } from './result/student-registration.result';

export class CheckRegistrationQuery implements IQuery {
    constructor(
        public readonly streamId: StreamId,
        public readonly userEmail: string,
    ) {}
}

@QueryHandler(CheckRegistrationQuery)
export class CheckRegistrationQueryHandler implements IQueryHandler<CheckRegistrationQuery> {
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
    ) {}

    async execute(query: CheckRegistrationQuery): Promise<StudentRegistrationQueryResult> {
        const student = await this.prisma.participant.findFirst({
            where: {
                streamId: query.streamId.unwrap(),
                email: query.userEmail,
                role: ParticipantRole.STUDENT,
            },
            select: {
                userUuid: true,
                email: true,
                name: true,
                timezone: true,
                phoneNumber: true,
            },
        });
        return student
            ? new StudentRegistrationQueryResult({
                  uuid: student.userUuid,
                  email: student.email,
                  name: student.name,
                  timezone: student.timezone,
                  phoneNumber: student.phoneNumber,
              })
            : null;
    }
}
