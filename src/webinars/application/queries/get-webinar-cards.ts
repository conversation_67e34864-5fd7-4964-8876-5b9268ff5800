import { Inject, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ParticipantRole, RegistrationType, StreamType, WebinarVisibility } from '@prisma/client';
import { InputJsonValue } from '@prisma/client/runtime/library';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';
import { RoomParams } from '../../domain/models/stream/stream-room';
import { WebinarUser } from '../../domain/models/user/user-abstract';
import { UploadedImageParams } from '../../domain/objects/cover-image';
import { SchoolUuid } from '../../domain/objects/school-uuid';
import { CardsViewMode } from '../../presentation/enum.presentation';
import { WebinarCardsFilter } from '../../presentation/webinar.resolver';
import { <PERSON><PERSON>atedResult } from './result/paginated.result';
import { IWebinarCardQueryResultParams, WebinarCardQueryResult } from './result/webinar-card-query.result';

interface StreamAggregatedData {
    // webinar
    webinarId: string;
    title: string;
    description: string;
    coverImage: UploadedImageParams | null;
    visibility: WebinarVisibility;
    isPhoneRequiredOnRegistration: boolean;
    // stream
    streamType: StreamType;
    streamId: string;
    date: number;
    duration: number;
    recordUrl: string;
    room: RoomParams;
    savedRecordUrl: string;
    // participant
    userUuid: string;
    role: ParticipantRole;
    isViewed: boolean;
}

export class GetWebinarCardsQuery implements IQuery {
    constructor(
        public readonly anUser: WebinarUser,
        public readonly filter: WebinarCardsFilter,
        public readonly pagination,
        public readonly currentDate = new Date(),
    ) {}
}

@QueryHandler(GetWebinarCardsQuery)
export class GetWebinarCardsHandler implements IQueryHandler<GetWebinarCardsQuery> {
    private readonly logger = new Logger(GetWebinarCardsHandler.name);
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
    ) {}

    async execute(query: GetWebinarCardsQuery): Promise<PaginatedResult<WebinarCardQueryResult>> {
        if (query.filter.mode === CardsViewMode.library) {
            return this.getLibrary(query);
        }

        return this.getCatalog(query);
    }

    /**
     * БИБЛИОТЕКА
     */

    private async getLibrary(query: GetWebinarCardsQuery): Promise<PaginatedResult<WebinarCardQueryResult>> {
        const anUser = query.anUser;

        const result = await this.prisma.participant.aggregateRaw({
            pipeline: [
                ...this.getParticipantStreamsForLibraryPipeline(query),
                ...this.getPaginatedResultPipeline(query),
            ],
        });

        if (!result || result.length === 0) {
            return {
                data: [],
                total: 0,
            };
        }

        const typedResult = result[0] as unknown as PaginatedResult<StreamAggregatedData>;
        const mappedData = typedResult.data.map((stream) => {
            const isSpeaker = stream.role === ParticipantRole.SPEAKER;
            const isRegistered = stream.role === ParticipantRole.STUDENT;
            return {
                ...stream,
                date: stream.date,
                isRegistered,
                isSpeaker,
                isViewed: stream.isViewed,
                participants: isSpeaker
                    ? []
                    : [{ userUuid: stream.userUuid, role: stream.role, email: anUser.params.email }],
            };
        });

        // console.log(mappedData.map((d) => d.date));

        if (mappedData.length === 0) {
            return {
                data: [],
                total: 0,
            };
        }

        return {
            data: mappedData.map((webinar) => new WebinarCardQueryResult(anUser, webinar)),
            total: typedResult.total,
        };
    }

    /**
     * КАТАЛОГ
     */

    private async getCatalog(query: GetWebinarCardsQuery): Promise<PaginatedResult<WebinarCardQueryResult>> {
        const anUser = query.anUser;
        const schoolUuid = SchoolUuid.create(anUser.params.schoolUuid).unwrap();

        const preConditions = anUser.isEmployee()
            ? { schoolUuid }
            : {
                  schoolUuid,
                  visibility: WebinarVisibility.PUBLIC,
                  registrationType: { $ne: RegistrationType.INVITATION },
              };

        const postConditions: {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            $and: Record<string, any>[];
        } = {
            $and: [],
        };

        if (!anUser.hasPermissionToView()) {
            postConditions.$and.push({
                $or: [{ visibility: WebinarVisibility.PUBLIC }, { participant: { $ne: null } }],
            });
        }

        // Добавляем условие поиска, если оно задано
        const search = query?.filter?.search;
        if (search) {
            postConditions.$and.push({
                title: {
                    $regex: search,
                    $options: 'i',
                },
            });
        }

        // Если массив $and остался пустым, не используем фильтрацию
        const postFilter = postConditions.$and.length === 0 ? [] : [{ $match: postConditions }];

        const mapToCard = [
            {
                $addFields: {
                    activeStreams: { $slice: ['$activeStreams', 1] },
                    pastStreams: { $slice: ['$pastStreams', -1] },
                    stream: {
                        $cond: {
                            if: { $gt: [{ $size: '$activeStreams' }, 0] },
                            then: { $arrayElemAt: ['$activeStreams', 0] },
                            else: { $arrayElemAt: ['$pastStreams', 0] },
                        },
                    },
                },
            },
            {
                $addFields: {
                    streamType: '$stream.streamType',
                    webinarId: '$stream.webinarId',
                    streamId: '$stream.streamId',
                    streamTitle: '$stream.title',
                    date: '$stream.date',
                    duration: '$stream.duration',
                    recordUrl: '$stream.recordUrl',
                    room: '$stream.room',
                    savedRecordUrl: '$stream.savedRecordUrl',
                },
            },
            {
                $project: {
                    _id: 0,
                    streamType: 1,
                    title: 1,
                    visibility: 1,
                    streamTitle: 1,
                    description: 1,
                    coverImage: 1,
                    webinarId: 1,
                    streamId: 1,
                    date: 1,
                    duration: 1,
                    recordUrl: 1,
                    room: 1,
                    savedRecordUrl: 1,
                    isPhoneRequiredOnRegistration: 1,
                },
            },
        ];

        const getUserStatus = anUser.isGuest()
            ? [
                  {
                      $addFields: {
                          participants: [], // Гость — сразу пустой массив
                          participant: null, // Устанавливаем `participant` как null
                      },
                  },
              ]
            : [
                  {
                      $lookup: {
                          from: 'participants',
                          let: {
                              localStreamId: { $toObjectId: '$streamId' },
                              userUuid: anUser.params.id,
                          },
                          pipeline: [
                              {
                                  $match: {
                                      $expr: {
                                          $and: [
                                              {
                                                  $eq: ['$streamId', '$$localStreamId'],
                                              },
                                              {
                                                  $eq: ['$userUuid', '$$userUuid'],
                                              },
                                          ],
                                      },
                                  },
                              },
                              {
                                  $project: {
                                      _id: 0,
                                      userUuid: 1,
                                      email: 1,
                                      role: 1,
                                      isViewed: 1,
                                  },
                              },
                          ],
                          as: 'participants',
                      },
                  },
                  {
                      $addFields: {
                          participant: {
                              $ifNull: [{ $arrayElemAt: ['$participants', 0] }, null],
                          },
                      },
                  },
                  { $project: { participants: 0 } },
              ];

        const pipeline = [
            { $match: preConditions },
            {
                $lookup: {
                    from: 'streams',
                    localField: '_id',
                    foreignField: 'webinarId',
                    as: 'streams',
                    pipeline: [
                        {
                            $addFields: {
                                streamId: { $toString: '$_id' },
                                webinarId: { $toString: '$webinarId' },
                                date: { $toLong: '$date' },
                            },
                        },
                        {
                            $project: {
                                _id: 0,
                                streamType: 1,
                                streamId: 1,
                                webinarId: 1,
                                title: 1,
                                date: 1,
                                duration: 1,
                                recordUrl: 1,
                                room: 1,
                                savedRecordUrl: 1,
                            },
                        },
                    ],
                },
            },
            {
                $addFields: {
                    activeStreams: {
                        $slice: [
                            {
                                $sortArray: {
                                    input: {
                                        $filter: {
                                            input: '$streams',
                                            as: 'stream',
                                            // Стрим активен, если его окончание в будущем
                                            cond: {
                                                $gt: [
                                                    {
                                                        $add: ['$$stream.date', '$$stream.duration'],
                                                    },
                                                    query.currentDate.getTime(),
                                                ],
                                            },
                                        },
                                    },
                                    sortBy: { date: 1 }, // новые идут первыми
                                },
                            },
                            1, // берем самый первый
                        ],
                    },
                    pastStreams: {
                        $slice: [
                            {
                                $sortArray: {
                                    input: {
                                        $filter: {
                                            input: '$streams',
                                            as: 'stream',
                                            // Стрим завершен, если его окончание <= текущей даты
                                            cond: {
                                                $lte: [
                                                    {
                                                        $add: ['$$stream.date', '$$stream.duration'],
                                                    },
                                                    query.currentDate.getTime(),
                                                ],
                                            },
                                        },
                                    },
                                    sortBy: { date: -1 }, // старые идут первыми
                                },
                            },
                            1, // берем самый первый
                        ],
                    },
                },
            },
            ...mapToCard,
            ...this.filterActiveStreamsPipeline(query),
            ...getUserStatus,
            ...postFilter,
            // оставляем только те, на которых пользователь участвует как спикер
            ...(query?.filter?.onlyAssigned
                ? [
                      {
                          $match: {
                              participant: { $ne: null },
                              'participant.role': ParticipantRole.SPEAKER,
                          },
                      },
                  ]
                : []),
            ...this.sortCardsPipeline(query),
            ...this.getPaginatedResultPipeline(query),
        ];

        const result = await this.prisma.webinar.aggregateRaw({ pipeline });

        if (!result || result.length === 0) {
            return {
                data: [],
                total: 0,
            };
        }

        const typedRes = result[0] as unknown as {
            data: (IWebinarCardQueryResultParams & {
                participant: {
                    userUuid: string;
                    email?: string;
                    role: ParticipantRole;
                    isViewed?: boolean;
                };
                date: number;
            })[];
            total: number;
        };
        const mappedData = typedRes.data.map((d) => {
            const participant = d.participant;
            const isSpeaker = participant?.role === ParticipantRole.SPEAKER;
            const isRegistered = participant?.role === ParticipantRole.STUDENT;
            const isViewed = participant?.isViewed;
            return {
                ...d,
                isSpeaker,
                isRegistered,
                isViewed,
                participants: isSpeaker ? [] : [participant],
            };
        });

        if (mappedData.length === 0) {
            return {
                data: [],
                total: 0,
            };
        }

        const res = mappedData.map((webinar) => new WebinarCardQueryResult(anUser, webinar));
        if (typedRes.total === 0) {
            this.logger.warn({ user: anUser.toLog() }, 'Пользователь получил пустой каталог вебинаров');
        }

        return {
            data: res,
            total: typedRes.total,
        };
    }

    private filterActiveStreamsPipeline(query: GetWebinarCardsQuery): InputJsonValue[] {
        /**
         * необходимые свойства: date, duration
         */
        const isActive = query.filter.isActive;
        const currentDate = query.currentDate;
        return isActive !== undefined
            ? [
                  {
                      $match: {
                          $expr: isActive
                              ? {
                                    $gt: [{ $add: ['$date', '$duration'] }, currentDate.valueOf()],
                                } // активные вебинары
                              : {
                                    $lte: [{ $add: ['$date', '$duration'] }, currentDate.valueOf()],
                                }, // прошедшие вебинары
                      },
                  },
              ]
            : [];
    }

    private sortCardsPipeline(query: GetWebinarCardsQuery): InputJsonValue[] {
        /**
         * необходимые свойства: date
         */
        const isActive = query.filter.isActive;
        return isActive !== undefined
            ? [
                  {
                      $sort: { date: isActive ? 1 : -1 },
                  },
              ]
            : [
                  {
                      $sort: { date: -1 },
                  },
              ];
    }

    private getIsAssignedFilterPipeline(query: GetWebinarCardsQuery): InputJsonValue[] {
        /**
         * необходимые свойства: role, duration
         */
        return query.filter.onlyAssigned
            ? [
                  {
                      $match: { role: ParticipantRole.SPEAKER },
                  },
              ]
            : [];
    }

    private getParticipantStreamsForLibraryPipeline(query: GetWebinarCardsQuery): InputJsonValue[] {
        const anUser = query.anUser;
        const { schoolUuid } = anUser.params;

        if (anUser.isGuest()) {
            return [];
        }

        const userEmail = anUser.params.email;

        const search = query?.filter?.search;
        const filterBySearchStage = search
            ? [
                  {
                      $match: {
                          title: {
                              $regex: search,
                              $options: 'i',
                          },
                      },
                  },
              ]
            : [];

        return [
            { $match: { email: userEmail } },
            {
                $lookup: {
                    from: 'streams',
                    let: { streamId: { $toString: '$streamId' } },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: [{ $toString: '$_id' }, '$$streamId'],
                                },
                            },
                        },
                        {
                            $project: {
                                _id: 0,
                                streamId: { $toString: '$_id' },
                                webinarId: { $toString: '$webinarId' },
                                date: { $toLong: '$date' },
                                title: 1,
                                duration: 1,
                                streamType: 1,
                                externalStreamUrl: 1,
                                recordUrl: 1,
                                room: 1,
                                savedRecordUrl: 1,
                            },
                        },
                        ...this.filterActiveStreamsPipeline(query), // фильтрация активных потоков
                    ],
                    as: 'stream',
                },
            },
            { $unwind: '$stream' },
            {
                // Получаем информацию о вебинаре для каждого потока
                $lookup: {
                    from: 'webinars',
                    let: { webinarId: '$stream.webinarId' },
                    pipeline: [
                        {
                            $match: {
                                $and: [
                                    {
                                        $expr: {
                                            $eq: [{ $toString: '$_id' }, '$$webinarId'],
                                        },
                                    },
                                    {
                                        $expr: {
                                            $eq: ['$schoolUuid', schoolUuid],
                                        },
                                    }, // фильтрация по schoolUuid
                                ],
                            },
                        },
                        {
                            // Проекция для вебинара
                            $project: {
                                _id: 0,
                                title: 1,
                                description: 1,
                                coverImage: 1,
                                visibility: 1,
                                isPhoneRequiredOnRegistration: 1,
                            },
                        },
                    ],
                    as: 'stream.webinar',
                },
            },
            { $unwind: '$stream.webinar' },
            {
                // Финальная проекция
                $project: {
                    _id: 0,
                    // student
                    userUuid: 1,
                    isViewed: '$isViewed',
                    role: '$role',
                    // stream
                    streamType: '$stream.streamType',
                    streamId: '$stream.streamId',
                    webinarId: '$stream.webinarId',
                    date: '$stream.date',
                    duration: '$stream.duration',
                    recordUrl: '$stream.recordUrl',
                    room: '$stream.room',
                    savedRecordUrl: '$stream.savedRecordUrl',
                    // webinar
                    title: '$stream.webinar.title',
                    description: '$stream.webinar.description',
                    coverImage: '$stream.webinar.coverImage',
                    visibility: '$stream.webinar.visibility',
                    isPhoneRequiredOnRegistration: '$stream.webinar.isPhoneRequiredOnRegistration',
                },
            },
            ...filterBySearchStage, // дополнительные фильтры
            ...this.sortCardsPipeline(query),
        ];
    }

    private getPaginatedResultPipeline(query: GetWebinarCardsQuery): InputJsonValue[] {
        if (!query.pagination) {
            return [
                {
                    $group: {
                        _id: null,
                        total: { $sum: 1 },
                        data: { $push: '$$ROOT' },
                    },
                },
                {
                    $project: {
                        total: 1,
                        data: 1,
                    },
                },
            ];
        }

        return [
            {
                $facet: {
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
                    data: [{ $skip: query.pagination.skip }, { $limit: query.pagination.take }],
                    total: [{ $count: 'count' }],
                },
            },
            {
                $project: {
                    total: { $arrayElemAt: ['$total.count', 0] },
                    data: 1,
                },
            },
        ];
    }
}
