import { Inject, Logger } from '@nestjs/common';
import { <PERSON><PERSON>ueryHandler, QueryHandler } from '@nestjs/cqrs';

import { OTEL_PRISMA_SERVICE } from '../../../core/prisma/otel-prisma.service';
import { PrismaService } from '../../../core/prisma/prisma.service';
import { CoverImage, UploadedImageParams } from '../../domain/objects/cover-image';
import { SchoolUuid } from '../../domain/objects/school-uuid';
import { GetWebinarsResult } from './result/get-webinars.result';

export class GetWebinarsQuery {
    constructor(readonly schoolUuid: SchoolUuid) {}
}

interface PipelineResult {
    data: {
        id: string;
        coverImage: UploadedImageParams | null;
        title: string;
        countRegistered: number;
        streams: {
            id: string;
            date: number;
            duration: number;
            title: string;
            countRegistered: number;
            webinarId: string;
        }[];
    }[];
}

@QueryHandler(GetWebinarsQuery)
export class GetWebinarsHandler implements IQueryHandler<GetWebinarsQuery, GetWebinarsResult> {
    private readonly logger = new Logger(GetWebinarsHandler.name);

    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: PrismaService,
    ) {}

    async execute(query: GetWebinarsQuery): Promise<GetWebinarsResult> {
        const pipeline = [
            {
                $match: {
                    schoolUuid: query.schoolUuid.unwrap(),
                },
            },
            {
                $lookup: {
                    from: 'streams',
                    localField: '_id',
                    foreignField: 'webinarId',
                    as: 'streams',
                },
            },
            // Шаг 3: Подключить участников к потокам
            {
                $unwind: {
                    path: '$streams',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: 'participants',
                    localField: 'streams._id',
                    foreignField: 'streamId',
                    as: 'streams.participants',
                },
            },
            // Шаг 4: Рассчитать количество участников для каждого потока
            {
                $addFields: {
                    'streams.countRegistered': {
                        $size: { $ifNull: ['$streams.participants', []] },
                    },
                },
            },
            // Шаг 5: Сгруппировать данные потоков обратно к вебинарам
            {
                $group: {
                    _id: '$_id',
                    title: { $first: '$title' },
                    coverImage: { $first: '$coverImage' },
                    countRegistered: { $sum: '$streams.countRegistered' }, // Сумма участников по всем потокам
                    streams: {
                        $push: {
                            id: { $toString: '$streams._id' },
                            date: { $toLong: '$streams.date' },
                            duration: '$streams.duration',
                            title: '$streams.title',
                            webinarId: { $toString: '$_id' },
                            countRegistered: '$streams.countRegistered',
                        },
                    },
                },
            },
            // Шаг 6: Проекция итоговых данных
            {
                $project: {
                    _id: 0, // Убираем _id, если он не нужен
                    id: { $toString: '$_id' },
                    coverImage: 1,
                    title: 1,
                    countRegistered: 1,
                    streams: 1,
                },
            },
            {
                $group: {
                    _id: null,
                    data: { $push: '$$ROOT' },
                },
            },
            {
                $project: {
                    _id: 0,
                    data: 1,
                },
            },
        ];

        const result = (await this.prisma.webinar.aggregateRaw({ pipeline }))[0] as unknown as PipelineResult;

        return !result?.data
            ? new GetWebinarsResult([])
            : new GetWebinarsResult(
                  result.data.map((e) => ({
                      ...e,
                      coverUrl: CoverImage.from({ coverImage: e.coverImage }).url,
                  })),
              );
    }
}
