import { EventsHandler, IEvent, IEventHandler } from '@nestjs/cqrs';

import { StreamModel } from '../../../domain/models/stream/stream.model';
import { PlansService } from '../../services/plans.service';

export class StreamUpdatedEvent implements IEvent {
    constructor(
        public readonly params: {
            previous: StreamModel;
            updated: StreamModel;
        },
        public readonly now = new Date(),
    ) {}
}

@EventsHandler(StreamUpdatedEvent)
export class StreamUpdatedEventHandler implements IEventHandler<StreamUpdatedEvent> {
    constructor(private readonly plansService: PlansService) {}
    async handle(event: StreamUpdatedEvent): Promise<void> {
        const { previous, updated } = event.params;

        // переход на встроенные вебинары
        if (!previous.isInternal && updated.isInternal) {
            return this.plansService.decreaseQuota(updated.params.schoolUuid);
        }

        // переход на внешние вебинары
        if (previous.isInternal && !previous.streamingIsOver(event.now) && !updated.isInternal) {
            return this.plansService.increaseQuota(updated.params.schoolUuid, 1);
        }
    }
}
