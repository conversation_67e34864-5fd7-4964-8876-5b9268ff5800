import { Inject } from '@nestjs/common';
import { <PERSON>Handler, IEvent, IEventHandler } from '@nestjs/cqrs';

import { StreamModel } from '../../../domain/models/stream/stream.model';
import { IWebinarRepository } from '../../../domain/repositories/webinar-repository.interface';
import { STATISTICS_SERVICE_ADAPTER, WEBINAR_REPOSITORY } from '../../../injects';
import { IStatisticsServiceAdapter } from '../../adapters/microservices/statistics-service-adapter.interface';
import { PlansService } from '../../services/plans.service';

export class StreamCreatedEvent implements IEvent {
    constructor(public readonly aStream: StreamModel) {}
}

@EventsHandler(StreamCreatedEvent)
export class StreamCreatedEventHandler implements IEventHandler<StreamCreatedEvent> {
    constructor(
        @Inject(STATISTICS_SERVICE_ADAPTER)
        private readonly statistics: IStatisticsServiceAdapter,
        @Inject(WEBINAR_REPOSITORY)
        private readonly webinarRepo: IWebinarRepository,
        private readonly plansService: PlansService,
    ) {}
    async handle(event: StreamCreatedEvent): Promise<void> {
        const { aStream } = event;
        const aWebinarId = aStream.aWebinarId;
        const aWebinar = await this.webinarRepo.getWebinarParams(aWebinarId);

        // Использование встроенных вебинаров
        if (aStream.isInternal) {
            await this.plansService.decreaseQuota(aWebinar.params.schoolUuid);
        }

        // Статистика
        await this.statistics.notifyAboutCreatedStream({
            schoolId: aWebinar.params.schoolUuid,
            webinar: {
                id: aWebinarId.unwrap(),
                name: aWebinar.params.title,
            },
            stream: {
                id: aStream.anId.unwrap(),
                name: aStream.params.title,
            },
        });
    }
}
