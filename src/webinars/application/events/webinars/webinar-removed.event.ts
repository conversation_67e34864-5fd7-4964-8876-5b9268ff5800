import { Inject } from '@nestjs/common';
import { EventsHandler, IEvent, IEventHandler } from '@nestjs/cqrs';

import { WebinarModel } from '../../../domain/models/webinar/webinar.model';
import { FILE_STORAGE_ADAPTER, STATISTICS_SERVICE_ADAPTER, VIDEO_STORAGE_ADAPTER } from '../../../injects';
import { IStatisticsServiceAdapter } from '../../adapters/microservices/statistics-service-adapter.interface';
import { IFileStorageAdapter } from '../../adapters/saas/file-storage-adapter.interface';
import { IVideoStorageAdapter } from '../../adapters/saas/video-storage-adapter.interface';
import { SchoolService } from '../../services/school.service';

export class WebinarRemovedEvent implements IEvent {
    constructor(public readonly aRemovedWebinar: WebinarModel) {}
}

@EventsHandler(WebinarRemovedEvent)
export class WebinarRemovedEventHandler implements IEventHandler<WebinarRemovedEvent> {
    constructor(
        @Inject(FILE_STORAGE_ADAPTER)
        private readonly storageService: IFileStorageAdapter,
        @Inject(VIDEO_STORAGE_ADAPTER)
        private readonly videoStorage: IVideoStorageAdapter,
        @Inject(STATISTICS_SERVICE_ADAPTER)
        private readonly statistics: IStatisticsServiceAdapter,
        private readonly schoolService: SchoolService,
    ) {}
    async handle(event: WebinarRemovedEvent) {
        const { aRemovedWebinar } = event;

        // Статистика
        await this.statistics.notifyAboutRemovedWebinars({
            removedWebinarsIds: [aRemovedWebinar.id],
        });

        await this.statistics.notifyAboutRemovedStreams({
            removedStreamsIds: aRemovedWebinar.params.streams.map((stream) => stream.id),
        });

        // Файлы
        await this.storageService.removeAllWebinarFiles(aRemovedWebinar.id);

        // Хостинг видео
        const school = await this.schoolService.getSchool(aRemovedWebinar.schoolUuid);
        if (school) {
            await this.videoStorage.deleteWebinarRecords(school, aRemovedWebinar.params);
        }
    }
}
