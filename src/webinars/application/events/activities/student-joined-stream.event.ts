import { setTimeout } from 'node:timers/promises';
import { Inject, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, IEvent, IEventHandler } from '@nestjs/cqrs';
import { StatisticsStudentStreamUpdatedContractNamespace } from '@skillspace/amqp-contracts';

import { WAIT_FOR_USER_ID_MS } from '../../../../configs/app.config';
import { ERROR_CODE } from '../../../../shared/webinar-service.error';
import { StudentStreamHistoryRecord } from '../../../domain/models/student/student-webinar-history.aggregate';
import { IParticipantRepository } from '../../../domain/repositories/participant-repository.interface';
import { IStreamRepository } from '../../../domain/repositories/stream-repository.interface';
import { PARTICIPANT_REPOSITORY, STATISTICS_SERVICE_ADAPTER, STREAM_REPOSITORY } from '../../../injects';
import { IStatisticsServiceAdapter } from '../../adapters/microservices/statistics-service-adapter.interface';

export class StudentJoinedStreamEvent implements IEvent {
    constructor(
        public readonly aStreamHistoryRecord: Omit<StudentStreamHistoryRecord, 'changed'>,
        public readonly studentEmail: string,
    ) {}
}

@EventsHandler(StudentJoinedStreamEvent)
export class StudentJoinedStreamEventHandler implements IEventHandler<StudentJoinedStreamEvent> {
    private readonly logger = new Logger(StudentJoinedStreamEventHandler.name);

    constructor(
        @Inject(PARTICIPANT_REPOSITORY)
        private readonly participantRepo: IParticipantRepository,
        @Inject(STATISTICS_SERVICE_ADAPTER)
        private readonly statistics: IStatisticsServiceAdapter,
        @Inject(STREAM_REPOSITORY)
        private readonly streamRepo: IStreamRepository,
    ) {}

    async handle(event: StudentJoinedStreamEvent) {
        const { aStreamHistoryRecord, studentEmail } = event;
        const attendancePoints = aStreamHistoryRecord.attendancePoints;
        const streamId = aStreamHistoryRecord.streamId.unwrap();

        let studentId = aStreamHistoryRecord.userUuid.unwrap();

        if (!studentId) {
            await setTimeout(WAIT_FOR_USER_ID_MS);
            const student = await this.participantRepo.getStudentInfoOrNull(streamId, studentEmail);

            if (!student) {
                this.logger.warn(
                    {
                        code: ERROR_CODE.INTEGRATION_ERROR,
                        details: { studentEmail, streamId: aStreamHistoryRecord.streamId.unwrap() },
                    },
                    `Не зафиксировано посещение потока студеном ${studentEmail} в статистике из-за отсутствия идентификатора`,
                );
                return;
            }

            const aStream = await this.streamRepo.getStream(aStreamHistoryRecord.streamId);
            studentId = student.userUuid;

            await this.statistics.notifyAboutRegisteredStudents({
                webinarId: aStream.params.webinarId,
                schoolId: aStream.params.schoolUuid,
                streamId,
                studentsIds: [studentId],
                students: [
                    {
                        id: studentId,
                        phoneNumber: student.phoneNumber,
                    },
                ],
            });
        }

        await this.statistics.notifyAboutStudentActivity({
            streamId: aStreamHistoryRecord.streamId.unwrap(),
            studentId,
            score: attendancePoints,
            visitingStatus: StatisticsStudentStreamUpdatedContractNamespace.VisitingStatusEnum.VISIT,
            completed: true,
        });
    }
}
