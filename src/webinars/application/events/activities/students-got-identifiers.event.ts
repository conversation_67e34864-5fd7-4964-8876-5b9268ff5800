import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, IEvent, IEventHandler } from '@nestjs/cqrs';

import { StreamId } from '../../../domain/objects/stream-id';
import { IStreamRepository } from '../../../domain/repositories/stream-repository.interface';
import { IWebinarRepository } from '../../../domain/repositories/webinar-repository.interface';
import {
    NOTIFICATION_SERVICE_ADAPTER,
    STATISTICS_SERVICE_ADAPTER,
    STREAM_REPOSITORY,
    WEBINAR_REPOSITORY,
} from '../../../injects';
import { INotificationServiceAdapter } from '../../adapters/microservices/notification-service-adapter.interface';
import { IStatisticsServiceAdapter } from '../../adapters/microservices/statistics-service-adapter.interface';
import { NewStudent } from '../../commands/sync/synchronize-students-identity.command';

export class StudentsGotIdentifiersEvent implements IEvent {
    constructor(
        public readonly streamId: string,
        public readonly students: NewStudent[],
    ) {}
}

interface NotifyMeta {
    schoolId: string;
    webinarId: string;
    webinarTitle: string;
    streamId: string;
    date: string;
}

@EventsHandler(StudentsGotIdentifiersEvent)
export class StudentsGotIdentifiersEventHandler implements IEventHandler<StudentsGotIdentifiersEvent> {
    constructor(
        @Inject(STATISTICS_SERVICE_ADAPTER)
        private readonly statistics: IStatisticsServiceAdapter,
        @Inject(NOTIFICATION_SERVICE_ADAPTER)
        private readonly notification: INotificationServiceAdapter,
        @Inject(WEBINAR_REPOSITORY)
        private readonly webinarRepository: IWebinarRepository,
        @Inject(STREAM_REPOSITORY)
        private readonly streamRepository: IStreamRepository,
    ) {}

    async handle(event: StudentsGotIdentifiersEvent) {
        const { students, streamId } = event;

        const aStream = await this.streamRepository.getStream(StreamId.wrap(streamId));
        const aWebinarParams = await this.webinarRepository.getWebinarParams(aStream.aWebinarId);

        const notifyMeta: NotifyMeta = {
            schoolId: aWebinarParams.params.schoolUuid,
            webinarTitle: aWebinarParams.params.title,
            webinarId: aWebinarParams.id,
            streamId,
            date: aStream.params.date.toLocaleDateString('ru-RU', {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
            }),
        };

        await this.notifyStatistic(students, notifyMeta);
        await this.notifyStudentsByEmail(students, notifyMeta);
    }

    private async notifyStatistic(students: NewStudent[], meta: NotifyMeta): Promise<void> {
        await this.statistics.notifyAboutRegisteredStudents({
            webinarId: meta.webinarId,
            schoolId: meta.schoolId,
            streamId: meta.streamId,
            studentsIds: students.map((student) => student.userUuid),
            students: students.map((student) => ({
                id: student.userUuid,
                phoneNumber: student.studentPhoneNumber,
            })),
        });
    }

    private async notifyStudentsByEmail(students: NewStudent[], meta: NotifyMeta): Promise<void> {
        for (const student of students) {
            const { streamId, studentEmail, school } = student;
            const registerToken = student.registerToken;

            const linkToFollow = registerToken
                ? `https://${school.domain}/stream/${streamId}?registerToken=${registerToken}`
                : `https://${school.domain}/stream/${streamId}`;

            const title = meta.webinarTitle;
            const date = meta.date;

            // Локализация для HTML-сообщения
            const htmlTranslations = {
                ru: `Вы записаны на вебинар "${title}" в школе "${school.name}", который состоится ${date}`,
                en: `You are registered for the webinar "${title}" at the school "${school.name}", which will take place on ${date}`,
                es: `Estás inscrito al webinar "${title}" en la escuela "${school.name}", que tendrá lugar el ${date}`,
                kk: `Сіз "${title}" вебинарына, "${school.name}" мектебінде, ${date} күні өтетін вебинарға жазылдыңыз`,
                uz: `"${school.name}" maktabida bo'lib o'tadigan "${title}" vebinariga yozildingiz. Vebinar ${date} da o'tkaziladi.`,
                de: `Sie haben sich für das Webinar "${title}" an der Schule "${school.name}" angemeldet, das am ${date} stattfindet`,
                fr: `Vous êtes inscrit au webinaire "${title}" à l'école "${school.name}", qui aura lieu le ${date}`,
                it: `Sei iscritto al webinar "${title}" presso la scuola "${school.name}", che si terrà il ${date}`,
            };

            // Локализация для текста кнопки
            const buttonTextTranslations = {
                ru: 'Перейти к вебинару',
                en: 'Go to the webinar',
                es: 'Ir al webinar',
                kk: 'Вебинарға өту',
                uz: 'Vebinarga o‘tish',
                de: 'Zum Webinar gehen',
                fr: 'Aller au webinaire',
                it: 'Vai al webinar',
            };

            const subjectTranslations = {
                ru: `Вебинар: ${title}`,
                en: `Webinar: ${title}`,
                es: `Webinar: ${title}`,
                kk: `Вебинар: ${title}`,
                uz: `Vebinar: ${title}`,
                de: `Webinar: ${title}`,
                fr: `Webinar: ${title}`,
                it: `Webinar: ${title}`,
            };

            const lang = school.language || 'ru';

            // Вызов функции уведомления
            await this.notification.notifyStudentByEmail({
                schoolUuid: school.uuid,
                recipients: [studentEmail],
                subject: subjectTranslations[lang],
                body: {
                    html: htmlTranslations[lang],
                    redirectUrl: linkToFollow,
                    buttonText: buttonTextTranslations[lang],
                    schoolSlug: null,
                    hideMobileAppLinks: school?.whiteLabel?.hideMobileAppLinks,
                    isWhiteLabel: school?.whiteLabel?.isWhiteLabel,
                    schoolLogoUrl: school?.whiteLabel?.schoolLogoUrl,
                    primaryColor: school?.whiteLabel?.primaryColor,
                },
                template: 'notification',
                fromEmail: school.email,
                fromName: school.name,
            });
        }
    }
}
