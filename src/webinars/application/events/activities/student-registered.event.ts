import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ler, IEvent, IEventHandler } from '@nestjs/cqrs';
import { MonolithWebinarRegistrationNotifyContractNamespace } from '@skillspace/amqp-contracts';

import { StreamModel } from '../../../domain/models/stream/stream.model';
import { USER_REGISTRY_ADAPTER } from '../../../injects';
import { IUserRegistryAdapter } from '../../adapters/microservices/user-registry-adapter.interface';

export class StudentRegisteredEvent implements IEvent {
    public readonly aStream: StreamModel;
    public readonly studentEmail: string;
    public readonly studentName: string;
    public readonly studentPhoneNumber?: string;
    public readonly studentTimeZone: string;

    constructor(
        public readonly params: {
            aStream: StreamModel;
            studentEmail: string;
            studentName: string;
            studentPhoneNumber?: string;
            studentTimeZone: string;
        },
    ) {
        this.aStream = params.aStream;
        this.studentEmail = params.studentEmail;
        this.studentName = params.studentName;
        this.studentPhoneNumber = params?.studentPhoneNumber;
        this.studentTimeZone = params.studentTimeZone;
    }
}

@EventsHandler(StudentRegisteredEvent)
export class StudentRegisteredEventHandler implements IEventHandler<StudentRegisteredEvent> {
    constructor(
        @Inject(USER_REGISTRY_ADAPTER)
        private readonly userService: IUserRegistryAdapter,
    ) {}

    async handle(event: StudentRegisteredEvent): Promise<void> {
        const { studentEmail, studentName, studentTimeZone, aStream } = event;

        await this.userService.notifyMonolithAboutNewStudents({
            type: MonolithWebinarRegistrationNotifyContractNamespace.EVENT_TYPE_ENUM.STUDENTS_REGISTERED,
            streamId: aStream.id,
            registeredStudents: [
                {
                    schoolUuid: aStream.params.schoolUuid,
                    email: studentEmail,
                    name: studentName,
                    phoneNumber: event.studentPhoneNumber,
                    timezone: studentTimeZone,
                },
            ],
        });
    }
}
