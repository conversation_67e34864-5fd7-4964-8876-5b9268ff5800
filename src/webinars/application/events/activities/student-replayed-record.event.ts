import { setTimeout } from 'node:timers/promises';
import { Inject, Logger } from '@nestjs/common';
import { EventsHandler, IEvent, IEventHandler } from '@nestjs/cqrs';
import { StatisticsStudentStreamUpdatedContractNamespace } from '@skillspace/amqp-contracts';
import { ERROR_CODE } from '@skillspace/utils';

import { WAIT_FOR_USER_ID_MS } from '../../../../configs/app.config';
import { StreamId } from '../../../domain/objects/stream-id';
import { IParticipantRepository } from '../../../domain/repositories/participant-repository.interface';
import { IStreamRepository } from '../../../domain/repositories/stream-repository.interface';
import { PARTICIPANT_REPOSITORY, STATISTICS_SERVICE_ADAPTER, STREAM_REPOSITORY } from '../../../injects';
import { IStatisticsServiceAdapter } from '../../adapters/microservices/statistics-service-adapter.interface';

export class StudentReplayedRecordEvent implements IEvent {
    constructor(
        public readonly params: {
            studentId: string;
            studentEmail: string;
            streamId: string;
        },
    ) {}
}

@EventsHandler(StudentReplayedRecordEvent)
export class StudentReplayedRecordEventHandler implements IEventHandler<StudentReplayedRecordEvent> {
    private readonly logger = new Logger(StudentReplayedRecordEventHandler.name);

    constructor(
        @Inject(PARTICIPANT_REPOSITORY)
        private readonly participantRepo: IParticipantRepository,
        @Inject(STATISTICS_SERVICE_ADAPTER)
        private readonly statistics: IStatisticsServiceAdapter,
        @Inject(STREAM_REPOSITORY)
        private readonly streamRepo: IStreamRepository,
    ) {}

    async handle(event: StudentReplayedRecordEvent) {
        const {
            params: { streamId, studentEmail },
        } = event;

        let studentId = event.params.studentId;

        if (!studentId) {
            await setTimeout(WAIT_FOR_USER_ID_MS);
            const student = await this.participantRepo.getStudentInfoOrNull(streamId, studentEmail);

            if (!student) {
                this.logger.warn(
                    {
                        code: ERROR_CODE.INTEGRATION_ERROR,
                        details: { email: studentEmail, streamId },
                    },
                    `Не зафиксирован просмотр записи студеном ${studentEmail} в статистике из-за отсутствия идентификатора`,
                );
                return;
            }

            const aStream = await this.streamRepo.getStream(StreamId.wrap(streamId));
            studentId = student.userUuid;

            await this.statistics.notifyAboutRegisteredStudents({
                webinarId: aStream.params.webinarId,
                schoolId: aStream.params.schoolUuid,
                streamId,
                studentsIds: [studentId],
                students: [
                    {
                        id: studentId,
                        phoneNumber: student.phoneNumber,
                    },
                ],
            });
        }

        await this.statistics.notifyAboutStudentActivity({
            streamId,
            studentId,
            score: 0,
            visitingStatus: StatisticsStudentStreamUpdatedContractNamespace.VisitingStatusEnum.RECORD,
            completed: true,
        });
    }
}
