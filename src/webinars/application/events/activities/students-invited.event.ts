import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, IEvent, IEventHandler } from '@nestjs/cqrs';
import { MonolithWebinarRegistrationNotifyContractNamespace } from '@skillspace/amqp-contracts';

import { SchoolUuid } from '../../../domain/objects/school-uuid';
import { StreamId } from '../../../domain/objects/stream-id';
import { USER_REGISTRY_ADAPTER } from '../../../injects';
import { IUserRegistryAdapter } from '../../adapters/microservices/user-registry-adapter.interface';

export class StudentsInvitedEvent implements IEvent {
    constructor(
        public readonly aSchoolUuid: SchoolUuid,
        public readonly aStreamId: StreamId,
        public readonly studentEmails: string[],
    ) {}
}

@EventsHandler(StudentsInvitedEvent)
export class StudentsInvitedEventHandler implements IEventHandler<StudentsInvitedEvent> {
    constructor(
        @Inject(USER_REGISTRY_ADAPTER)
        private readonly userService: IUserRegistryAdapter,
    ) {}

    async handle(event: StudentsInvitedEvent) {
        const { aSchoolUuid, studentEmails, aStreamId } = event;
        const invitedStudents = studentEmails.map((email) => ({
            email,
            schoolUuid: aSchoolUuid.unwrap(),
        }));
        await this.userService.notifyMonolithAboutNewStudents({
            type: MonolithWebinarRegistrationNotifyContractNamespace.EVENT_TYPE_ENUM.STUDENTS_REGISTERED,
            streamId: aStreamId.unwrap(),
            registeredStudents: invitedStudents,
        });
    }
}
