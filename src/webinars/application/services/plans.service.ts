import { Inject, Injectable, Logger } from '@nestjs/common';
import { CustomError, ERROR_CODE } from '@skillspace/utils';

import { PLANS_SERVICE_ADAPTER } from '../../injects';
import { IPlansServiceAdapter } from '../adapters/microservices/plans-service-adapter.interface';

@Injectable()
export class PlansService {
    private readonly logger = new Logger(PlansService.name);

    constructor(
        @Inject(PLANS_SERVICE_ADAPTER)
        private readonly plansAdapter: IPlansServiceAdapter,
    ) {}

    public async checkInternalWebinarFeatureAccess(schoolUuid: string): Promise<void> {
        await Promise.all([this.checkWebinarFeatureAccess(schoolUuid), this.checkMonthlyLimit(schoolUuid)]);
    }

    public async decreaseQuota(schoolUuid: string, delta = 1): Promise<void> {
        await this.plansAdapter.changeInternalWebinarUsage({ schoolUuid, delta: -delta });
        this.logger.log({ schoolUuid, delta }, `Квота использования уменьшена`);
        return;
    }

    public async increaseQuota(schoolUuid: string, delta = 1): Promise<void> {
        await this.plansAdapter.changeInternalWebinarUsage({ schoolUuid, delta });
        this.logger.log({ schoolUuid, delta }, `Квота использования увеличена`);
        return;
    }

    public async getParticipantsLimit(schoolUuid: string): Promise<number> {
        const featureAccess = await this.plansAdapter.checkWebinarFeatureAccess(schoolUuid);
        this.logger.log({ schoolUuid, limit: featureAccess.limit }, `Получен лимит участников: ${featureAccess.limit}`);
        return featureAccess.limit;
    }

    private async checkWebinarFeatureAccess(schoolUuid: string): Promise<void> {
        const featureAccess = await this.plansAdapter.checkWebinarFeatureAccess(schoolUuid);
        const { limit, enabled } = featureAccess;
        this.logger.log(
            { schoolUuid, activeUsersLimit: limit, enabled },
            `Проверка доступа к встроенным вебам: ${featureAccess.enabled}`,
        );

        if (!featureAccess.enabled) {
            throw new CustomError('Возможность использования встроенных вебинаров не предусмотрена планом подписки', {
                code: ERROR_CODE.FORBIDDEN_ERROR,
                details: { schoolUuid, limit: featureAccess.limit, enabled: featureAccess.enabled },
            });
        }
    }

    private async checkMonthlyLimit(schoolUuid: string): Promise<void> {
        const featureLimit = await this.plansAdapter.checkInternalWebinarUsageLimit(schoolUuid);
        const { limit, enabled } = featureLimit;

        this.logger.log({ schoolUuid, limit, enabled }, `Проверка лимита вебинаров: ${limit}`);

        if (featureLimit.limit === 0 || !featureLimit.enabled) {
            throw new CustomError('Превышен лимит за последний месяц', {
                code: ERROR_CODE.FORBIDDEN_ERROR,
                details: {
                    schoolUuid,
                    available: featureLimit.limit,
                },
            });
        }
    }
}
