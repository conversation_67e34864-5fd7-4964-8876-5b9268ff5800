import { Inject, Logger } from '@nestjs/common';
import { SaasProviderEnum, StorageStateEnum } from '@prisma/client';

import { SchoolModel } from '../../domain/models/school.model';
import { StreamModel } from '../../domain/models/stream/stream.model';
import { RoomParams, RoomStateEnum } from '../../domain/models/stream/stream-room';
import { UserBot } from '../../domain/models/user/user-bot';
import { WebinarModel } from '../../domain/models/webinar/webinar.model';
import { StreamId } from '../../domain/objects/stream-id';
import { IStreamRepository } from '../../domain/repositories/stream-repository.interface';
import { IWebinarRepository } from '../../domain/repositories/webinar-repository.interface';
import { STREAM_REPOSITORY, VIDEO_STORAGE_ADAPTER, WEBINAR_PROVIDER_ADAPTER, WEBINAR_REPOSITORY } from '../../injects';
import { IVideoStorageAdapter, VideoStorageFolderIds } from '../adapters/saas/video-storage-adapter.interface';
import {
    IWebinarProviderAdapter,
    RoomActiveCall,
    RoomRecordUrl,
} from '../adapters/saas/webinar-provider-adapter.interface';
import { SchoolService } from './school.service';

interface StreamRecordParams {
    spaceId: string;
    roomId: string;
    recordId: string;
    provider: SaasProviderEnum;
}

export class TransferService {
    constructor(
        @Inject(WEBINAR_REPOSITORY)
        private readonly webinarRepo: IWebinarRepository,
        @Inject(STREAM_REPOSITORY)
        private readonly streamRepo: IStreamRepository,
        @Inject(WEBINAR_PROVIDER_ADAPTER)
        private readonly webinarProvider: IWebinarProviderAdapter,
        @Inject(VIDEO_STORAGE_ADAPTER)
        private readonly videoStorage: IVideoStorageAdapter,
        private readonly schoolService: SchoolService,
    ) {}

    private readonly logger = new Logger(TransferService.name);

    public async transferRoomRecordByWebhook(params: StreamRecordParams): Promise<void> {
        const { roomId, recordId } = params;

        const aStream = await this.streamRepo.findStreamByRoomId(roomId, params);

        const recordsToSync = await this.getRoomRecordsToSync(aStream.room);
        if (!recordsToSync.length) return;

        const record = recordsToSync.find((rec) => (rec.id = recordId));
        if (!record) return;

        return this.uploadManyRoomRecords(aStream, [record]);
    }

    public async syncRecordsAndCloseRoom(aStream: StreamModel) {
        if (!aStream.slotIsOver()) return;

        let roomCall: RoomActiveCall | null = null;
        try {
            roomCall = await this.webinarProvider.getRoomActiveCall(aStream.room);
        } catch {
            // если звонок не найден, то он уже закончился
            roomCall = null;
        }
        // Если в комнате идет трансляция откладываем удаление
        if (roomCall?.active) return;

        // Прежде чем закрыть, запускаем загрузку записей на хостинг
        const recordsToSync = await this.getRoomRecordsToSync(aStream.room);
        await this.uploadManyRoomRecords(aStream, recordsToSync); // set storageState: uploading

        // Получаем обновленный поток с загруженными записями
        const anUpdatedStream = await this.streamRepo.getStream(aStream.anId);
        const { room, id: streamId } = anUpdatedStream;

        // Закрываем комнату
        await Promise.all([
            this.webinarProvider.deleteRoom(room),
            this.streamRepo.update(anUpdatedStream.update({ room: { ...room, state: RoomStateEnum.closed } })),
        ]);
        const { state, roomId, uploadedRecordIds } = room;
        this.logger.log({ streamId, state, roomId, uploadedRecordIds }, `Закрыта комната вебинара ${streamId}`);
    }

    public async removeDraftVideosByCron(aStream: StreamModel): Promise<void> {
        if (
            !(
                aStream.room.state === RoomStateEnum.closed &&
                aStream.params.storageState === StorageStateEnum.processing
            )
        ) {
            this.logger.warn(
                { state: aStream.room.state, storage: aStream.params.storageState },
                'Ожидается, что комната закрыта, а видео в процессе обработки',
            );
            return;
        }

        const { id: streamId, room, kinescopeFolderId } = aStream;

        // Если видео все еще в процессе объединения, оставляем до следующего обхода планировщика
        const inProgress = await this.hasUnprocessedVideos({ id: streamId, room, kinescopeFolderId });
        if (inProgress) return;

        // Удаляем записи из которых монтировалось финальное видео
        const allRecords = await this.videoStorage.getRecords(aStream);
        const draftRecords = allRecords.filter((record) => record.embedLink !== aStream.embedRecordUrl);

        if (draftRecords.length) {
            for (const record of draftRecords) {
                try {
                    await this.videoStorage.deleteRecord(record.id);
                } catch (e) {
                    if (e instanceof Error) {
                        this.logger.error(
                            { error: e.stack, allRecords, draftRecords },
                            `Ошибка удаления черновой записи ${record.id}: ${e}`,
                        );
                    }
                }
            }
            this.logger.log({ streamId, kinescopeFolderId }, `Удалены черновые записи для потока ${streamId}`);
        }

        // Отмечаем что процесс обработки видео на хостинге завершен
        const anUpdatedStream = aStream.update({ storageState: StorageStateEnum.finished });
        await this.streamRepo.update(anUpdatedStream);
    }

    private async uploadManyRoomRecords(aStream: StreamModel, records: RoomRecordUrl[]): Promise<void> {
        if (!records.length) return;

        const aWebinar = await this.webinarRepo.getWebinarParams(aStream.aWebinarId);
        const aSchool = await this.getSchoolModel(aWebinar.schoolUuid);

        // Создаем структуру папок
        const hostingFolders = await this.videoStorage.createFolderStructure({
            school: aSchool,
            webinar: aWebinar,
            stream: aStream,
        });
        const { anUpdatedStream } = await this.updateKinescopeIds(hostingFolders, { aWebinar, aStream });

        // Синхронизация записей комнаты с хостингом
        this.logger.log({ roomId: aStream.room.roomId, count: records.length }, 'Загрузка записей на хостинг...');
        const uploadedIds = [...anUpdatedStream.room.uploadedRecordIds];
        for (const record of records) {
            const recordInfo = {
                streamId: anUpdatedStream.id,
                folderId: anUpdatedStream.kinescopeFolderId,
                recordId: record.id,
            };
            try {
                await this.videoStorage.uploadVideoByURL(anUpdatedStream, record.url);
                uploadedIds.push(record.id);
                this.logger.log(recordInfo, `Запись успешно загружена на видео хостинг`);
            } catch (error) {
                if (error instanceof Error) {
                    this.logger.error(
                        { ...recordInfo, error: error.stack },
                        `Ошибка загрузки записи на видео хостинг: ${error.message}`,
                    );
                }
            }
        }

        // Обновляем список загруженных записей
        const aStreamToUpdate = anUpdatedStream.update({
            room: { ...anUpdatedStream.room, uploadedRecordIds: uploadedIds },
            storageState: StorageStateEnum.uploading,
        });
        await this.streamRepo.update(aStreamToUpdate);
    }

    public async updateStreamWithRecordLink(aStream: StreamModel) {
        if (
            !(aStream.room.state === RoomStateEnum.closed && aStream.params.storageState === StorageStateEnum.uploading)
        ) {
            this.logger.warn(
                { state: aStream.room.state, storage: aStream.params.storageState },
                'Ожидается, что комната закрыта, а записи в процессе загрузки',
            );
            return;
        }

        // Проверяем процесс окончания загрузки
        const { id: streamId, room, kinescopeFolderId } = aStream;
        const isInProgress = await this.hasUnprocessedVideos({ id: streamId, room, kinescopeFolderId });
        if (isInProgress) return;

        // Начинаем процесс обработки записей в одно видео и уже получаем ссылку
        const embedRecordUrl = await this.videoStorage.generateEmbedVideoLink(aStream);
        const anUpdatedStream = aStream.update({
            savedRecordUrl: embedRecordUrl,
            storageState: StorageStateEnum.processing,
        });
        await this.streamRepo.update(anUpdatedStream);

        this.logger.log({ roomId: room.roomId, streamId: aStream.id }, `Сохранен URL записи для потока ${aStream.id}`);
    }

    public async closeRoomsWithFinishedSession(currentDate: Date = new Date()): Promise<void> {
        const streamIdsToCloseRoom: string[] = await this.streamRepo.findStreamIdsToCloseRoom(currentDate);
        for (const streamId of streamIdsToCloseRoom) {
            const aStream = await this.streamRepo.getStream(StreamId.wrap(streamId));
            if (!aStream.slotIsOver()) {
                this.logger.warn(
                    {
                        endDate: aStream.period.end.toISOString(),
                        currentDate: currentDate.toISOString(),
                        streamId: aStream.id,
                    },
                    'Ошибка запроса планировщика',
                );
                continue;
            }
            await this.syncRecordsAndCloseRoom(aStream);
        }
    }

    /** Склеиваем видео для вебов, для которых загрузились записи, обновляем url */
    public async updateSavedRecordsUrl(): Promise<void> {
        const streamIdsToUpdateSavedRecordUrl = await this.streamRepo.findStreamIdsToUpdateRecordUrl();
        for (const streamId of streamIdsToUpdateSavedRecordUrl) {
            const aStream = await this.streamRepo.getStream(StreamId.wrap(streamId));
            await this.updateStreamWithRecordLink(aStream);
        }
    }

    /** Удаляем записи в  комнатах, в которых закончена обработка финального видео */
    public async removeDraftVideoRecordsFromKinescope(): Promise<void> {
        const streamIdsToRemoveDraftRecords = await this.streamRepo.findStreamIdsToRemoveDraftVideos();
        for (const streamId of streamIdsToRemoveDraftRecords) {
            const aStream = await this.streamRepo.getStream(StreamId.wrap(streamId));
            await this.removeDraftVideosByCron(aStream);
        }
    }

    private async getRoomRecordsToSync(room: RoomParams): Promise<RoomRecordUrl[]> {
        const uploadedRecordIds = room.uploadedRecordIds;
        const roomRecords = await this.webinarProvider.getRoomRecords(room);
        return roomRecords.filter((record) => !uploadedRecordIds.includes(record.id));
    }

    private async hasUnprocessedVideos(stream: {
        id: string;
        room: RoomParams;
        kinescopeFolderId: string;
    }): Promise<boolean> {
        const room = stream.room;
        const videoRecords = await this.videoStorage.getRecords({
            room,
            id: stream.id,
            kinescopeFolderId: stream.kinescopeFolderId,
        });

        return videoRecords.filter((record) => record.progress > 0 || record.duration === 0).length !== 0;
    }

    private async getSchoolModel(schoolUuid: string): Promise<SchoolModel> {
        const aSchool = await this.schoolService.getSchool(schoolUuid);
        if (aSchool) {
            return aSchool;
        }
        await this.schoolService.createSchool(schoolUuid);
        return this.schoolService.getSchool(schoolUuid);
    }

    private async updateKinescopeIds(
        folders: VideoStorageFolderIds,
        models: { aWebinar: WebinarModel; aStream: StreamModel },
    ): Promise<{ anUpdatedStream: StreamModel }> {
        const { webinarFolderId, streamFolderId } = folders;
        const { aWebinar, aStream } = models;

        if (!aWebinar.kinescopeFolderId) {
            const aBot = UserBot.create();
            const anUpdatedWebinar = aWebinar.update(aBot, { kinescopeFolderId: webinarFolderId });
            await this.webinarRepo.update(anUpdatedWebinar);
        }

        if (!aStream.kinescopeFolderId) {
            const anUpdatedStream = aStream.update({ kinescopeFolderId: streamFolderId });
            await this.streamRepo.update(anUpdatedStream);
            return { anUpdatedStream };
        }

        return { anUpdatedStream: aStream };
    }
}
