import { Inject, Injectable, Logger } from '@nestjs/common';

import { EDITOR_SERVICE_ADAPTER } from '../../injects';
import { IEditorServiceAdapter, Page } from '../adapters/microservices/editor-service-adapter.interface';

@Injectable()
export class ContentService {
    private readonly logger = new Logger(ContentService.name);

    constructor(
        @Inject(EDITOR_SERVICE_ADAPTER)
        private readonly contentService: IEditorServiceAdapter,
    ) {}

    public async addPage(schoolId: string, streamId: string): Promise<Page> {
        return this.contentService.createPage({ schoolId, streamId });
    }

    public async removePage(pageId: string): Promise<void> {
        await this.contentService.removePage(pageId);
    }
}
