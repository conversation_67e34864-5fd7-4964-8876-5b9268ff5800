import { Inject, Injectable } from '@nestjs/common';

import { SchoolModel } from '../../domain/models/school.model';
import { ISchoolRepository } from '../../domain/repositories/school-repository.interface';
import { SCHOOL_REGISTRY_ADAPTER, SCHOOL_REPOSITORY, VIDEO_STORAGE_ADAPTER } from '../../injects';
import { ISchoolRegistryAdapter } from '../adapters/microservices/school-registry-adapter.interface';
import { IVideoStorageAdapter } from '../adapters/saas/video-storage-adapter.interface';

@Injectable()
export class SchoolService {
    constructor(
        @Inject(SCHOOL_REPOSITORY)
        private readonly schoolRepo: ISchoolRepository,
        @Inject(VIDEO_STORAGE_ADAPTER)
        private readonly videoStorage: IVideoStorageAdapter,
        @Inject(SCHOOL_REGISTRY_ADAPTER)
        private schoolRegistry: ISchoolRegistryAdapter,
    ) {}

    public async getSchool(schoolUuid: string): Promise<SchoolModel> {
        return this.schoolRepo.findOne(schoolUuid);
    }

    public async createSchool(schoolUuid: string): Promise<void> {
        const projectId = await this.schoolRegistry.getKinescopeBySchoolId(schoolUuid);
        const subFolderId = await this.videoStorage.createProjectSubFolder(projectId, 'Вебинары');

        const aNewSchool = SchoolModel.create({
            uuid: schoolUuid,
            kinescope: { projectId, folderId: subFolderId },
        });
        await this.schoolRepo.create(aNewSchool);
    }
}
