import {
    RequestStatisticsStreamCreatedContract,
    RequestStatisticsStreamsRemovedContract,
    RequestStatisticsStudentsRegisteredContract,
    RequestStatisticsStudentStreamUpdatedContract,
    RequestStatisticsWebinarsRemovedContract,
} from '@skillspace/amqp-contracts';

export interface IStatisticsServiceAdapter {
    // Statistics Webinars
    notifyAboutCreatedStream(payload: RequestStatisticsStreamCreatedContract['payload']): Promise<void>;
    notifyAboutRemovedStreams(payload: RequestStatisticsStreamsRemovedContract['payload']): Promise<void>;
    notifyAboutRemovedWebinars(payload: RequestStatisticsWebinarsRemovedContract['payload']): Promise<void>;
    // Statistics Students
    notifyAboutRegisteredStudents(payload: RequestStatisticsStudentsRegisteredContract['payload']): Promise<void>;
    notifyAboutStudentActivity(payload: RequestStatisticsStudentStreamUpdatedContract['payload']): Promise<void>;
}
