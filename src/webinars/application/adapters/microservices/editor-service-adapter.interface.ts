export interface Page {
    schoolId: string;
    streamId: string;
    pageId: string;
}

export interface PageOwner {
    schoolId: string;
    streamId: string;
}

export interface RemovePageResult {
    success: boolean;
}

export interface RemoveManyPagesResult extends RemovePageResult {
    removedPageIds: string[];
    errors: string | string[];
}

export interface IEditorServiceAdapter {
    createPage(owner: PageOwner): Promise<Page>;
    removePage(pageId: string): Promise<RemovePageResult>;
    createManyPages(owners: PageOwner[]): Promise<Page[]>;
    removeManyPages(pageIds: string[]): Promise<RemoveManyPagesResult>;
}
