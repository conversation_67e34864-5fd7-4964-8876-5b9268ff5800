import { IFileUpload } from '../../../../shared/gql-file-upload-convert';
import { UploadParams } from '../../../infrastructure/adapters/saas/s3-storage/s3-file-storage.adapter';

export interface IFileStorageAdapter {
    getSignedUrl(key: string, expiresIn?: number): Promise<string>;
    uploadFile(params: UploadParams): Promise<void>;
    deleteFile(key: string): Promise<void>;
    deleteFiles(listOfKeys: string[]): Promise<void>;
    getFileList(prefix: string): Promise<string[]>;
    uploadWebinarCover(params: { webinarId: string; cover: IFileUpload }): Promise<{ key: string; name: string }>;
    uploadStreamCover(params: {
        webinarId: string;
        streamId: string;
        cover: IFileUpload;
    }): Promise<{ key: string; name: string }>;
    getUrl(key: string): string;
    removeWebinarCovers(webinarId: string): Promise<void>;
    removeStreamCovers(params: { webinarId: string; streamId: string }): Promise<void>;
    removeAllWebinarFiles(webinarId: string): Promise<void>;
}
