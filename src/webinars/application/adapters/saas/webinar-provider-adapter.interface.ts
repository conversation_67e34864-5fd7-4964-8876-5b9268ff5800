import { SaasProviderEnum } from '@prisma/client';

import { RoomParams } from '../../../domain/models/stream/stream-room';
import { WebinarUser } from '../../../domain/models/user/user-abstract';

export interface RoomRecordUrl {
    id: string;
    provider: SaasProviderEnum;
    url: string;
    startedAt: Date;
}

export interface RoomActiveCall {
    active: boolean;
    roomId: string;
    participantsCount: number; // кол-во активных участников в звонке
}

export interface IWebinarProviderAdapter {
    setupWebinarRoom(streamName: string, autoRecord?: boolean): Promise<RoomParams>;
    getWebinarRoomUrl(params: { stream: RoomParams & { streamId: string }; anUser: WebinarUser }): Promise<string>;
    getRoomActiveCall(room: RoomParams): Promise<RoomActiveCall>;
    deleteRoom(room: RoomParams): Promise<void>;
    deleteRooms(rooms: RoomParams[]): Promise<void>;
    getRoomRecords(room: Pick<RoomParams, 'provider' | 'spaceId' | 'roomId'>): Promise<RoomRecordUrl[] | null>;
}
