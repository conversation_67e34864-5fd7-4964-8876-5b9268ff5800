import { SchoolModel, SchoolModelParams } from '../../../domain/models/school.model';
import { StreamModel, StreamParams } from '../../../domain/models/stream/stream.model';
import { WebinarParams } from '../../../domain/models/webinar/webinar.model';

export interface VideoStorageFolderIds {
    webinarFolderId: string;
    streamFolderId: string;
}

export interface VideoAsset {
    fileSize: number;
    quality: 'original' | '1080p' | '720p' | '480p' | '360p';
    createdAt: string;
    downloadLink: string;
}

export interface VideoStorageRecordType {
    id: string;
    title: string;
    description: string;
    createdAt: Date;
    duration: number;
    progress: number;
    tags: string[];
    status: string;
    embedLink: string;
    assets: VideoAsset[];
}

interface FolderParams {
    id: string;
    title: string;
    kinescopeFolderId?: string;
}

export interface VideoStorageFolderParams {
    school: SchoolModelParams | null;
    webinar: FolderParams;
    stream: FolderParams;
}

export interface IVideoStorageAdapter {
    createFolderStructure(params: VideoStorageFolderParams): Promise<VideoStorageFolderIds>;
    createProjectSubFolder(kinescopeProjectId: string, folderName: string): Promise<string>;
    uploadVideoByURL(stream: StreamModel, recordUrl: string): Promise<void>;
    getRecords(stream: Pick<StreamModel, 'room' | 'id' | 'kinescopeFolderId'>): Promise<VideoStorageRecordType[]>;
    deleteRecord(videoId: string): Promise<void>;
    generateEmbedVideoLink(stream: StreamModel): Promise<string>;
    getDownloadVideoLink(
        stream: Pick<StreamModel, 'room' | 'id' | 'kinescopeFolderId' | 'embedRecordUrl'>,
    ): Promise<string>;
    deleteStreamRecords(aSchool: SchoolModel, stream: StreamParams): Promise<void>;
    deleteWebinarRecords(aSchool: SchoolModel, webinar: WebinarParams): Promise<void>;
}
