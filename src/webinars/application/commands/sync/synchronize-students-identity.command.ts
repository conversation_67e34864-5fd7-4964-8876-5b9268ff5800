import { Inject, Logger } from '@nestjs/common';
import { CommandHandler, EventBus, ICommand, ICommandHandler } from '@nestjs/cqrs';
import { ParticipantRole, StatisticRecordType } from '@prisma/client';
import { WebinarsRegistrationDetailsContractNamespace } from '@skillspace/amqp-contracts';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../../core/prisma/otel-prisma.service';
import { StudentsGotIdentifiersEvent } from '../../events/activities/students-got-identifiers.event';

export type LanguageType = 'ru' | 'en' | 'es' | 'kk' | 'uz' | 'de' | 'fr' | 'it';

interface SchoolWhiteLabel {
    hideMobileAppLinks: boolean;
    isWhiteLabel: boolean;
    schoolLogoUrl?: string;
    primaryColor?: string;
}

interface StudentSchool {
    uuid: string;
    name: string;
    email: string;
    whiteLabel?: SchoolWhiteLabel;
    domain: string;
    language: LanguageType;
}

interface StudentIdentifier {
    email: string;
    uuid: string;
    registerToken?: string;
    school: StudentSchool;
}

export interface NewStudent {
    userUuid: string;
    streamId: string;
    currentStatus: StatisticRecordType;
    registerToken?: string;
    school: StudentSchool;
    studentEmail: string;
    studentPhoneNumber?: string;
    language: LanguageType;
}

export class SynchronizeStudentsIdentityCommand implements ICommand {
    constructor(public readonly payload: WebinarsRegistrationDetailsContractNamespace.StudentIdProvidedPayload) {}
}

@CommandHandler(SynchronizeStudentsIdentityCommand)
export class SynchronizeStudentsIdentityHandler implements ICommandHandler<SynchronizeStudentsIdentityCommand> {
    constructor(
        private readonly eventBus: EventBus,
        @Inject(OTEL_PRISMA_SERVICE)
        private readonly prisma: OtelPrismaService,
    ) {}

    private readonly logger = new Logger(SynchronizeStudentsIdentityHandler.name);

    async execute(command: SynchronizeStudentsIdentityCommand) {
        const studentsToUpdate = command.payload.students as StudentIdentifier[];
        const streamId = command.payload.streamId;
        const uuidByEmailMap = new Map<string, string>(
            studentsToUpdate.map((student) => [student.email, student.uuid]),
        );

        const students = await this.prisma.participant.findMany({
            where: {
                streamId,
                email: { in: studentsToUpdate.map((student) => student.email) },
                role: ParticipantRole.STUDENT,
            },
            select: { id: true, streamId: true, email: true, phoneNumber: true, currentStatus: true },
        });

        for (const student of students) {
            const userUuid = uuidByEmailMap.get(student.email);
            await this.prisma.participant.update({
                where: { id: student.id },
                data: { userUuid },
            });
        }

        // Карта email -> данные для писем
        const letterMap = new Map<string, { registerToken?: string; school: StudentSchool }>(
            studentsToUpdate.map((student) => [
                student.email,
                {
                    registerToken: student?.registerToken,
                    school: student.school,
                },
            ]),
        );

        const newStudents: NewStudent[] = students
            .filter(
                ({ currentStatus }) =>
                    currentStatus === StatisticRecordType.REGISTERED || currentStatus === StatisticRecordType.INVITED,
            )
            .map((student) => {
                const userUuid = uuidByEmailMap.get(student.email);
                const { registerToken, school } = letterMap.get(student.email);
                return {
                    userUuid,
                    streamId: student.streamId,
                    currentStatus: student.currentStatus,
                    studentEmail: student.email,
                    studentPhoneNumber: student.phoneNumber,
                    registerToken,
                    school,
                    language: school.language,
                };
            });

        if (newStudents.length > 0) {
            // для уведомления студентов и сервиса статистики
            this.eventBus.publish(new StudentsGotIdentifiersEvent(streamId, newStudents));
        }
    }
}
