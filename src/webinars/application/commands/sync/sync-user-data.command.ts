import { Inject, Logger } from '@nestjs/common';
import { CommandHandler, ICommand, ICommandHandler } from '@nestjs/cqrs';
import { WebinarsRegistrationDetailsContractNamespace } from '@skillspace/amqp-contracts';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../../core/prisma/otel-prisma.service';

export class SyncUserDataCommand implements ICommand {
    constructor(public readonly payload: WebinarsRegistrationDetailsContractNamespace.StudentsUpdatedDataPayload) {}
}

@CommandHandler(SyncUserDataCommand)
export class SyncUserDataHandler implements ICommandHandler<SyncUserDataCommand> {
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private readonly prisma: OtelPrismaService,
    ) {}

    private readonly logger = new Logger(SyncUserDataHandler.name);

    async execute(command: SyncUserDataCommand) {
        const studentsToUpdate = command.payload.students;

        for (const { uuid, email } of studentsToUpdate) {
            await this.prisma.participant.updateMany({
                where: { userUuid: uuid },
                data: { email },
            });
        }
    }
}
