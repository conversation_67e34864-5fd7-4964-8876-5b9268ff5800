import { Inject, Logger } from '@nestjs/common';
import { CommandHandler, ICommand, ICommandHandler } from '@nestjs/cqrs';
import { SaasProviderEnum } from '@prisma/client';

import { RoomStateEnum } from '../../../domain/models/stream/stream-room';
import { IStreamRepository } from '../../../domain/repositories/stream-repository.interface';
import { STREAM_REPOSITORY } from '../../../injects';
import { LiveDigitalWebhookBody } from '../../../presentation/input/live-digital-webhook.dto';

type RoomRecordParams = Omit<LiveDigitalWebhookBody, 'eventName' | 'recordId'> & { provider: SaasProviderEnum };

export class MarkRoomAsLiveCommand implements ICommand {
    constructor(public readonly params: RoomRecordParams) {}
}

@CommandHandler(MarkRoomAsLiveCommand)
export class MarkRoomAsLiveCommandHandler implements ICommandHandler<MarkRoomAsLiveCommand> {
    constructor(
        @Inject(STREAM_REPOSITORY)
        private readonly streamRepo: IStreamRepository,
    ) {}

    private readonly logger = new Logger(MarkRoomAsLiveCommandHandler.name);

    async execute(command: MarkRoomAsLiveCommand) {
        const aStream = await this.streamRepo.findStreamByRoomId(command.params.roomId, command.params);

        const room = aStream.room;
        const anUpdatedStream = aStream.update({ room: { ...room, state: RoomStateEnum.started } });
        await this.streamRepo.update(anUpdatedStream);

        this.logger.log({ streamId: aStream.id, room }, `В комнате вебинара началась трансляция`);
    }
}
