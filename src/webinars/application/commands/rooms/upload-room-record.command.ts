import { Logger } from '@nestjs/common';
import { CommandHandler, ICommand, ICommandHandler } from '@nestjs/cqrs';
import { SaasProviderEnum } from '@prisma/client';

import { TransferService } from '../../services/transfer.service';

export class UploadRoomRecordCommand implements ICommand {
    constructor(
        public readonly params: {
            roomId: string;
            spaceId: string;
            recordId: string;
            provider: SaasProviderEnum;
        },
    ) {}
}

@CommandHandler(UploadRoomRecordCommand)
export class UploadRoomRecordCommandHandler implements ICommandHandler<UploadRoomRecordCommand> {
    constructor(private readonly transferRecordsService: TransferService) {}

    private readonly logger = new Logger(UploadRoomRecordCommandHandler.name);

    async execute(command: UploadRoomRecordCommand) {
        return this.transferRecordsService.transferRoomRecordByWebhook(command.params);
    }
}
