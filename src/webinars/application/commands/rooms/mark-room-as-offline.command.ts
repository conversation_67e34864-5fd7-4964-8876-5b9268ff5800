import { Inject, Logger } from '@nestjs/common';
import { CommandHandler, ICommand, ICommandHandler } from '@nestjs/cqrs';
import { SaasProviderEnum } from '@prisma/client';

import { RoomStateEnum } from '../../../domain/models/stream/stream-room';
import { IStreamRepository } from '../../../domain/repositories/stream-repository.interface';
import { STREAM_REPOSITORY } from '../../../injects';
import { TransferService } from '../../services/transfer.service';

export class MarkRoomAsOfflineCommand implements ICommand {
    constructor(
        public readonly params: {
            roomId: string;
            spaceId: string;
            provider: SaasProviderEnum;
        },
        public readonly currentDate = new Date(),
    ) {}
}

@CommandHandler(MarkRoomAsOfflineCommand)
export class MarkRoomAsOfflineCommandHandler implements ICommandHandler<MarkRoomAsOfflineCommand> {
    constructor(
        @Inject(STREAM_REPOSITORY)
        private readonly streamRepo: IStreamRepository,
        private readonly transferService: TransferService,
    ) {}

    private readonly logger = new Logger(MarkRoomAsOfflineCommandHandler.name);

    async execute(command: MarkRoomAsOfflineCommand) {
        const aStream = await this.streamRepo.findStreamByRoomId(command.params.roomId, command.params);

        const anUpdatedStream = aStream.update({ room: { ...aStream.room, state: RoomStateEnum.finished } });
        await this.streamRepo.update(anUpdatedStream);

        this.logger.log(
            { streamId: aStream.id, room: anUpdatedStream.room },
            `В комнате вебинара завершена трансляция`,
        );
        await this.transferService.syncRecordsAndCloseRoom(aStream);
    }
}
