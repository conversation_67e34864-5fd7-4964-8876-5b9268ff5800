import { Inject, Logger } from '@nestjs/common';
import { CommandHandler, EventBus, ICommand, ICommandHandler } from '@nestjs/cqrs';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../../core/prisma/otel-prisma.service';
import { CustomError, ERROR_CODE } from '../../../../shared/webinar-service.error';
import { WebinarUser } from '../../../domain/models/user/user-abstract';
import { StreamId } from '../../../domain/objects/stream-id';
import { IStreamRepository } from '../../../domain/repositories/stream-repository.interface';
import { IWebinarRepository } from '../../../domain/repositories/webinar-repository.interface';
import { STREAM_REPOSITORY, WEBINAR_REPOSITORY } from '../../../injects';
import { StreamRemovedEvent } from '../../events/streams/stream-removed.event';

export class DeleteStreamCommand implements ICommand {
    constructor(
        public readonly anUser: WebinarUser,
        public readonly aStreamId: StreamId,
    ) {}
}

@CommandHandler(DeleteStreamCommand)
export class DeleteStreamHandler implements ICommandHandler<DeleteStreamCommand> {
    constructor(
        private readonly eventBus: EventBus,
        @Inject(STREAM_REPOSITORY)
        private readonly streamRepository: IStreamRepository,
        @Inject(WEBINAR_REPOSITORY)
        private readonly webinarRepository: IWebinarRepository,
        @Inject(OTEL_PRISMA_SERVICE)
        private readonly prisma: OtelPrismaService,
    ) {}

    private readonly logger = new Logger(DeleteStreamHandler.name);

    async execute(command: DeleteStreamCommand) {
        const { anUser, aStreamId } = command;
        const streamId = aStreamId.unwrap();

        const aStream = await this.streamRepository.getStream(aStreamId);
        if (!aStream) {
            throw new CustomError('Поток вебинара не найден при попытке удаления', {
                code: ERROR_CODE.NOT_FOUND_ERROR,
                details: { streamId },
            });
        }

        const aWebinarParams = await this.webinarRepository.getWebinarParams(aStream.aWebinarId);
        if (!anUser.hasPermissionToRemove(aWebinarParams)) {
            throw new CustomError('Недостаточно прав для удаления потока вебинара', {
                code: ERROR_CODE.BAD_REQUEST_ERROR,
                details: { streamId },
            });
        }

        const webinarId = aStream.webinarId;
        const streamCount = await this.prisma.stream.count({ where: { webinarId } });

        if (streamCount === 1) {
            throw new CustomError('Нельзя удалить последний поток из вебинара', {
                code: ERROR_CODE.BAD_REQUEST_ERROR,
                details: { webinarId, streamId },
            });
        }

        await this.streamRepository.delete(aStreamId);
        this.eventBus.publish(new StreamRemovedEvent({ aWebinar: aWebinarParams, aStream }));
    }
}
