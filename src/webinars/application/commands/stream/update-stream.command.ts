import { Inject, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommand, ICommandHandler } from '@nestjs/cqrs';
import { StreamType } from '@prisma/client';

import { CustomError, ERROR_CODE } from '../../../../shared/webinar-service.error';
import { StreamModel, StreamUpdateParams } from '../../../domain/models/stream/stream.model';
import { WebinarUser } from '../../../domain/models/user/user-abstract';
import { StreamId } from '../../../domain/objects/stream-id';
import { IStreamRepository } from '../../../domain/repositories/stream-repository.interface';
import { IWebinarRepository } from '../../../domain/repositories/webinar-repository.interface';
import { STREAM_REPOSITORY, WEBINAR_PROVIDER_ADAPTER, WEBINAR_REPOSITORY } from '../../../injects';
import { IWebinarProviderAdapter } from '../../adapters/saas/webinar-provider-adapter.interface';
import { PlansService } from '../../services/plans.service';

export class UpdateStreamCommand implements ICommand {
    constructor(
        public readonly anUser: WebinarUser,
        public readonly anStreamId: StreamId,
        public readonly params: StreamUpdateParams,
    ) {}
}

@CommandHandler(UpdateStreamCommand)
export class UpdateStreamHandler implements ICommandHandler<UpdateStreamCommand> {
    constructor(
        private readonly publisher: EventPublisher,
        @Inject(STREAM_REPOSITORY)
        private readonly streamRepository: IStreamRepository,
        @Inject(WEBINAR_REPOSITORY)
        private readonly webinarRepository: IWebinarRepository,
        @Inject(WEBINAR_PROVIDER_ADAPTER)
        private readonly webinarProvider: IWebinarProviderAdapter,
        private readonly plansService: PlansService,
    ) {}

    private readonly logger = new Logger(UpdateStreamHandler.name);

    async execute(command: UpdateStreamCommand): Promise<StreamModel> {
        const { anUser, anStreamId, params } = command;

        const aStream = await this.streamRepository.getStream(anStreamId);
        if (!aStream) {
            throw new Error(`Stream ${anStreamId.unwrap()} not found`);
        }

        const aWebinar = await this.webinarRepository.getWebinar(aStream.aWebinarId, anUser);
        if (!anUser.hasPermissionToModify(aWebinar)) {
            throw new CustomError('Недостаточно прав для изменения настроек потока', {
                code: ERROR_CODE.FORBIDDEN_ERROR,
                details: {
                    streamId: anStreamId.unwrap(),
                    user: anUser.toLog(),
                },
            });
        }

        const updateToInternal = !aStream.isInternal && params.streamType === StreamType.INTERNAL;
        if (updateToInternal && !aStream.room) {
            await this.plansService.checkInternalWebinarFeatureAccess(anUser.params.schoolUuid);
            const room = await this.webinarProvider.setupWebinarRoom(params.title, params.autoRecord);
            return this.updateStream(aStream.update({ ...params, room }));
        } else {
            const anUpdatedStream = aStream.update(params);
            return this.updateStream(anUpdatedStream);
        }
    }

    private async updateStream(aStream: StreamModel): Promise<StreamModel> {
        this.publisher.mergeObjectContext(aStream);
        await this.streamRepository.update(aStream);
        aStream.commit();
        this.logger.log({ ...aStream.logParams }, `Обновлен поток вебинара "${aStream.params.title}"`);
        return aStream;
    }
}
