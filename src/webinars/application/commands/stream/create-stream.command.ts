import { Inject, Logger } from '@nestjs/common';
import { CommandHandler, EventBus, ICommand, ICommandHandler } from '@nestjs/cqrs';
import { StreamType } from '@prisma/client';

import { CustomError, ERROR_CODE } from '../../../../shared/webinar-service.error';
import { StreamCreateParams, StreamModel } from '../../../domain/models/stream/stream.model';
import { WebinarUser } from '../../../domain/models/user/user-abstract';
import { StreamId } from '../../../domain/objects/stream-id';
import { WebinarId } from '../../../domain/objects/webinar-id';
import { IStreamRepository } from '../../../domain/repositories/stream-repository.interface';
import { IWebinarRepository } from '../../../domain/repositories/webinar-repository.interface';
import { STREAM_REPOSITORY, WEBINAR_PROVIDER_ADAPTER, WEBINAR_REPOSITORY } from '../../../injects';
import { IWebinarProviderAdapter } from '../../adapters/saas/webinar-provider-adapter.interface';
import { StreamCreatedEvent } from '../../events/streams/strem-created.event';
import { ContentService } from '../../services/content.service';
import { PlansService } from '../../services/plans.service';

export class CreateStreamCommand implements ICommand {
    constructor(
        public readonly anUser: WebinarUser,
        public readonly params: StreamCreateParams,
    ) {}
}

@CommandHandler(CreateStreamCommand)
export class CreateStreamHandler implements ICommandHandler<CreateStreamCommand, StreamId> {
    constructor(
        public readonly eventBus: EventBus,
        @Inject(STREAM_REPOSITORY)
        private readonly streamRepository: IStreamRepository,
        @Inject(WEBINAR_REPOSITORY)
        private readonly webinarRepository: IWebinarRepository,
        @Inject(WEBINAR_PROVIDER_ADAPTER)
        private readonly webinarProvider: IWebinarProviderAdapter,
        private readonly plansService: PlansService,
        private readonly contentService: ContentService,
    ) {}

    private readonly logger = new Logger(CreateStreamHandler.name);

    async execute(command: CreateStreamCommand): Promise<StreamId> {
        const { anUser, params } = command;

        const aWebinarId = WebinarId.wrap(params.webinarId);
        const aWebinar = await this.webinarRepository.getWebinarParams(aWebinarId);
        if (!aWebinar) {
            throw new CustomError('Вебинар не найден', {
                code: ERROR_CODE.NOT_FOUND_ERROR,
                details: { webinarId: params.webinarId },
            });
        }

        if (!anUser.hasPermissionToCreate() || !anUser.hasPermissionToModify(aWebinar)) {
            throw new CustomError('Недостаточно прав для создания вебинара', {
                code: ERROR_CODE.FORBIDDEN_ERROR,
                details: { user: anUser.toLog() },
            });
        }

        const anInitialStream = StreamModel.create(params);
        const page = await this.contentService.addPage(anInitialStream.anId.unwrap());

        if (params.streamType === StreamType.INTERNAL) {
            await this.plansService.checkInternalWebinarFeatureAccess(anUser.params.schoolUuid);
            const room = await this.webinarProvider.setupWebinarRoom(params.title, params.autoRecord);
            const aStream = StreamModel.from(anInitialStream.anId, {
                ...anInitialStream.params,
                pages: [page.pageId],
                room,
            });
            return this.createStream(aStream);
        }

        const aStream = StreamModel.from(anInitialStream.anId, {
            ...anInitialStream.params,
            pages: [page.pageId],
        });
        return this.createStream(aStream);
    }

    private async createStream(aStream: StreamModel) {
        await this.streamRepository.create(aStream);
        this.eventBus.publish(new StreamCreatedEvent(aStream));
        this.logger.log({ ...aStream.logParams }, `Создан поток вебинара "${aStream.params.title}"`);
        return aStream.anId;
    }
}
