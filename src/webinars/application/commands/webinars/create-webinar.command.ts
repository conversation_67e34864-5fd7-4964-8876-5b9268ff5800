import { Inject, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommand, ICommandHandler } from '@nestjs/cqrs';
import { StreamType } from '@prisma/client';

import { RoomParams } from '../../../domain/models/stream/stream-room';
import { WebinarUser } from '../../../domain/models/user/user-abstract';
import { WebinarCreateParams, WebinarModel } from '../../../domain/models/webinar/webinar.model';
import { WebinarId } from '../../../domain/objects/webinar-id';
import { IWebinarRepository } from '../../../domain/repositories/webinar-repository.interface';
import { WEBINAR_PROVIDER_ADAPTER, WEBINAR_REPOSITORY } from '../../../injects';
import { IWebinarProviderAdapter } from '../../adapters/saas/webinar-provider-adapter.interface';
import { ContentService } from '../../services/content.service';
import { PlansService } from '../../services/plans.service';

export class CreateWebinarCommand implements ICommand {
    constructor(
        public readonly anUser: WebinarUser,
        public readonly params: WebinarCreateParams,
    ) {}
}

@CommandHandler(CreateWebinarCommand)
export class CreateWebinarHandler implements ICommandHandler<CreateWebinarCommand> {
    constructor(
        private readonly publisher: EventPublisher,
        @Inject(WEBINAR_REPOSITORY)
        private readonly webinarRepository: IWebinarRepository,
        @Inject(WEBINAR_PROVIDER_ADAPTER)
        private readonly webinarProvider: IWebinarProviderAdapter,
        private readonly plansService: PlansService,
        private readonly contentService: ContentService,
    ) {}

    private readonly logger = new Logger(CreateWebinarHandler.name);

    async execute(command: CreateWebinarCommand): Promise<string> {
        const { anUser, params } = command;

        const anInitialWebinar = WebinarModel.create(anUser, params);

        const streams = await Promise.all(
            anInitialWebinar.params.streams.map(async (stream) => {
                let room: RoomParams = null;
                if (stream.streamType === StreamType.INTERNAL) {
                    await this.plansService.checkInternalWebinarFeatureAccess(anUser.params.schoolUuid);
                    room = await this.webinarProvider.setupWebinarRoom(params.title, stream.autoRecord);
                }
                const page = await this.contentService.addPage(stream.id);
                return room ? { ...stream, pages: [page.pageId], room } : { ...stream, pages: [page.pageId] };
            }),
        );

        const aWebinar = WebinarModel.from(WebinarId.wrap(anInitialWebinar.id), {
            ...anInitialWebinar.params,
            streams,
        });
        this.publisher.mergeObjectContext(aWebinar);
        await this.webinarRepository.create(aWebinar);
        aWebinar.commit();
        return aWebinar.id;
    }
}
