import { Inject, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ler, EventPublisher, ICommand, ICommandHandler } from '@nestjs/cqrs';

import { CustomError, ERROR_CODE } from '../../../../shared/webinar-service.error';
import { WebinarUser } from '../../../domain/models/user/user-abstract';
import { WebinarUpdateParams } from '../../../domain/models/webinar/webinar.model';
import { WebinarId } from '../../../domain/objects/webinar-id';
import { IWebinarRepository } from '../../../domain/repositories/webinar-repository.interface';
import { WEBINAR_REPOSITORY } from '../../../injects';

export class UpdateWebinarCommand implements ICommand {
    constructor(
        public readonly anUser: WebinarUser,
        public readonly webinarId: string,
        public readonly params: WebinarUpdateParams,
    ) {}
}

@CommandHandler(UpdateWebinarCommand)
export class UpdateWebinarHandler implements ICommandHandler<UpdateWebinarCommand> {
    constructor(
        private readonly publisher: EventPublisher,
        @Inject(WEBINAR_REPOSITORY)
        private readonly webinarRepository: IWebinarRepository,
    ) {}

    private readonly logger = new Logger(UpdateWebinarHandler.name);

    async execute(command: UpdateWebinarCommand) {
        const { anUser, params } = command;
        const aWebinarId = WebinarId.create(command.webinarId);
        const webinarId = aWebinarId.unwrap();

        const aWebinar = await this.webinarRepository.getWebinarParams(aWebinarId);

        if (!aWebinar) {
            throw new CustomError('Вебинар не найден', {
                code: ERROR_CODE.NOT_FOUND_ERROR,
                details: { webinarId },
            });
        }

        this.publisher.mergeObjectContext(aWebinar);
        const updatedWebinar = aWebinar.update(anUser, params);
        await this.webinarRepository.update(updatedWebinar);
        aWebinar.commit();
    }
}
