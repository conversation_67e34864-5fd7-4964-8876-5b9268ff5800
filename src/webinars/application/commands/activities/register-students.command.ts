import { Inject, Logger } from '@nestjs/common';
import { CommandHandler, EventBus, ICommand, ICommandHandler } from '@nestjs/cqrs';
import { ParticipantRole, RegistrationType, StatisticRecordType } from '@prisma/client';
import { z } from 'zod';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../../core/prisma/otel-prisma.service';
import { CustomError, ERROR_CODE } from '../../../../shared/webinar-service.error';
import { WebinarUser } from '../../../domain/models/user/user-abstract';
import { StreamId } from '../../../domain/objects/stream-id';
import { IStreamRepository } from '../../../domain/repositories/stream-repository.interface';
import { IWebinarRepository } from '../../../domain/repositories/webinar-repository.interface';
import { CAPTCHA_SERVICE_ADAPTER, STREAM_REPOSITORY, WEBINAR_REPOSITORY } from '../../../injects';
import { ICaptchaServiceAdapter } from '../../adapters/saas/captcha-service-adapter.interface';
import { StudentRegisteredEvent } from '../../events/activities/student-registered.event';

export const phoneSchema = z.preprocess(
    // Предобработка: удаление пробелов, скобок, дефисов и точек
    (value) => {
        if (typeof value === 'string') {
            // eslint-disable-next-line no-useless-escape
            return value.replace(/[\s\-\(\)\.]/g, '');
        }
        return value;
    },
    z
        .string({ required_error: 'Телефонный номер обязателен' })
        .min(10, { message: 'Номер слишком короткий' })
        .max(20, { message: 'Номер слишком длинный' })
        .regex(/^\+[1-9]\d{0,2}[-.\s]?\(?\d{1,4}\)?[-.\s]?\d{1,4}[-.\s]?\d{1,9}$/, {
            message: 'Неверный формат телефонного номера',
        }),
);

export class RegisterStudentCommand implements ICommand {
    public readonly aStreamId: StreamId;
    public readonly studentEmail: string;
    public readonly studentName: string;
    public readonly studentTimeZone: string;
    public readonly phoneNumber: string;
    public readonly token: string;
    public readonly ip: string;

    constructor(
        public readonly anUser: WebinarUser,
        public readonly params: {
            aStreamId: StreamId;
            studentEmail: string;
            studentName: string;
            studentTimeZone: string;
            phoneNumber?: string;
            token?: string;
            ip?: string;
        },
        public readonly currentDate = new Date(),
    ) {
        this.aStreamId = params.aStreamId;
        this.studentEmail = params.studentEmail;
        this.studentName = params.studentName;
        this.studentTimeZone = params.studentTimeZone;
        this.phoneNumber = params?.phoneNumber;
        this.token = params?.token;
        this.ip = params?.ip;
    }
}

@CommandHandler(RegisterStudentCommand)
export class RegisterStudentHandler implements ICommandHandler<RegisterStudentCommand> {
    constructor(
        private readonly eventBus: EventBus,
        @Inject(WEBINAR_REPOSITORY)
        private readonly webinarRepository: IWebinarRepository,
        @Inject(STREAM_REPOSITORY)
        private readonly streamRepository: IStreamRepository,
        @Inject(CAPTCHA_SERVICE_ADAPTER)
        private readonly captchaService: ICaptchaServiceAdapter,
        @Inject(OTEL_PRISMA_SERVICE)
        private readonly prisma: OtelPrismaService,
    ) {}

    private readonly logger = new Logger(RegisterStudentHandler.name);

    async execute(command: RegisterStudentCommand) {
        const { aStreamId, studentEmail, studentName, studentTimeZone, currentDate, anUser } = command;

        // Для регистрации гостя требую капчу
        // if (anUser.isGuest) {
        //     if (!command.token) {
        //         throw new CustomError('Для регистрации необходима капча', {
        //             code: ERROR_CODE.BAD_REQUEST_ERROR,
        //             details: { studentEmail, token: command.token, user: anUser.toLog() },
        //         });
        //     }
        //     const isValidCaptcha = await this.captchaService.validateCaptcha(command.token, command.ip);
        //     if (!isValidCaptcha) {
        //         throw new CustomError('Ошибка валидации капчи', {
        //             code: ERROR_CODE.BAD_REQUEST_ERROR,
        //             details: { studentEmail, token: command.token, user: anUser.toLog() },
        //         });
        //     }
        // }

        // TODO: заменить на получение WebinarStream
        const aStream = await this.streamRepository.getStream(aStreamId);
        if (!aStream) {
            throw new CustomError('Поток вебинара не найден при регистрации', {
                code: ERROR_CODE.NOT_FOUND_ERROR,
                details: { streamId: aStreamId.unwrap(), user: anUser.toLog() },
            });
        }
        const aWebinar = await this.webinarRepository.getWebinarParams(aStream.aWebinarId);

        // если при регистрации требуется телефон
        if (aWebinar.isPhoneRequiredOnRegistration) {
            phoneSchema.parse(command.phoneNumber);
        }

        const streamId = aStreamId.unwrap();
        const webinarId = aStream.aWebinarId.unwrap();

        // Проверяем приглашение
        const participant = await this.prisma.participant.findFirst({
            where: { streamId, email: studentEmail },
        });

        if (!participant) {
            // Проверяем возможность регистрации на вебинар для случая с приглашением
            if (aWebinar.params.registrationType === RegistrationType.INVITATION) {
                // TODO: заменить на вызов метода, а не чтение параметра
                throw new CustomError('Регистрация доступна только для приглашенных участников', {
                    code: ERROR_CODE.BAD_REQUEST_ERROR,
                    details: {
                        streamId,
                        webinarId,
                        studentEmail,
                        user: anUser.toLog(),
                    },
                });
            }

            await this.prisma.participant.create({
                data: {
                    streamId,
                    email: studentEmail,
                    name: studentName,
                    timezone: studentTimeZone,
                    phoneNumber: command.phoneNumber,
                    role: ParticipantRole.STUDENT,
                    currentStatus: StatisticRecordType.REGISTERED,
                    statistics: [
                        {
                            type: StatisticRecordType.REGISTERED,
                            date: currentDate,
                        },
                    ],
                },
            });

            this.eventBus.publish(
                new StudentRegisteredEvent({
                    aStream,
                    studentEmail,
                    studentName,
                    studentPhoneNumber: command.phoneNumber,
                    studentTimeZone,
                }),
            );
        } else {
            await this.prisma.participant.update({
                where: {
                    id: participant.id,
                },
                data: {
                    name: studentName,
                    timezone: studentTimeZone,
                    phoneNumber: command.phoneNumber,
                    currentStatus: StatisticRecordType.REGISTERED,
                    statistics: {
                        push: {
                            type: StatisticRecordType.REGISTERED,
                            date: currentDate,
                        },
                    },
                },
            });
        }

        this.logger.log(
            { streamId, email: studentEmail, name: studentName },
            `Студент ${studentEmail} зарегистрирован на вебинар`,
        );
    }
}
