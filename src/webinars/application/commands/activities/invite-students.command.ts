import { Inject, Logger } from '@nestjs/common';
import { CommandHandler, EventBus, ICommand, ICommandHandler } from '@nestjs/cqrs';
import { ParticipantRole, StatisticRecordType } from '@prisma/client';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../../core/prisma/otel-prisma.service';
import { CustomError, ERROR_CODE } from '../../../../shared/webinar-service.error';
import { WebinarUser } from '../../../domain/models/user/user-abstract';
import { StreamId } from '../../../domain/objects/stream-id';
import { IStreamRepository } from '../../../domain/repositories/stream-repository.interface';
import { IWebinarRepository } from '../../../domain/repositories/webinar-repository.interface';
import { STREAM_REPOSITORY, WEBINAR_REPOSITORY } from '../../../injects';
import { StudentsInvitedEvent } from '../../events/activities/students-invited.event';

export class InviteStudentsCommand implements ICommand {
    constructor(
        public readonly anUser: WebinarUser,
        public readonly aStreamId: StreamId,
        public readonly studentEmails: string[],
        public readonly currentDate = new Date(),
    ) {}
}

@CommandHandler(InviteStudentsCommand)
export class InviteStudentsHandler implements ICommandHandler<InviteStudentsCommand, number> {
    constructor(
        private readonly eventBus: EventBus,
        @Inject(STREAM_REPOSITORY)
        private readonly streamRepository: IStreamRepository,
        @Inject(WEBINAR_REPOSITORY)
        private readonly webinarRepository: IWebinarRepository,
        @Inject(OTEL_PRISMA_SERVICE)
        private readonly prisma: OtelPrismaService,
    ) {}

    private readonly logger = new Logger(InviteStudentsHandler.name);

    async execute(command: InviteStudentsCommand): Promise<number> {
        const { anUser, aStreamId, studentEmails, currentDate } = command;
        const streamId = aStreamId.unwrap();

        const aStream = await this.streamRepository.getStream(aStreamId);
        if (!aStream) {
            throw new CustomError('Поток вебинара не найден при приглашении участников', {
                code: ERROR_CODE.NOT_FOUND_ERROR,
                details: { streamId },
            });
        }

        const aWebinar = await this.webinarRepository.getWebinarParams(aStream.aWebinarId);
        if (!anUser.hasPermissionToModify(aWebinar)) {
            throw new CustomError('Недостаточно прав для изменения параметров вебинара', {
                code: ERROR_CODE.FORBIDDEN_ERROR,
                details: { streamId, user: anUser.toLog() },
            });
        }

        const studentsNotYetInvited = await this.excludeAlreadyInvitedStudents(studentEmails, aStreamId, currentDate);

        const newStudentEmails = studentsNotYetInvited.map((student) => student.email);

        this.eventBus.publish(new StudentsInvitedEvent(aWebinar.aSchoolUuid, aStreamId, newStudentEmails));
        return studentsNotYetInvited.length;
    }

    private async excludeAlreadyInvitedStudents(studentEmails: string[], aStreamId: StreamId, currentDate: Date) {
        const invitedStudents: { email: string }[] = [];

        // проверяем был ли уже приглашен или зарегистрирован
        for (const studentEmail of studentEmails) {
            const participant = await this.prisma.participant.findFirst({
                where: {
                    streamId: aStreamId.unwrap(),
                    email: studentEmail,
                },
            });
            if (!participant) {
                const invitedStudentRecord = {
                    streamId: aStreamId.unwrap(),
                    email: studentEmail,
                    role: ParticipantRole.STUDENT,
                    currentStatus: StatisticRecordType.INVITED,
                    statistics: [
                        {
                            type: StatisticRecordType.INVITED,
                            date: currentDate,
                        },
                    ],
                };
                await this.prisma.participant.create({
                    data: invitedStudentRecord,
                });
                invitedStudents.push(invitedStudentRecord);
            }
        }
        return invitedStudents;
    }
}
