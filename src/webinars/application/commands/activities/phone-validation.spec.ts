import { phoneSchema } from './register-students.command';

describe('Phone Schema Validation', () => {
    // не проходят '****** 123 45 67', '+380 95 123 45 67'
    it.each(['****** 567 890', '+44 20 7946 0958', '+375 29 6959016', '+375 29 59 59 012', '****** 109 98 69'])(
        'should validate a valid international phone number %s',
        (number) => {
            const result = phoneSchema.safeParse(number);
            expect(result.success).toBe(true);
        },
    );

    it.each(['+375295959012', '+74951099869'])('should validate a valid international phone number %s', (number) => {
        const result = phoneSchema.safeParse(number);
        expect(result.success).toBe(true);
    });

    it.each([
        'invalid number', // Невалидный формат
        '1234567890', // Отсутствует код страны
        '+12345678901234567890', // Слишком длинный номер
        '******', // Слишком короткий номер
        '', // Пустая строка
    ])('should reject invalid phone numbers %s', (number) => {
        const result = phoneSchema.safeParse(number);
        expect(result.success).toBe(false);
    });

    it.each([
        {
            input: '',
            expectedError: 'Номер слишком короткий',
        },
        {
            input: '1234567890',
            expectedError: 'Неверный формат телефонного номера',
        },
        {
            input: '******',
            expectedError: 'Номер слишком короткий',
        },
        {
            input: '+12345678901234567890',
            expectedError: 'Номер слишком длинный',
        },
    ])('should provide meaningful error messages for invalid numbers %s', ({ input, expectedError }) => {
        const result = phoneSchema.safeParse(input);
        expect(result.error.errors[0].message).toBe(expectedError);
    });
});
