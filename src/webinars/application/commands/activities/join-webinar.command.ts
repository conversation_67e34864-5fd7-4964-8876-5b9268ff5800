import { Inject, Logger } from '@nestjs/common';
import { CommandHandler, EventBus, ICommand, ICommandHandler } from '@nestjs/cqrs';
import { RegistrationType, StatisticRecordType } from '@prisma/client';
import { UserContext } from '@skillspace/access';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../../core/prisma/otel-prisma.service';
import { CustomError, ERROR_CODE } from '../../../../shared/webinar-service.error';
import { StreamModel, StreamParams } from '../../../domain/models/stream/stream.model';
import { StudentStatusOnStream } from '../../../domain/models/student/student-status-on-stream';
import { UserFactory } from '../../../domain/models/user/user.factory';
import { WebinarUser } from '../../../domain/models/user/user-abstract';
import { WebinarModel } from '../../../domain/models/webinar/webinar.model';
import { StreamId } from '../../../domain/objects/stream-id';
import { UserUuid } from '../../../domain/objects/user-uuid';
import { IParticipantRepository } from '../../../domain/repositories/participant-repository.interface';
import { IStreamRepository } from '../../../domain/repositories/stream-repository.interface';
import { IStudentWebinarsHistoryRepository } from '../../../domain/repositories/student-webinars-history-repository.interface';
import { IWebinarRepository } from '../../../domain/repositories/webinar-repository.interface';
import {
    PARTICIPANT_REPOSITORY,
    STREAM_REPOSITORY,
    STUDENT_WEBINARS_HISTORY_REPOSITORY,
    WEBINAR_PROVIDER_ADAPTER,
    WEBINAR_REPOSITORY,
} from '../../../injects';
import { IWebinarProviderAdapter } from '../../adapters/saas/webinar-provider-adapter.interface';
import { StudentJoinedStreamEvent } from '../../events/activities/student-joined-stream.event';
import { StudentReplayedRecordEvent } from '../../events/activities/student-replayed-record.event';
import { StreamQueryResult } from '../../queries/result/stream-query.result';
import { PlansService } from '../../services/plans.service';

export class JoinWebinarCommand implements ICommand {
    constructor(
        public readonly user: UserContext,
        public readonly schoolUuid: string,
        public readonly streamId: string,
        public readonly email: string, // предоставляет гость, который не зарегистрирован на платформе
        public readonly currentDate: Date = new Date(),
    ) {}
}

@CommandHandler(JoinWebinarCommand)
export class JoinWebinarCommandHandler implements ICommandHandler<JoinWebinarCommand> {
    private readonly logger = new Logger(JoinWebinarCommandHandler.name);

    constructor(
        @Inject(STREAM_REPOSITORY)
        private readonly streamRepo: IStreamRepository,
        @Inject(WEBINAR_REPOSITORY)
        private readonly webinarRepo: IWebinarRepository,
        @Inject(WEBINAR_PROVIDER_ADAPTER)
        private readonly webinarProvider: IWebinarProviderAdapter,
        @Inject(PARTICIPANT_REPOSITORY)
        private readonly participantRepo: IParticipantRepository,
        @Inject(OTEL_PRISMA_SERVICE) private readonly prisma: OtelPrismaService,
        @Inject(STUDENT_WEBINARS_HISTORY_REPOSITORY)
        private readonly studentWebinarRepo: IStudentWebinarsHistoryRepository,
        private readonly plansService: PlansService,
        private readonly eventBus: EventBus,
    ) {}

    async execute(command: JoinWebinarCommand): Promise<StreamQueryResult> {
        const { user, schoolUuid, streamId, email, currentDate } = command;
        const aStreamId = StreamId.create(streamId);
        const aStream = await this.streamRepo.getStream(aStreamId, email);
        if (!aStream) {
            throw new CustomError('Поток вебинара не найден при подключении к вебинару', {
                code: ERROR_CODE.NOT_FOUND_ERROR,
                details: { streamId },
            });
        }

        const aWebinar = await this.webinarRepo.getWebinarParams(aStream.aWebinarId);
        const anUser = UserFactory.create({ ...user, schoolUuid });

        // пропускаем сотрудника
        if (anUser.isEmployee()) {
            const logEmployee = { streamId, email, user: anUser.toLog() };
            if (aStream.isAllowedToJoin(anUser, currentDate)) {
                this.logger.log(logEmployee, `На вебинар пришел сотрудник ${user.email}`);
                return this.joinWebinar(aWebinar, aStream, anUser);
            }

            if (aStream.streamingIsOver(currentDate)) {
                this.logger.log(logEmployee, `Сотрудник ${user.email} зашел посмотреть запись`);
                return this.viewWebinarRecord(aWebinar, aStream, anUser);
            }

            return this.showTeaser(aWebinar, aStream, anUser);
        }

        const aStudentStatusOnStream = await this.participantRepo.getStudentStatusOnStreamOrNull(aStreamId, email);
        if (!aStudentStatusOnStream) {
            throw new CustomError('Пользователь не зарегистрирован на вебинар', {
                code: ERROR_CODE.FORBIDDEN_ERROR,
                details: { streamId, email },
            });
        }

        let studentId: string = null;
        if (!aStudentStatusOnStream.uuid) {
            this.logger.log({ email }, `У пользователя нет идентификатора. Поиск в базе...`);
            const student = await this.participantRepo.getStudentInfoOrNull(streamId, email);
            if (!student) {
                this.logger.log(
                    {
                        code: ERROR_CODE.INTEGRATION_ERROR,
                        details: { email, streamId, name: aStudentStatusOnStream.name },
                    },
                    `Студент ${email} не получил идентификатор на момент подключения к вебинару`,
                );
            } else {
                studentId = student.userUuid;
            }
        }

        const aStudent = studentId
            ? UserFactory.create({
                  id: studentId,
                  name: aStudentStatusOnStream.name,
                  email,
                  role: 'ROLE_STUDENT',
                  schoolUuid,
              })
            : UserFactory.create({ email, name: aStudentStatusOnStream.name ?? 'guest', schoolUuid });

        // проверяем регистрацию на вебинар по приглашению
        if (aWebinar.params.registrationType === RegistrationType.INVITATION) {
            if (!aStudentStatusOnStream?.hasRegistered) {
                throw new CustomError('Вебинар доступен только по приглашению', {
                    code: ERROR_CODE.FORBIDDEN_ERROR,
                    details: { streamId, ...aStudent.toLog() },
                });
            }
        }

        if (aStream.isAllowedToJoin(aStudent, currentDate)) {
            if (aStream.isInternal) {
                await this.validateParticipantsLimit(aStudent, aStream);
            }
            // Предположение
            // к этому моменту комната может быть уже закрыта, но хук еще не сработал
            // это приведет к попытке подключения к трансляции и ошибке генерации токена (временно - пока не отработает хук)

            // записываем статистику
            await this.trackAttendance(aStudentStatusOnStream, aStream, email, currentDate);
            this.logger.log({ streamId, ...aStudent.toLog() }, `На вебинар пришел студент ${email}`);
            // подключаем к трансляции
            return this.joinWebinar(aWebinar, aStream, aStudent);
        }

        if (aStream.streamingIsOver(currentDate)) {
            await this.trackRecordReplay(aStudentStatusOnStream, streamId, email, currentDate);
            this.logger.log({ streamId, ...aStudent.toLog() }, `Студент ${email} зашел посмотреть запись`);
            return this.viewWebinarRecord(aWebinar, aStream, aStudent);
        }

        return this.showTeaser(aWebinar, aStream, aStudent);
    }

    private async validateParticipantsLimit(anUser: WebinarUser, anStream: StreamModel): Promise<void> {
        let participantsCount = 0;

        try {
            const activeCall = await this.webinarProvider.getRoomActiveCall(anStream.room);
            participantsCount = activeCall?.participantsCount ?? 0;
        } catch (e) {
            if (e instanceof Error) {
                this.logger.warn(
                    `Ошибка при получении количества участников в комнате ${anStream.room.roomId}: ${e.message}`,
                );
            }
        }

        const { schoolUuid } = anUser.params;
        const limit = await this.plansService.getParticipantsLimit(schoolUuid);
        const details = { schoolUuid, limit, participantsCount };

        if (limit < participantsCount + 1) {
            throw new CustomError('Превышен лимит активных участников', { code: ERROR_CODE.FORBIDDEN_ERROR, details });
        }

        this.logger.log(details, `Количество активных участников трансляции: ${participantsCount}`);
    }

    private async joinWebinar(aWebinar: WebinarModel, aStream: StreamModel, anUser: WebinarUser) {
        const streamToWatch: StreamParams & { id: string } = {
            ...aStream.params,
            id: aStream.anId.unwrap(),
            date: aStream.params.date,
            participants: [...aStream.params.participants, ...aStream.students],
        };
        const webinarRoomUrl = await this.getWebinarRoomUrl(aStream, anUser);
        return new StreamQueryResult(anUser, aWebinar, {
            ...streamToWatch,
            savedRecordUrl: null,
            recordUrl: null,
            externalStreamUrl: webinarRoomUrl,
        });
    }

    private viewWebinarRecord(aWebinar: WebinarModel, aStream: StreamModel, anUser: WebinarUser) {
        const streamToWatch: StreamParams & { id: string } = {
            ...aStream.params,
            id: aStream.anId.unwrap(),
            date: aStream.params.date,
            participants: [...aStream.params.participants, ...aStream.students],
            externalStreamUrl: null,
        };
        return new StreamQueryResult(anUser, aWebinar, streamToWatch);
    }

    private showTeaser(aWebinar: WebinarModel, aStream: StreamModel, anUser: WebinarUser) {
        const streamToWatch = {
            ...aStream.params,
            id: aStream.anId.unwrap(),
            date: aStream.params.date,
            participants: [...aStream.params.participants, ...aStream.students],
            savedRecordUrl: null,
            recordUrl: null,
            externalStreamUrl: null,
        };
        return new StreamQueryResult(anUser, aWebinar, streamToWatch);
    }

    private async getWebinarRoomUrl(aStream: StreamModel, anUser: WebinarUser): Promise<string> {
        if (aStream.isInternal) {
            return this.webinarProvider.getWebinarRoomUrl({
                stream: { ...aStream.room, streamId: aStream.anId.unwrap() },
                anUser,
            });
        }
        return aStream.params.externalStreamUrl;
    }

    private async trackRecordReplay(
        aStudentStatusOnStream: StudentStatusOnStream,
        streamId: string,
        email: string,
        currentDate: Date,
    ) {
        if (!aStudentStatusOnStream) return;

        await this.prisma.participant.update({
            where: { id: aStudentStatusOnStream.params.id },
            data: {
                currentStatus: StatisticRecordType.REPLAYED,
                statistics: {
                    push: {
                        type: StatisticRecordType.REPLAYED,
                        date: currentDate,
                    },
                },
            },
        });

        this.eventBus.publish(
            new StudentReplayedRecordEvent({
                studentId: aStudentStatusOnStream.uuid,
                studentEmail: email,
                streamId,
            }),
        );
    }

    private async trackAttendance(
        aStudentStatusOnStream: StudentStatusOnStream,
        aStream: StreamModel,
        email: string,
        currentDate: Date,
    ) {
        if (!aStudentStatusOnStream) return;

        const aStudentWebinarHistory = await this.studentWebinarRepo.getStudentWebinarHistory(
            aStream.aWebinarId,
            email,
        );

        const attendancePoints = aStudentWebinarHistory.getEarnedPoints();

        await this.prisma.participant.update({
            where: { id: aStudentStatusOnStream.params.id },
            data: {
                currentStatus: StatisticRecordType.ATTENDED,
                isViewed: true,
                attendancePoints,
                statistics: {
                    push: {
                        type: StatisticRecordType.ATTENDED,
                        date: currentDate,
                        score: attendancePoints,
                    },
                },
            },
        });

        this.eventBus.publish(
            new StudentJoinedStreamEvent(
                {
                    id: aStudentStatusOnStream.params.id,
                    streamId: aStream.anId,
                    userUuid: UserUuid.wrap(aStudentStatusOnStream.uuid),
                    isViewed: true,
                    attendancePoints,
                },
                email, // для логов
            ),
        );
    }
}
