import { Inject } from '@nestjs/common';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';
import { SchoolModel } from '../../domain/models/school.model';
import { ISchoolRepository } from '../../domain/repositories/school-repository.interface';

export class SchoolRepository implements ISchoolRepository {
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
    ) {}

    public async findOne(schoolUuid: string): Promise<SchoolModel> {
        const data = await this.prisma.school.findUnique({ where: { uuid: schoolUuid } });
        if (!data) {
            return null;
        }
        return SchoolModel.from(data);
    }

    public async create(model: SchoolModel): Promise<void> {
        if (!model.kinescopeFolderId) {
            return;
        }
        await this.prisma.school.create({ data: model.params });
    }
}
