import { Inject } from '@nestjs/common';
import { ParticipantRole } from '@prisma/client';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';
import { StudentWebinarHistory } from '../../domain/models/student/student-webinar-history.aggregate';
import { StreamId } from '../../domain/objects/stream-id';
import { UserUuid } from '../../domain/objects/user-uuid';
import { WebinarId } from '../../domain/objects/webinar-id';
import { IStudentWebinarsHistoryRepository } from '../../domain/repositories/student-webinars-history-repository.interface';
import { IWebinarRepository } from '../../domain/repositories/webinar-repository.interface';
import { WEBINAR_REPOSITORY } from '../../injects';

export class StudentWebinarsHistoryRepository implements IStudentWebinarsHistoryRepository {
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
        @Inject(WEBINAR_REPOSITORY)
        private webinarRepository: IWebinarRepository,
    ) {}

    public async getStudentWebinarHistory(aWebinarId: WebinarId, studentEmail: string): Promise<StudentWebinarHistory> {
        const aWebinar = await this.webinarRepository.getWebinarParams(aWebinarId);

        const webinarStreamIds = await this.prisma.stream.findMany({
            where: { webinarId: aWebinarId.unwrap() },
            select: { id: true },
        });

        const webinarStudentHistory = await this.prisma.participant.findMany({
            where: {
                streamId: { in: webinarStreamIds.map((stream) => stream.id) },
                email: studentEmail,
                role: ParticipantRole.STUDENT, // TODO: не обязательно
            },
            select: {
                id: true,
                userUuid: true,
                streamId: true,
                isViewed: true,
                attendancePoints: true,
            },
        });
        const mappedHistory = webinarStudentHistory.map((history) => ({
            id: history.id,
            userUuid: UserUuid.wrap(history.userUuid),
            streamId: StreamId.wrap(history.streamId),
            isViewed: history.isViewed,
            attendancePoints: history.attendancePoints,
        }));
        return StudentWebinarHistory.from(
            studentEmail,
            aWebinar,
            mappedHistory.map((record) => ({ ...record, changed: false })),
        );
    }
}
