import { Inject } from '@nestjs/common';
import { ParticipantRole } from '@prisma/client';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';
import { WebinarUser } from '../../domain/models/user/user-abstract';
import { WebinarModel, WebinarParams } from '../../domain/models/webinar/webinar.model';
import { SchoolUuid } from '../../domain/objects/school-uuid';
import { WebinarId } from '../../domain/objects/webinar-id';
import { IWebinarRepository } from '../../domain/repositories/webinar-repository.interface';

export const getWebinarMapperPipeline = (anUser?: WebinarUser) => {
    const participantFilters =
        anUser && !anUser.isGuest()
            ? [
                  {
                      $or: [
                          {
                              $eq: ['$$participant.role', ParticipantRole.SPEAKER],
                          },
                          { $eq: ['$$participant.userUuid', anUser.params.id] },
                          { $eq: ['$$participant.email', anUser.params.email] },
                      ],
                  },
              ]
            : [{ $eq: ['$$participant.role', ParticipantRole.SPEAKER] }];

    return [
        {
            $addFields: {
                id: { $toString: '$_id' },
            },
        },
        {
            $lookup: {
                from: 'streams',
                localField: '_id',
                foreignField: 'webinarId',
                as: 'streams',
                pipeline: [
                    {
                        $addFields: {
                            id: { $toString: '$_id' },
                            date: { $toLong: '$date' },
                            createdAt: { $toLong: '$createdAt' },
                            updatedAt: { $toLong: '$updatedAt' },
                            webinarId: { $toString: '$webinarId' },
                        },
                    },
                    {
                        $lookup: {
                            from: 'participants',
                            localField: '_id',
                            foreignField: 'streamId',
                            as: 'participants',
                        },
                    },
                    {
                        $addFields: {
                            countRegistered: {
                                $size: {
                                    $filter: {
                                        input: '$participants',
                                        as: 'participant',
                                        cond: {
                                            $eq: ['$$participant.role', ParticipantRole.STUDENT],
                                        },
                                    },
                                },
                            },
                            countViewed: {
                                $size: {
                                    $filter: {
                                        input: '$participants',
                                        as: 'participant',
                                        cond: {
                                            $and: [
                                                {
                                                    $eq: ['$$participant.role', ParticipantRole.STUDENT],
                                                },
                                                {
                                                    $eq: ['$$participant.isViewed', true],
                                                },
                                            ],
                                        },
                                    },
                                },
                            },
                        },
                    },
                    {
                        $addFields: {
                            participants: {
                                $filter: {
                                    input: '$participants',
                                    as: 'participant',
                                    cond: participantFilters,
                                },
                            },
                        },
                    },
                    { $sort: { date: -1 } },
                    { $project: { _id: 0 } },
                ],
            },
        },
        { $project: { _id: 0 } },
    ];
};

export class WebinarRepository implements IWebinarRepository {
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
    ) {}

    async create(webinar: WebinarModel): Promise<void> {
        const { id, params } = webinar;
        const { streams, ...webinarData } = params;

        await this.prisma.webinar.create({
            data: {
                id,
                ...webinarData,
                streams: {
                    create: streams.map((streamParams) => {
                        // eslint-disable-next-line @typescript-eslint/no-unused-vars
                        const { participants, webinarId, ...streamData } = streamParams;
                        return {
                            ...streamData,
                            participants: {
                                create: participants.map((person) => {
                                    const { userUuid, role } = person;
                                    return {
                                        userUuid,
                                        role,
                                    };
                                }),
                            },
                        };
                    }),
                },
            },
        });
    }

    async update(webinar: WebinarModel): Promise<void> {
        const { id, params } = webinar;
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { streams, coverImage, ...webinarData } = params;

        await this.prisma.webinar.update({
            where: { id },
            data: {
                ...webinarData,
                coverImage: {
                    set: coverImage,
                },
            },
        });
    }

    async getWebinar(webinarId: WebinarId, anUser?: WebinarUser): Promise<WebinarModel | null> {
        const result = await this.prisma.webinar.aggregateRaw({
            pipeline: [{ $match: { _id: { $oid: webinarId.unwrap() } } }, ...getWebinarMapperPipeline(anUser)],
        });
        if (!result[0]) return null;

        const typedResult = result[0] as unknown as WebinarParams & {
            id: string;
        };
        const { id, ...webinarParams } = typedResult;
        return WebinarModel.from(WebinarId.wrap(id), webinarParams);
    }

    async getWebinarParams(aWebinarId: WebinarId): Promise<WebinarModel | null> {
        const data = await this.prisma.webinar.findUnique({
            where: {
                id: aWebinarId.unwrap(),
            },
        });
        if (!data) {
            return null;
        }
        const { id, ...webinarData } = data;

        return data ? WebinarModel.from(WebinarId.wrap(id), webinarData) : null;
    }

    async delete(webinarId: WebinarId): Promise<void> {
        await this.prisma.webinar.delete({
            where: {
                id: webinarId.unwrap(),
            },
        });
    }

    async deleteAllFromSchool(schoolUuid: SchoolUuid): Promise<void> {
        await this.prisma.webinar.deleteMany({
            where: { schoolUuid: schoolUuid.unwrap() },
        });
    }
}
