import { Inject } from '@nestjs/common';
import { ParticipantRole } from '@prisma/client';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';
import { StudentStatusOnStream } from '../../domain/models/student/student-status-on-stream';
import { StreamId } from '../../domain/objects/stream-id';
import { UserUuid } from '../../domain/objects/user-uuid';
import { IParticipantRepository } from '../../domain/repositories/participant-repository.interface';

export interface StudentsCount {
    countRegistered: number;
    countViewed: number;
}

export class ParticipantRepository implements IParticipantRepository {
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
    ) {}

    public async updateSpeakers(streamId: StreamId, speakerIds: UserUuid[]): Promise<void> {
        const speakerUuids: string[] = speakerIds.map((uuid) => uuid.unwrap());
        const existingSpeakers: UserUuid[] = await this.getExistingSpeakerUuids(streamId);
        const existingSpeakerUuids: string[] = existingSpeakers.map((speakerUuid) => speakerUuid.unwrap());

        const speakerUuidsToRemove: UserUuid[] = existingSpeakerUuids
            .filter((speakerUuid) => !speakerUuids.includes(speakerUuid))
            .map((uuid) => UserUuid.wrap(uuid));

        if (speakerUuidsToRemove.length > 0) {
            await this.deleteParticipants({ userUuids: speakerUuidsToRemove });
        }

        const speakerUuidsToAdd: UserUuid[] = speakerUuids
            .filter((speakerUuid) => !existingSpeakerUuids.includes(speakerUuid))
            .map((uuid) => UserUuid.wrap(uuid));

        if (speakerUuidsToAdd.length > 0) {
            await this.addSpeakers(streamId, speakerUuidsToAdd);
        }
    }

    public async getExistingSpeakerUuids(streamId: StreamId): Promise<UserUuid[]> {
        const speakers = await this.prisma.participant.findMany({
            where: {
                streamId: streamId.unwrap(),
                role: ParticipantRole.SPEAKER,
            },
            select: {
                userUuid: true,
            },
        });
        return speakers.map((speaker) => UserUuid.wrap(speaker.userUuid));
    }

    public async getStudentInfoOrNull(
        streamId: string,
        email: string,
    ): Promise<{ userUuid: string; phoneNumber: string } | null> {
        const result = await this.prisma.participant.aggregateRaw({
            pipeline: [
                {
                    $match: {
                        email,
                        streamId,
                        role: ParticipantRole.STUDENT,
                        $and: [{ userUuid: { $exists: true } }, { userUuid: { $ne: null } }, { userUuid: { $ne: '' } }],
                    },
                },
                {
                    $limit: 1,
                },
                {
                    $project: {
                        _id: 0,
                        userUuid: 1,
                        phoneNumber: 1,
                    },
                },
            ],
        });

        const students = result as unknown as { userUuid: string; phoneNumber: string }[];
        if (!students.length || !students[0].userUuid) {
            return null;
        }

        return students[0];
    }

    private async addSpeakers(streamId: StreamId, speakerIds: UserUuid[]): Promise<void> {
        const speakerUuids = speakerIds.map((uuid) => uuid.unwrap());
        await this.prisma.participant.createMany({
            data: speakerUuids.map((speakerUuid) => ({
                streamId: streamId.unwrap(),
                userUuid: speakerUuid,
                role: ParticipantRole.SPEAKER,
            })),
        });
    }

    public async deleteParticipants(params: { streamId?: StreamId; userUuids?: UserUuid[] }): Promise<void> {
        if (!params.streamId && !params.userUuids) {
            throw new Error('Either streamId or userUuids must be provided');
        }

        const where: { streamId?: string; userUuid?: { in: string[] } } = {};

        if (params.streamId) {
            where.streamId = params.streamId.unwrap();
        }
        if (params.userUuids) {
            where.userUuid = {
                in: params.userUuids.map((uuid) => uuid.unwrap()),
            };
        }

        await this.prisma.participant.deleteMany({ where });
    }

    public async getStudentCountsForStream(aStreamId: StreamId): Promise<StudentsCount> {
        const [countRegistered, countViewed] = await Promise.all([
            this.prisma.participant.count({
                where: {
                    streamId: aStreamId.unwrap(),
                    role: ParticipantRole.STUDENT,
                },
            }),

            this.prisma.participant.count({
                where: {
                    streamId: aStreamId.unwrap(),
                    isViewed: true,
                },
            }),
        ]);

        return { countRegistered, countViewed };
    }

    public async getStudentStatusOnStreamOrNull(
        aStreamId: StreamId,
        studentEmail: string,
    ): Promise<StudentStatusOnStream> {
        const streamId = aStreamId.unwrap();
        const studentStreamStatistic = await this.prisma.participant.findFirst({
            where: {
                streamId,
                email: studentEmail,
                role: ParticipantRole.STUDENT,
            },
        });

        if (!studentStreamStatistic) {
            return null;
        }

        return StudentStatusOnStream.from(
            {
                id: studentStreamStatistic.id,
                streamId,
                email: studentEmail,
                statistics: studentStreamStatistic?.statistics || [], // Потому что statistics было добавлено без миграции
            },
            {
                uuid: studentStreamStatistic?.userUuid,
                name: studentStreamStatistic?.name,
                phoneNumber: studentStreamStatistic?.phoneNumber,
                timezone: studentStreamStatistic?.timezone,
            },
        );
    }
}
