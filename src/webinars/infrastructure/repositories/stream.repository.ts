import { Inject } from '@nestjs/common';
import { ParticipantRole, StorageStateEnum } from '@prisma/client';
import { CustomError, ERROR_CODE } from '@skillspace/utils';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';
import { StreamModel, StreamParams } from '../../domain/models/stream/stream.model';
import { RoomParams, RoomStateEnum } from '../../domain/models/stream/stream-room';
import { StreamId } from '../../domain/objects/stream-id';
import { IStreamRepository } from '../../domain/repositories/stream-repository.interface';

export class StreamRepository implements IStreamRepository {
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
    ) {}

    public async create(stream: StreamModel): Promise<StreamId> {
        const { anId: id, params } = stream;
        const { participants, ...rest } = params;

        const result = await this.prisma.stream.create({
            data: {
                id: id.unwrap(),
                ...rest,
                participants: {
                    create: participants,
                },
            },
        });
        return StreamId.wrap(result.id); // TODO: не обязательно
    }

    public async update(stream: StreamModel): Promise<void> {
        const { anId: id, params } = stream;
        const { participants, room, ...rest } = params;

        await this.prisma.stream.update({
            where: { id: id.unwrap() },
            data: {
                ...rest,
                room: room
                    ? {
                          set: room,
                      }
                    : null,
                participants: {
                    deleteMany: {
                        streamId: id.unwrap(),
                        role: ParticipantRole.SPEAKER,
                    },
                    create: participants,
                },
            },
        });
    }

    public async getStream(id: StreamId, studentEmail?: string): Promise<StreamModel> {
        const streamId = id.unwrap();

        const participantFilter = studentEmail
            ? {
                  $or: [{ role: ParticipantRole.SPEAKER }, { email: studentEmail }],
              }
            : { role: ParticipantRole.SPEAKER };

        const streamData = await this.prisma.stream.aggregateRaw({
            pipeline: [
                { $match: { _id: { $oid: streamId } } },
                {
                    $addFields: {
                        id: { $toString: '$_id' },
                        date: { $toLong: '$date' },
                        webinarId: { $toString: '$webinarId' },
                        createdAt: { $toLong: '$createdAt' },
                        updatedAt: { $toLong: '$updatedAt' },
                    },
                },
                {
                    $lookup: {
                        from: 'participants',
                        localField: '_id',
                        foreignField: 'streamId',
                        as: 'participants',
                        pipeline: [{ $match: participantFilter }, { $project: { _id: 0, streamId: 0, id: 0 } }],
                    },
                },
                { $project: { _id: 0 } },
            ],
        });

        const typedParams = streamData[0] as unknown as Omit<StreamParams, 'date'> & { date: number };
        if (!typedParams) {
            return null;
        }
        return streamData
            ? StreamModel.from(id, {
                  ...typedParams,
                  date: new Date(typedParams.date),
                  updatedAt: new Date(typedParams.updatedAt),
              })
            : null;
    }

    public async delete(id: StreamId): Promise<void> {
        await this.prisma.stream.delete({
            where: { id: id.unwrap() },
        });
    }

    public async deleteByWebinarId(webinarId: string): Promise<void> {
        await this.prisma.stream.deleteMany({
            where: { webinarId },
        });
    }

    public async findStreamByRoomId(roomId: string, details?: object): Promise<StreamModel> {
        const result = await this.prisma.stream.aggregateRaw({
            pipeline: [
                { $match: { 'room.roomId': roomId } },
                { $limit: 1 },
                {
                    $addFields: {
                        id: { $toString: '$_id' },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        id: 1,
                    },
                },
            ],
        });
        const stream = result[0] as { id: string };
        if (!stream) {
            throw new CustomError(`Не найден поток вебинара с комнатой ${roomId}`, {
                code: ERROR_CODE.NOT_FOUND_ERROR,
                details: details ? details : { roomId },
            });
        }
        return this.getStream(StreamId.wrap(stream.id));
    }

    public async findStreamIdsToCloseRoom(currentDate: Date): Promise<string[]> {
        const result = await this.prisma.stream.aggregateRaw({
            options: {
                let: { currentDate: currentDate.getTime() },
            },
            pipeline: [
                {
                    $match: {
                        $expr: {
                            $and: [
                                {
                                    $in: [
                                        '$room.state',
                                        [RoomStateEnum.ready, RoomStateEnum.finished, RoomStateEnum.started],
                                    ],
                                },
                                { $lt: [{ $add: [{ $toLong: '$date' }, '$duration'] }, '$$currentDate'] },
                            ],
                        },
                    },
                },
                {
                    $addFields: {
                        id: { $toString: '$_id' },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        id: 1,
                    },
                },
            ],
        });

        const streams = result as unknown as { id: string }[];
        return streams.map((stream) => stream.id);
    }

    public async findStreamIdsToUpdateRecordUrl(): Promise<string[]> {
        const result = await this.prisma.stream.aggregateRaw({
            pipeline: [
                {
                    $match: {
                        'room.state': RoomStateEnum.closed,
                        storageState: StorageStateEnum.uploading,
                    },
                },
                {
                    $addFields: {
                        id: { $toString: '$_id' },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        id: 1,
                        kinescopeFolderId: 1,
                        savedRecordUrl: 1,
                        room: 1,
                    },
                },
            ],
        });

        const streams = result as unknown as {
            id: string;
            kinescopeFolderId: string;
            savedRecordUrl: string;
            room: RoomParams;
        }[];
        return streams.map(({ id }) => id);
    }

    public async findStreamIdsToRemoveDraftVideos(): Promise<string[]> {
        const result = await this.prisma.stream.aggregateRaw({
            pipeline: [
                {
                    $match: {
                        'room.state': RoomStateEnum.closed,
                        storageState: StorageStateEnum.processing,
                    },
                },
                {
                    $addFields: {
                        id: { $toString: '$_id' },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        id: 1,
                        kinescopeFolderId: 1,
                        savedRecordUrl: 1,
                        room: 1,
                    },
                },
            ],
        });

        const streams = result as unknown as {
            id: string;
            kinescopeFolderId: string;
            savedRecordUrl: string;
            room: RoomParams;
        }[];
        return streams.map(({ id }) => id);
    }
}
