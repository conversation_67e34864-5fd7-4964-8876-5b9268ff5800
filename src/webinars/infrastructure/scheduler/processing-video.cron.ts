import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { TransferService } from '../../application/services/transfer.service';

@Injectable()
export class WebinarsCron {
    constructor(private readonly transferService: TransferService) {}

    @Cron(CronExpression.EVERY_MINUTE)
    async closeRoomsWithFinishedSession(currentDate: Date = new Date()): Promise<void> {
        return this.transferService.closeRoomsWithFinishedSession(currentDate);
    }

    @Cron(CronExpression.EVERY_10_MINUTES)
    async updateSavedRecordsUrl(): Promise<void> {
        return this.transferService.updateSavedRecordsUrl();
    }

    @Cron(CronExpression.EVERY_10_MINUTES)
    async removeDraftVideoRecordsFromKinescope(): Promise<void> {
        return this.transferService.removeDraftVideoRecordsFromKinescope();
    }
}
