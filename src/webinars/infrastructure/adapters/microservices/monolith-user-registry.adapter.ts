import { Injectable, Logger } from '@nestjs/common';
import {
    AmqpManager,
    MonolithWebinarRegistrationNotifyContract,
    RequestMonolithWebinarRegistrationNotifyContract,
} from '@skillspace/amqp-contracts';

import { IUserRegistryAdapter } from '../../../application/adapters/microservices/user-registry-adapter.interface';

@Injectable()
export class MonolithUserRegistryAdapter implements IUserRegistryAdapter {
    private readonly logger = new Logger(MonolithUserRegistryAdapter.name);

    constructor(private readonly amqpConnection: AmqpManager) {}

    public async notifyMonolithAboutNewStudents(
        payload: RequestMonolithWebinarRegistrationNotifyContract['payload'],
    ): Promise<void> {
        await this.amqpConnection.publish(MonolithWebinarRegistrationNotifyContract, payload);
        this.logger.log(payload, 'Отправлено уведомление в монолит о регистрации пользователей');
    }
}
