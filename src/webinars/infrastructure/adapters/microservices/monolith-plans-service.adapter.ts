import { Injectable, Logger } from '@nestjs/common';
import { AmqpManager, MonolithChangeWebinarUsageContract } from '@skillspace/amqp-contracts';
import { GrpcClient, GrpcInjectClient } from '@skillspace/grpc';

import {
    FeatureAccess,
    IPlansServiceAdapter,
} from '../../../application/adapters/microservices/plans-service-adapter.interface';

@Injectable()
export class MonolithPlansServiceAdapter implements IPlansServiceAdapter {
    private readonly logger = new Logger(MonolithPlansServiceAdapter.name);

    constructor(
        @GrpcInjectClient('MonolithService')
        private readonly monolithGrpc: GrpcClient<'MonolithService'>,
        private readonly amqpConnection: AmqpManager,
    ) {}

    public async checkWebinarFeatureAccess(schoolUuid: string): Promise<FeatureAccess> {
        return this.monolithGrpc.send('IsEnableWebinarFeature', 'v1', {
            uuid: schoolUuid,
        });
    }

    public async checkInternalWebinarUsageLimit(schoolUuid: string): Promise<FeatureAccess> {
        return this.monolithGrpc.send('CheckLimitForInternalWebinarFeature', 'v1', {
            uuid: schoolUuid,
        });
    }

    public async changeInternalWebinarUsage(payload: { schoolUuid: string; delta: number }): Promise<void> {
        this.logger.log(payload, 'Уведомляем об изменении квоты использования на ' + payload.delta);
        return this.amqpConnection.publish(MonolithChangeWebinarUsageContract, payload);
    }
}
