import { Injectable, Logger } from '@nestjs/common';
import {
    AmqpManager,
    EmailServiceSendEmailToManyContract,
    EmailServiceSendEmailToManyContractNamespace,
    RequestEmailServiceSendEmailToManyContract,
} from '@skillspace/amqp-contracts';
import { CustomError, ERROR_CODE } from '@skillspace/utils';
import { plainToClassFromExist } from 'class-transformer';
import { validate } from 'class-validator';

import { getValidationErrorMessages } from '../../../../shared/utils/validation.utils';
import { INotificationServiceAdapter } from '../../../application/adapters/microservices/notification-service-adapter.interface';
import { StatisticsServiceAdapter } from './statistics-service.adapter';

@Injectable()
export class NotificationServiceAdapter implements INotificationServiceAdapter {
    private readonly logger = new Logger(StatisticsServiceAdapter.name);
    constructor(private readonly amqpConnection: AmqpManager) {}

    async notifyStudentByEmail(payload: RequestEmailServiceSendEmailToManyContract['payload']): Promise<void> {
        const messageToValidate = plainToClassFromExist(EmailServiceSendEmailToManyContractNamespace.Message, payload);
        const errors = await validate(messageToValidate);
        if (errors.length > 0) {
            throw new CustomError('Уведомление по электронной почте новых студентов не прошло валидацию', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: {
                    errors: getValidationErrorMessages(errors),
                    payload: messageToValidate,
                },
            });
        }

        await this.amqpConnection.publish(EmailServiceSendEmailToManyContract, payload);
        this.logger.log(payload, 'Уведомление по электронной почте новых студентов');
    }
}
