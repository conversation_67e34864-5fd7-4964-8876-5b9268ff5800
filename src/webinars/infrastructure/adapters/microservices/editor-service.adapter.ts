import { setTimeout } from 'node:timers/promises';
import { Injectable, Logger } from '@nestjs/common';

// import { GrpcClient, GrpcInjectClient } from '@skillspace/grpc';
import {
    IEditorServiceAdapter,
    Page,
    PageOwner,
    RemoveManyPagesResult,
    RemovePageResult,
} from '../../../application/adapters/microservices/editor-service-adapter.interface';

@Injectable()
export class EditorServiceAdapter implements IEditorServiceAdapter {
    private readonly logger = new Logger(EditorServiceAdapter.name);

    // constructor(
    // @GrpcInjectClient('MonolithService')
    // private readonly editorGrpc: GrpcClient<'EditorService'>,
    // ) {}

    public async createPage(owner: PageOwner): Promise<Page> {
        await setTimeout();
        return { pageId: '1', ...owner };
        // return this.editorGrpc.send('CreatePage', 'v1', { entityId: streamId, serviceId: 'webinars' });
    }

    public async removePage(pageId: string): Promise<RemovePageResult> {
        await setTimeout();
        void pageId;
        return { success: true };
        // return this.editorGrpc.send('RemovePage', 'v1', { pageId });
    }

    public async createManyPages(owners: PageOwner[]): Promise<Page[]> {
        await setTimeout();
        return owners.map((owner) => ({ pageId: '1', ...owner }));
    }

    public async removeManyPages(pageIds: string[]): Promise<RemoveManyPagesResult> {
        await setTimeout();
        return {
            success: true,
            removedPageIds: pageIds,
            errors: [],
        };
    }
}
