import { Injectable, Logger } from '@nestjs/common';
import { GrpcClient, GrpcInjectClient } from '@skillspace/grpc';

import { ISchoolRegistryAdapter } from '../../../application/adapters/microservices/school-registry-adapter.interface';

@Injectable()
export class MonolithSchoolRegistryAdapter implements ISchoolRegistryAdapter {
    private readonly logger = new Logger(MonolithSchoolRegistryAdapter.name);

    constructor(
        @GrpcInjectClient('MonolithService')
        private readonly monolithGrpc: GrpcClient<'MonolithService'>,
    ) {}

    public async getKinescopeBySchoolId(schoolUuid: string): Promise<string> {
        const identity = await this.monolithGrpc.send('GetKinescopeBySchool', 'v1', {
            uuid: schoolUuid,
        });
        return identity.uuid;
    }
}
