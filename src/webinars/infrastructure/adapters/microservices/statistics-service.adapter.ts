import { Injectable, Logger } from '@nestjs/common';
import {
    AmqpManager,
    RequestStatisticsStreamCreatedContract,
    RequestStatisticsStreamsRemovedContract,
    RequestStatisticsStudentsRegisteredContract,
    RequestStatisticsStudentStreamUpdatedContract,
    RequestStatisticsWebinarsRemovedContract,
    StatisticsStreamCreatedContract,
    StatisticsStreamCreatedContractNamespace,
    StatisticsStreamsRemovedContract,
    StatisticsStreamsRemovedContractNamespace,
    StatisticsStudentsRegisteredContract,
    StatisticsStudentsRegisteredContractNamespace,
    StatisticsStudentStreamUpdatedContract,
    StatisticsStudentStreamUpdatedContractNamespace,
    StatisticsWebinarsRemovedContract,
    StatisticsWebinarsRemovedContractNamespace,
} from '@skillspace/amqp-contracts';
import { plainToClassFromExist } from 'class-transformer';
import { validate } from 'class-validator';

import { getValidationErrorMessages } from '../../../../shared/utils/validation.utils';
import { CustomError, ERROR_CODE } from '../../../../shared/webinar-service.error';
import { IStatisticsServiceAdapter } from '../../../application/adapters/microservices/statistics-service-adapter.interface';

@Injectable()
export class StatisticsServiceAdapter implements IStatisticsServiceAdapter {
    private readonly logger = new Logger(StatisticsServiceAdapter.name);

    constructor(private readonly amqpConnection: AmqpManager) {}

    async notifyAboutCreatedStream(payload: RequestStatisticsStreamCreatedContract['payload']): Promise<void> {
        const messageToValidate = plainToClassFromExist(StatisticsStreamCreatedContractNamespace.Message, payload);
        const errors = await validate(messageToValidate);
        if (errors.length > 0) {
            throw new CustomError('Сообщение в статистику о создании потоков не прошло валидацию', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: {
                    errors: getValidationErrorMessages(errors),
                    payload: messageToValidate,
                },
            });
        }
        await this.amqpConnection.publish(StatisticsStreamCreatedContract, payload);
        this.logger.log(payload, 'Уведомление статистики о создании потока');
    }

    async notifyAboutRegisteredStudents(
        payload: RequestStatisticsStudentsRegisteredContract['payload'],
    ): Promise<void> {
        const messageToValidate = plainToClassFromExist(StatisticsStudentsRegisteredContractNamespace.Message, payload);
        const errors = await validate(messageToValidate);
        if (errors.length > 0) {
            throw new CustomError('Сообщение в статистику о регистрации студентов не прошло валидацию', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: {
                    errors: getValidationErrorMessages(errors),
                    payload: messageToValidate,
                },
            });
        }

        await this.amqpConnection.publish(StatisticsStudentsRegisteredContract, payload);
        this.logger.log(payload, 'Уведомление статистики о регистрации студентов');
    }

    async notifyAboutRemovedStreams(payload: RequestStatisticsStreamsRemovedContract['payload']): Promise<void> {
        const messageToValidate = plainToClassFromExist(StatisticsStreamsRemovedContractNamespace.Message, payload);
        const errors = await validate(messageToValidate);
        if (errors.length > 0) {
            throw new CustomError('Сообщение в статистику об удалении потоков не прошло валидацию', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: {
                    errors: getValidationErrorMessages(errors),
                    payload: messageToValidate,
                },
            });
        }

        await this.amqpConnection.publish(StatisticsStreamsRemovedContract, payload);
        this.logger.log(payload, 'Уведомление статистики об удалении потоков');
    }

    async notifyAboutRemovedWebinars(payload: RequestStatisticsWebinarsRemovedContract['payload']): Promise<void> {
        const messageToValidate = plainToClassFromExist(StatisticsWebinarsRemovedContractNamespace.Message, payload);
        const errors = await validate(messageToValidate);
        if (errors.length > 0) {
            throw new CustomError('Сообщение в статистику об удалении вебинара не прошло валидацию', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: {
                    errors: getValidationErrorMessages(errors),
                    payload: messageToValidate,
                },
            });
        }

        await this.amqpConnection.publish(StatisticsWebinarsRemovedContract, payload);
        this.logger.log(payload, 'Уведомление статистики об удалении вебинара');
    }

    async notifyAboutStudentActivity(payload: RequestStatisticsStudentStreamUpdatedContract['payload']): Promise<void> {
        const messageToValidate = plainToClassFromExist(
            StatisticsStudentStreamUpdatedContractNamespace.Message,
            payload,
        );
        const errors = await validate(messageToValidate);
        if (errors.length > 0) {
            throw new CustomError('Сообщение в статистику об активности студента не прошло валидацию', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: {
                    errors: getValidationErrorMessages(errors),
                    payload: messageToValidate,
                },
            });
        }

        await this.amqpConnection.publish(StatisticsStudentStreamUpdatedContract, payload);
        this.logger.log(payload, 'Уведомление статистики об активности студента');
    }
}
