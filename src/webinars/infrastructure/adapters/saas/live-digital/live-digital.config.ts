import { resolveStr } from '../../../../../shared/utils/environment.utils';

/**
 * Это предварительно созданные пространства в liveDigital для разных сред
 */
export const PROD_LIVE_DIGITAL_SPACE_ID = '67af175a1b3194213c466ded';
export const DEV_LIVE_DIGITAL_SPACE_ID = '67c8100b5b029103e180b993';
export const TEST_LIVE_DIGITAL_SPACE_ID = '67cff9fa66c8a0c2985d587c';

export const LIVE_DIGITAL_SPACE_IDS = [
    PROD_LIVE_DIGITAL_SPACE_ID,
    DEV_LIVE_DIGITAL_SPACE_ID,
    TEST_LIVE_DIGITAL_SPACE_ID,
];

export interface ILiveDigitalConfig {
    apiUrl: string;
    accessToken: string;
    spaceId: string;
    webhookUrl: string;
}

export const liveDigitalConfig: ILiveDigitalConfig = {
    apiUrl: resolveStr('LIVE_DIGITAL_API_URL', process.env.LIVE_DIGITAL_API_URL),
    accessToken: resolveStr('LIVE_DIGITAL_ACCESS_TOKEN', process.env.LIVE_DIGITAL_ACCESS_TOKEN),
    spaceId: resolveStr('LIVE_DIGITAL_SPACE_ID', process.env.LIVE_DIGITAL_SPACE_ID),
    webhookUrl: resolveStr('LIVE_DIGITAL_WEBHOOK_URL', process.env.LIVE_DIGITAL_WEBHOOK_URL),
};
