import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { SaasProviderEnum } from '@prisma/client';
import { CustomError, ERROR_CODE } from '@skillspace/utils';

import { isTest } from '../../../../../configs/app.config';
import { roomConfig } from '../../../../../configs/room.config';
import {
    IWebinarProviderAdapter,
    RoomActiveCall,
    RoomRecordUrl,
} from '../../../../application/adapters/saas/webinar-provider-adapter.interface';
import { RoomParams, RoomStateEnum } from '../../../../domain/models/stream/stream-room';
import { WebinarUser } from '../../../../domain/models/user/user-abstract';
import { LiveDigitalClient } from './client/live-digital.client';
import { liveDigitalConfig } from './live-digital.config';

@Injectable()
export class LiveDigitalAdapter implements IWebinarProviderAdapter, OnModuleInit {
    private config = liveDigitalConfig;
    private liveDigital = new LiveDigitalClient(this.config);
    private logger = new Logger(LiveDigitalAdapter.name);

    async onModuleInit(): Promise<void> {
        if (isTest) {
            this.logger.warn('🐇 Пропускаем настройку LiveDigital в тестовой среде');
            return;
        }
        const { spaceId, webhookUrl } = this.config;

        const currentWebhook = await this.liveDigital.getWebHook(spaceId);

        if (currentWebhook.url !== this.config.webhookUrl) {
            this.logger.warn({ currentUrl: this.config.webhookUrl }, 'Адрес вебхука LiveDigital изменился');
            try {
                await this.liveDigital.createWebHook(spaceId, webhookUrl);
            } catch (error) {
                if (error instanceof Error) {
                    this.logger.warn({ message: error.message }, 'Ошибка привязки адреса вебхука для LiveDigital');
                }
                return;
            }
        }

        this.logger.log('Адрес вебхука LiveDigital не изменился');
    }

    public async setupWebinarRoom(streamName: string, autoRecord = false): Promise<RoomParams> {
        if (roomConfig.provider === SaasProviderEnum.LiveDigital) {
            const room = await this.liveDigital.createRoom(roomConfig.spaceId, streamName, autoRecord);
            this.logger.log({ roomId: room.id, name: streamName }, `Создана комната вебинара в ${roomConfig.provider}`);
            return {
                state: RoomStateEnum.ready,
                provider: roomConfig.provider,
                spaceId: room.spaceId,
                roomId: room.id,
                storage: roomConfig.storageProvider,
                uploadedRecordIds: [],
            };
        }

        throw new Error('Нет реализации для данного провайдера');
    }

    public async getWebinarRoomUrl(params: {
        stream: RoomParams & { streamId: string };
        anUser: WebinarUser;
    }): Promise<string> {
        const { stream, anUser } = params;

        if (stream.provider === SaasProviderEnum.LiveDigital) {
            try {
                const place = {
                    spaceId: this.config.spaceId,
                    roomId: stream.roomId,
                };

                const userOptions = {
                    username: anUser.params.name,
                    email: anUser.params.email,
                    role: anUser.isEmployee() ? ('moderator' as const) : ('user' as const),
                    externalUserId: anUser.params.id,
                    externalMeetingId: stream.streamId,
                };

                const response = await this.liveDigital.generateAccessLink(place, userOptions);

                this.logger.log(
                    { ...place, ...userOptions },
                    `Получен токен ${stream.provider} для пользователя ${anUser.params.email}`,
                );

                return response.accessUrl;
            } catch (e) {
                if (e instanceof Error) {
                    throw new CustomError('Ошибка генерации токена LiveDigital', {
                        code: ERROR_CODE.INTEGRATION_ERROR,
                        cause: e.message,
                        details: { ...stream, message: e.message },
                    });
                }
            }
        }

        throw new Error('Нет реализации для данного провайдера');
    }

    public async deleteRoom(room: RoomParams): Promise<void> {
        try {
            await this.liveDigital.deleteRoom(room);
            return;
        } catch (e) {
            if (e instanceof Error) {
                throw new CustomError('Ошибка удаления комнаты вебинаров провайдера ' + e.message, {
                    code: ERROR_CODE.INTEGRATION_ERROR,
                    cause: e,
                    details: room,
                });
            }
        }
    }

    public async getRoomActiveCall(room: RoomParams): Promise<RoomActiveCall> {
        return this.liveDigital.getRoomActiveCall(room.spaceId, room.roomId);
    }

    public async deleteRooms(rooms: RoomParams[]): Promise<void> {
        await Promise.all(rooms.map((room) => this.deleteRoom(room)));
    }

    public async getRoomRecords(room: Omit<RoomParams, 'storage'>): Promise<RoomRecordUrl[] | null> {
        const records = await this.getProviderRecords(room);
        if (!records || records.length === 0) return [];

        if (room.provider === SaasProviderEnum.LiveDigital) {
            const enrichedRecords = await Promise.all(
                records.map(async (record) => {
                    const url = await this.liveDigital.downloadRecord(room, record.id);
                    return {
                        ...record,
                        url,
                    };
                }),
            );
            this.logger.log(
                { count: enrichedRecords.length, recordIds: enrichedRecords.map(({ id }) => id) },
                `Запрос записей комнаты вебинара ${room.provider}`,
            );
            return enrichedRecords;
        }

        throw new Error('Нет реализации для данного провайдера');
    }

    private async getProviderRecords(room: Omit<RoomParams, 'storage'>): Promise<Omit<RoomRecordUrl, 'url'>[]> {
        if (room.provider === SaasProviderEnum.LiveDigital) {
            try {
                const records = await this.liveDigital.getRecords(room);
                if (records.total === 0) {
                    return [];
                }
                return records.items.map((record) => ({
                    id: record.id,
                    provider: SaasProviderEnum.LiveDigital,
                    name: record.name,
                    fileSize: record.fileSize,
                    duration: record.duration,
                    state: record.state,
                    startedAt: new Date(record.startedAt),
                    finishedAt: new Date(record.finishedAt),
                }));
            } catch (e) {
                if (e instanceof Error) {
                    throw new CustomError('Ошибка получения записей комнаты вебинара на LiveDigital', {
                        code: ERROR_CODE.INTEGRATION_ERROR,
                        cause: e,
                        details: { ...room, message: e.message },
                    });
                }
            }
        }

        throw new Error('Нет реализации для данного провайдера');
    }
}
