export interface LSpace {
    id: string; // Уникальный идентификатор пространства
    appId: string; // Идентификатор приложения, связанного с пространством
    name: string; // Название пространства
    isPublic: boolean; // Флаг, указывающий, является ли пространство публичным
    description?: string; // Описание пространства (может быть необязательным)
    chatToken: string; // Токен для доступа к чату
    subscriptionEndsAt: string; // Дата окончания подписки в формате ISO 8601
    planId: string; // Идентификатор тарифного плана
    planAlias: string; // Алиас тарифного плана
    roleInSpace: string; // Роль пользователя в данном пространстве
    accountId: string; // Идентификатор аккаунта владельца пространства
    isSubscriptionActive: boolean; // Флаг, указывающий, активна ли подписка
}

export interface CreateRoomArgs {
    name: string; // Название комнаты
    templateId?: string; // ID шаблона для комнаты
    isPublic: boolean; // Является ли комната публичной
    isChatAllowed: boolean; // Разрешена ли функция чата
    isRecordAllowed: boolean; // Разрешена ли запись
    isAutoRecordingAllowed: boolean; // Разрешена ли автоматическая запись
    isMicrophonePublishingAllowed: boolean; // Разрешена ли публикация аудио с микрофона
    isScreenMediaPublishingAllowed: boolean; // Разрешена ли публикация экрана
    isCameraPublishingAllowed: boolean; // Разрешена ли публикация видео с камеры
    isAvatarsAllowed: boolean; // Разрешены ли аватары
    isSelfRenamingAllowed: boolean; // Могут ли участники переименовывать себя
    isHandRaisingAllowed: boolean; // Разрешена ли функция поднятия руки
    isScreensharingAllowed: boolean; // Разрешена ли функция совместного просмотра экрана
    isRemoteDrawingAllowed: boolean; // Разрешена ли функция удаленной рисовальной доски
    type: 'lesson' | 'meeting' | 'webinar'; // Тип комнаты (например, урок, совещание, вебинар)
    redirectUrl?: string; // URL для перенаправления после завершения (опционально)
}

export interface LRoom {
    id: string; // Уникальный идентификатор комнаты
    appId: string; // Идентификатор приложения, связанного с комнатой
    channelId: string; // Идентификатор канала
    alias: string; // Алиас комнаты (уникальное имя)
    name: string; // Название комнаты
    isPublic: boolean; // Флаг, указывающий, является ли комната публичной
    spaceId: string; // Идентификатор пространства, к которому принадлежит комната
    isChatAllowed: boolean; // Разрешен ли чат в комнате
    isFavorite: boolean; // Является ли комната избранной
    isRecordAllowed: boolean; // Разрешена ли запись в комнате
    isScreensharingAllowed: boolean; // Разрешен ли экранной-sharing
    isAutoRecordingAllowed: boolean; // Разрешена ли автоматическая запись
    isMicrophonePublishingAllowed: boolean; // Разрешена ли публикация микрофона
    isScreenMediaPublishingAllowed: boolean; // Разрешена ли публикация экрана
    isCustomMediaPublishingAllowed: boolean; // Разрешена ли публикация пользовательских медиа
    isCameraPublishingAllowed: boolean; // Разрешена ли публикация камеры
    isAvatarsAllowed: boolean; // Разрешены ли аватары
    isSelfRenamingAllowed: boolean; // Разрешено ли переименование пользователей
    isHandRaisingAllowed: boolean; // Разрешен ли подъем рук
    isRemoteDrawingAllowed: boolean; // Разрешено ли рисование
    lastCallAt: string; // Время последнего вызова в формате ISO 8601
    createdAt: string; // Время создания комнаты в формате ISO 8601
    redirectUrl?: string; // URL для перенаправления после завершения встречи (опционально)
}

export interface RoomCall {
    id: string;
    active: boolean;
    roomId: string;
    participantsCount: number; // кол-во активных участников в звонке
    isBreakoutRoom: boolean;
    planAlias: string;
    roomType: string;
    totalParticipantsCount: number; //общее кол-во участников, который были в звонке за всё его время
    uniqueParticipantsCount: number;
    startedAt: string;
    finishedAt: string;
}

export interface UserTokenArgs {
    username: string; // Имя пользователя
    role: 'user' | 'moderator'; // Роль пользователя
    externalUserId?: string; // Внешний ID пользователя
    externalMeetingId: string; // Внешний ID встречи
    ttl?: number; // Время жизни токена в секундах
    isPermanent: boolean; // Является ли токен постоянным
    email: string; // Email пользователя (опционально)
    phone?: string; // Телефон пользователя (опционально)
}

export interface WebRecord {
    id: string;
    name: string;
    fileSize: number;
    duration: number;
    roomId: string;
    spaceId: string;
    state: string;
    createdAt: string;
    startedAt: string;
    finishedAt: string;
}

export interface LWebhookResponse {
    url: string;
    isActive: boolean;
}

export type WebhookCreated = LWebhookResponse & {
    secret: string;
};

export type WebhookUpdateParams = Partial<LWebhookResponse>;
