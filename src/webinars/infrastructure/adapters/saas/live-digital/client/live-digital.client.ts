import { setTimeout } from 'node:timers/promises';
import { Injectable, Logger } from '@nestjs/common';

import { EXTERNAL_API_RETRY_COUNT, EXTERNAL_API_RETRY_DELAY } from '../../../../../../configs/app.config';
import { LIVE_DIGITAL_SPACE_IDS, liveDigitalConfig } from '../live-digital.config';
import {
    CreateRoomArgs,
    LRoom,
    LSpace,
    LWebhookResponse,
    RoomCall,
    UserTokenArgs,
    WebhookCreated,
    WebhookUpdateParams,
    WebRecord,
} from './live-digital.type';

export const MAX_TTL = 432000;

export const DEFAULT_CREATE_ROOM_ARGS: Partial<CreateRoomArgs> = {
    isPublic: false,
    isChatAllowed: true,
    isRecordAllowed: true,
    isMicrophonePublishingAllowed: true,
    isScreenMediaPublishingAllowed: true,
    isCameraPublishingAllowed: true,
    isAvatarsAllowed: true,
    isSelfRenamingAllowed: true, // разрешено себя переименовывать
    isHandRaisingAllowed: true,
    isScreensharingAllowed: true,
    isRemoteDrawingAllowed: true,
    type: 'webinar',
};

export const DEFAULT_TOKEN_ARGS: Pick<UserTokenArgs, 'ttl' | 'isPermanent'> = {
    ttl: MAX_TTL,
    isPermanent: true,
};

@Injectable()
export class LiveDigitalClient {
    private logger = new Logger(LiveDigitalClient.name);

    constructor(private readonly config = liveDigitalConfig) {}

    /**
     * SPACES
     */

    async createSpace(name: string, description: string): Promise<{ id: string }> {
        const url = this.config.apiUrl + '/v1/spaces/';
        const body = {
            isPublic: false,
            name,
            description,
        };

        const response = await this.makeApiRequest(url, 'POST', body);

        const data = (await response.json()) as { id: string };
        return { id: data.id };
    }

    async getSpaces(): Promise<{ items: LSpace[]; total: number }> {
        const url = this.config.apiUrl + '/v1/spaces/';

        const response = await this.makeApiRequest(url, 'GET');

        const data = (await response.json()) as { items: LSpace[]; total: number };
        this.logger.log(
            { spaceIds: data.items.map((item) => item.id) },
            'Запрос списка групп LiveDigital ' + data.items.map((item) => item.id).join(', '),
        );
        return { items: data.items, total: data.total };
    }

    async getSpaceById(spaceId: string): Promise<LSpace> {
        const url = this.config.apiUrl + `/v1/spaces/${spaceId}`;

        const response = await this.makeApiRequest(url, 'GET');

        const data = (await response.json()) as LSpace;
        this.logger.log({ spaceId: data.id }, 'Запрос группы LiveDigital');
        return data;
    }

    /**
     * Используется только в тестах
     */
    async deleteSpace(spaceId: string): Promise<void> {
        if (LIVE_DIGITAL_SPACE_IDS.includes(spaceId)) {
            return;
        }

        const url = this.config.apiUrl + `/v1/spaces/${spaceId}`;

        await this.makeApiRequest(url, 'DELETE');
    }

    /**
     * Удаление тестовых групп
     */
    async deleteUnusedTestSpaces(): Promise<void> {
        const USED_LIVE_DIGITAL_SPACES_COUNT = LIVE_DIGITAL_SPACE_IDS.length;
        const spaces = await this.getSpaces();
        if (spaces.items.length > USED_LIVE_DIGITAL_SPACES_COUNT) {
            console.log('Удаляем тестовые группы');
            const spaceIdsToRemove = spaces.items.map((space) => space.id);
            for (const id of spaceIdsToRemove) {
                await this.deleteSpace(id);
            }
            const spacesCountAfterCleaning = await this.getSpaces();
            if (spacesCountAfterCleaning.items.length === USED_LIVE_DIGITAL_SPACES_COUNT) {
                this.logger.log('Тестовые группы были успешно удалены');
            } else {
                this.logger.log(
                    'Ошибка удаления тестовых групп. Количество групп ' + spacesCountAfterCleaning.items.length,
                );
            }
        }
    }

    /**
     * ROOMS
     */

    async createRoom(spaceId: string, roomName: string, autoRecord = false): Promise<LRoom> {
        const url = this.config.apiUrl + `/v1/spaces/${spaceId}/rooms`;
        const body: Partial<CreateRoomArgs> = {
            ...DEFAULT_CREATE_ROOM_ARGS,
            name: roomName,
            isAutoRecordingAllowed: autoRecord,
        };

        const response = await this.makeApiRequest(url, 'POST', body);

        const data = (await response.json()) as LRoom;
        return data;
    }

    async getRoomActiveCall(spaceId: string, roomId: string): Promise<RoomCall> {
        const url = this.config.apiUrl + `/v1/spaces/${spaceId}/rooms/${roomId}/active-call`;

        const response = await this.makeApiRequest(url, 'GET');
        return (await response.json()) as RoomCall;
    }

    /**
     * WEBHOOKS
     */

    async createWebHook(spaceId: string, webhookUrl: string): Promise<WebhookCreated | LWebhookResponse> {
        const existingWebhook = await this.getWebHook(spaceId);

        if (existingWebhook) {
            return this.updateWebHook(spaceId, { url: webhookUrl, isActive: true });
        } else {
            const url = `${this.config.apiUrl}/v1/spaces/${spaceId}/webhook`;
            const body = { url: webhookUrl, isActive: true };

            const response = await this.makeApiRequest(url, 'POST', body);

            const data = (await response.json()) as WebhookCreated;
            this.logger.log({ webhookUrl: data.url }, 'Создали новый вебхук');
            return data;
        }
    }

    async getWebHook(spaceId: string): Promise<LWebhookResponse | null> {
        const url = `${this.config.apiUrl}/v1/spaces/${spaceId}/webhook`;

        const response = await this.makeApiRequest(url, 'GET');

        const data = (await response.json()) as LWebhookResponse | null;
        return data;
    }

    async updateWebHook(spaceId: string, webhookData: WebhookUpdateParams): Promise<LWebhookResponse> {
        const url = `${this.config.apiUrl}/v1/spaces/${spaceId}/webhook`;

        const response = await this.makeApiRequest(url, 'PUT', webhookData);

        const data = (await response.json()) as LWebhookResponse;
        this.logger.log({ webhookUrl: data.url }, 'Обновили существующий вебхук');
        return data;
    }

    async deleteWebHook(spaceId: string): Promise<void> {
        const url = `${this.config.apiUrl}/v1/spaces/${spaceId}/webhook`;

        await this.makeApiRequest(url, 'DELETE');

        this.logger.log({ spaceId }, 'Удалили вебхук');
    }

    async deleteRoom(args: { spaceId: string; roomId: string }): Promise<void> {
        const url = this.config.apiUrl + `/v1/spaces/${args.spaceId}/rooms/${args.roomId}`;
        await this.makeApiRequest(url, 'DELETE');
        return;
    }

    async getSpaceRooms(
        spaceId: string,
    ): Promise<{ items: Pick<LRoom, 'id' | 'name' | 'alias'>[]; total: number; webinars: number }> {
        const url = this.config.apiUrl + `/v1/spaces/${spaceId}/rooms`;

        const response = await this.makeApiRequest(url, 'GET');

        const data = (await response.json()) as { items: LRoom[]; total: number; webinars: number };
        return {
            items: data.items.map((item) => ({ id: item.id, name: item.name, alias: item.alias })),
            total: data.total,
            webinars: data.webinars,
        };
    }

    async getRoomById(args: { spaceId: string; roomId: string }): Promise<LSpace> {
        const url = this.config.apiUrl + `/v1/spaces/${args.spaceId}/rooms/${args.roomId}`;

        const response = await this.makeApiRequest(url, 'GET');

        const data = (await response.json()) as LSpace;
        return data;
    }

    /**
     * ACCESS
     */

    async generateAccessLink(
        place: { spaceId: string; roomId: string },
        options: {
            username: string;
            email: string;
            role: 'user' | 'moderator';
            externalUserId?: string;
            externalMeetingId: string;
        },
    ): Promise<{ accessUrl: string }> {
        const url = this.config.apiUrl + `/v1/spaces/${place.spaceId}/rooms/${place.roomId}/generate-access`;
        const username = options.username ? options.username : options.email;
        const body: UserTokenArgs = {
            ...DEFAULT_TOKEN_ARGS,
            ...options,
            username,
        };

        const response = await this.makeApiRequest(url, 'POST', body);

        const data = (await response.json()) as { url: string };
        return { accessUrl: data.url };
    }

    /**
     * CALLS
     */

    async finishCall(place: { spaceId: string; roomId: string }) {
        const url = this.config.apiUrl + `/v1/spaces/${place.spaceId}/rooms/${place.roomId}/finish-call`;
        await this.makeApiRequest(url, 'POST');

        this.logger.log(place, 'Вебинар остановлен');
    }

    /**
     * RECORDS
     */

    async startRecord(place: { spaceId: string; roomId: string }) {
        const url = this.config.apiUrl + `/v1/spaces/${place.spaceId}/rooms/${place.roomId}/records/start`;
        await this.makeApiRequest(url, 'POST');

        this.logger.log(place, 'Запись вебинара включена');
    }

    async stopRecord(place: { spaceId: string; roomId: string }) {
        const url = this.config.apiUrl + `/v1/spaces/${place.spaceId}/rooms/${place.roomId}/records/stop`;
        await this.makeApiRequest(url, 'POST');

        this.logger.log(place, 'Запись вебинара остановлена');
    }

    async getRecords(place: { spaceId: string; roomId: string }): Promise<{ items: WebRecord[]; total: number }> {
        const url = this.config.apiUrl + `/v1/spaces/${place.spaceId}/rooms/${place.roomId}/records`;
        const response = await this.makeApiRequest(url, 'GET');

        const data = (await response.json()) as { items: WebRecord[]; total: number };
        return data;
    }

    async downloadRecord(place: { spaceId: string; roomId: string }, recordId: string): Promise<string> {
        const url =
            this.config.apiUrl + `/v1/spaces/${place.spaceId}/rooms/${place.roomId}/records/download/${recordId}`;

        const response = await this.makeApiRequest(url, 'GET');

        return response.text();
    }

    private async makeApiRequest(
        url: string,
        method: 'POST' | 'PUT' | 'GET' | 'DELETE',
        body = null,
        retries = 0,
    ): Promise<Response> {
        const options: RequestInit = {
            method,
            headers: {
                Authorization: `Bearer ${this.config.accessToken}`,
                'Content-Type': 'application/json',
            },
        };

        if (body) {
            options.body = JSON.stringify(body);
        }

        try {
            const response = await fetch(url, options);

            if (!response.ok) {
                let errorBody;
                try {
                    errorBody = (await response.json()) as { message: string };
                } catch {
                    errorBody = { message: response.statusText };
                }
                throw errorBody;
            }

            return response;
        } catch (error) {
            if (
                retries < EXTERNAL_API_RETRY_COUNT &&
                (error as { cause?: { code?: string } }).cause?.code === 'EAI_AGAIN'
            ) {
                await setTimeout(EXTERNAL_API_RETRY_DELAY);
                return this.makeApiRequest(url, method, body, retries + 1);
            }

            return this.handleRequestError(error);
        }
    }

    private handleRequestError(error: unknown): never {
        const typedError = error as { cause?: { code?: string }; message?: string };

        if (typedError.cause?.code === 'EAI_AGAIN') {
            throw new Error('Сетевая ошибка: Не удалось разрешить имя хоста ' + liveDigitalConfig.apiUrl);
        }

        if (typedError.message === 'fetch failed') {
            throw new Error('Не удалось подключиться к серверу ' + liveDigitalConfig.apiUrl);
        }

        const errorMessage = typedError.message || 'Произошла неожиданная ошибка';
        throw new Error(`Ошибка запроса: ${errorMessage}`);
    }
}
