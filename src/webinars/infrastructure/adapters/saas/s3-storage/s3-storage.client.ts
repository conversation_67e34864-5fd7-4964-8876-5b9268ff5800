import { Readable } from 'node:stream';
import {
    DeleteObjectCommand,
    DeleteObjectsCommand,
    GetObjectCommand,
    ListObjectsV2Command,
    S3Client,
} from '@aws-sdk/client-s3';
import { Upload } from '@aws-sdk/lib-storage';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Injectable, Logger } from '@nestjs/common';

import { S3Config } from './s3.config';

export interface AwsUploadParams {
    Bucket: string;
    Key: string;
    Body: Buffer | Readable;
    ContentType: string;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [key: string]: any; // Для дополнительных параметров
}

@Injectable()
export class S3StorageClient {
    private logger = new Logger(S3StorageClient.name);
    private readonly storageClient: S3Client;

    constructor() {
        const { region, endpoint, accessKeyId, secretAccessKey } = S3Config;
        this.storageClient = new S3Client({
            region,
            endpoint,
            credentials: { accessKeyId, secretAccessKey },
            forcePathStyle: true,
        });
    }

    public async getSignedUrl(bucket: string, key: string, expiresIn?: number): Promise<string> {
        const command = new GetObjectCommand({
            Bucket: bucket,
            Key: key,
        });

        return getSignedUrl(this.storageClient, command, { expiresIn });
    }

    public async uploadFile(params: AwsUploadParams): Promise<void> {
        const upload = new Upload({
            client: this.storageClient,
            params,
        });

        await upload.done();
    }

    public async deleteFile(bucket: string, key: string): Promise<void> {
        const command = new DeleteObjectCommand({
            Bucket: bucket,
            Key: key,
        });

        await this.storageClient.send(command);
    }

    public async deleteFiles(bucket: string, listOfKeys: string[]): Promise<void> {
        const objects = listOfKeys.map((fileKey) => ({ Key: fileKey }));

        const command = new DeleteObjectsCommand({
            Bucket: bucket,
            Delete: { Objects: objects, Quiet: false },
        });

        await this.storageClient.send(command);
    }

    public async getFileList(bucket: string, prefix: string): Promise<string[]> {
        const command = new ListObjectsV2Command({
            Bucket: bucket,
            Prefix: prefix,
        });

        const response = await this.storageClient.send(command);
        return response.Contents ? response.Contents.map((object) => object.Key) : [];
    }
}
