import { parse } from 'node:path';
import { Readable } from 'node:stream';
import { Injectable, Logger } from '@nestjs/common';
import { CustomError, ERROR_CODE } from '@skillspace/utils';
import slugify from 'slugify';

import { appConfig } from '../../../../../configs/app.config';
import { IFileUpload } from '../../../../../shared/gql-file-upload-convert';
import { IFileStorageAdapter } from '../../../../application/adapters/saas/file-storage-adapter.interface';
import { S3Config } from './s3.config';
import { AwsUploadParams, S3StorageClient } from './s3-storage.client';

export type UploadParams = Omit<AwsUploadParams, 'Bucket'> & {
    ContentType: string;
    Key: string;
    Body: Buffer | Readable;
};

@Injectable()
export class S3FileStorageAdapter implements IFileStorageAdapter {
    private logger = new Logger(S3FileStorageAdapter.name);
    private readonly bucket: string;

    private readonly s3Client = new S3StorageClient();

    constructor() {
        this.bucket = S3Config.bucket;
    }

    async getSignedUrl(key: string, expiresIn?: number): Promise<string> {
        return this.s3Client.getSignedUrl(this.bucket, key, expiresIn);
    }

    async deleteFile(key: string): Promise<void> {
        await this.s3Client.deleteFile(this.bucket, key);
    }

    async deleteFiles(listOfKeys: string[]): Promise<void> {
        await this.s3Client.deleteFiles(this.bucket, listOfKeys);
    }

    async getFileList(prefix: string): Promise<string[]> {
        return this.s3Client.getFileList(this.bucket, prefix);
    }

    public async uploadFile(params: UploadParams): Promise<void> {
        const { Key, ContentType, Body, ...rest } = params;
        await this.s3Client.uploadFile({
            Bucket: this.bucket,
            Key,
            ContentType,
            Body,
            ...rest,
        });
    }

    public async uploadWebinarCover(params: {
        webinarId: string;
        cover: IFileUpload;
    }): Promise<{ key: string; name: string }> {
        const { buffer, filename, mimetype } = params.cover;
        const { name, ext } = parse(filename);

        const key = this.getWebinarCoverKey({
            webinarId: params.webinarId,
            name,
            ext,
        });
        const uploadParams: UploadParams = {
            Body: buffer,
            Key: key,
            ContentType: mimetype,
            ACL: 'public-read',
            CacheControl: `max-age=86400`,
            Tagging: `environment=${appConfig.nodeEnv}&webinarId=${params.webinarId}`,
        };
        await this.uploadFile(uploadParams);

        return { key, name };
    }

    public async uploadStreamCover(params: {
        webinarId: string;
        streamId: string;
        cover: IFileUpload;
    }): Promise<{ key: string; name: string }> {
        const { buffer, filename, mimetype } = params.cover;
        const { name, ext } = parse(filename);
        const { webinarId, streamId } = params;

        const key = this.getStreamCoverKey({ webinarId, streamId, name, ext });
        const uploadParams = {
            Body: buffer,
            Key: key,
            ContentType: mimetype,
            ACL: 'public-read',
            CacheControl: `max-age=86400`,
            Tagging: `environment=${appConfig.nodeEnv}&webinarId=${params.webinarId}&streamId=${params.streamId}`,
        };
        await this.uploadFile(uploadParams);

        return { key, name };
    }

    public getUrl(key: string): string {
        return `${S3Config.endpoint}/${S3Config.bucket}/${key}`;
    }

    public async removeWebinarCovers(webinarId: string): Promise<void> {
        const coverKeys = await this.getFileList(`/webinar/${webinarId}/cover/`);
        await this.deleteFiles(coverKeys);
    }

    public async removeStreamCovers(params: { webinarId: string; streamId: string }): Promise<void> {
        try {
            const coverKeys = await this.getFileList(`/webinar/${params.webinarId}/stream/${params.streamId}/cover/`);
            await this.deleteFiles(coverKeys);
        } catch (e) {
            throw new CustomError('Ошибка удаления обложек', {
                code: ERROR_CODE.INTEGRATION_ERROR,
                cause: e,
                details: { webinarId: params.webinarId, streamId: params.streamId },
            });
        }
    }

    public async removeAllWebinarFiles(webinarId: string): Promise<void> {
        const webinarFileKeys = await this.getFileList(`/webinar/${webinarId}/`);
        return this.deleteFiles(webinarFileKeys);
    }

    private getWebinarCoverKey({ webinarId, name, ext }: { webinarId: string; name: string; ext: string }): string {
        return `/webinar/${webinarId}/cover/${this.slugifyFileName(name, ext)}`;
    }

    private getStreamCoverKey({
        webinarId,
        streamId,
        name,
        ext,
    }: {
        webinarId: string;
        streamId: string;
        name: string;
        ext: string;
    }): string {
        return `/webinar/${webinarId}/stream/${streamId}/cover/${this.slugifyFileName(name, ext)}`;
    }

    private slugifyFileName(name: string, ext: string): string {
        const slaggedName = slugify(name, {
            replacement: '_',
            lower: true,
            strict: true,
            locale: 'en',
            trim: true,
        });
        return slaggedName + ext;
    }
}
