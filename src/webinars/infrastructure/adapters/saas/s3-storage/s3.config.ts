import { resolveStr } from '../../../../../shared/utils/environment.utils';

interface IS3Config {
    region: string;
    endpoint: string;
    bucket: string;
    bucketUrl: string;
    accessKeyId: string;
    secretAccessKey: string;
}

export const S3Config: IS3Config = {
    region: resolveStr('S3_REGION', process.env.S3_REGION),
    endpoint: resolveStr('S3_ENDPOINT', process.env.S3_ENDPOINT),
    bucket: resolveStr('S3_BUCKET', process.env.S3_BUCKET),
    bucketUrl: resolveStr('S3_BUCKET_URL', process.env.S3_BUCKET_URL),
    accessKeyId: resolveStr('S3_API_KEY', process.env.S3_API_KEY),
    secretAccessKey: resolveStr('S3_SECRET_KEY', process.env.S3_SECRET_KEY),
};
