import * as https from 'node:https';
import * as querystring from 'node:querystring';
import { Injectable, Logger } from '@nestjs/common';

import { ICaptchaServiceAdapter } from '../../../../application/adapters/saas/captcha-service-adapter.interface';
import { captchaConfig, ICaptchaConfig } from './captcha.config';

@Injectable()
export class YandexCaptchaAdapter implements ICaptchaServiceAdapter {
    private readonly logger = new Logger(YandexCaptchaAdapter.name);
    private readonly options: ICaptchaConfig;

    constructor() {
        this.options = {
            secret: captchaConfig.secret,
        };
    }

    public async validateCaptcha(token: string, ip: string = null): Promise<boolean> {
        return new Promise((resolve) => {
            const postData = querystring.stringify({
                secret: this.options.secret,
                token,
                ip,
            });

            const options = {
                hostname: 'smartcaptcha.yandexcloud.net',
                port: 443,
                path: '/validate',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Content-Length': Buffer.byteLength(postData),
                },
            };

            const req = https.request(options, (res) => {
                let content = '';

                res.on('data', (chunk) => {
                    content += chunk;
                });

                res.on('end', () => {
                    if (res.statusCode !== 200) {
                        this.logger.verbose(
                            `Разрешаем доступ по причине ошибки: code=${res.statusCode}; message=${content}`,
                        );
                        resolve(true);
                        return;
                    }

                    try {
                        const parsedContent = JSON.parse(content) as { status: string };

                        // TODO: для целей дебага
                        // this.logger.error(`Результат проверки капчи: code=${res.statusCode}; message=${content}`);

                        resolve(parsedContent.status === 'ok');
                    } catch (error) {
                        this.logger.verbose('Ошибка разбора ответа:', error);
                        resolve(true);
                    }
                });
            });

            req.on('error', (error) => {
                this.logger.verbose(`Разрешаем доступ по причине ошибки: ${error}`);
                resolve(true);
            });

            req.write(postData);
            req.end();
        });
    }
}
