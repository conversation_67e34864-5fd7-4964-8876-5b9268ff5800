export interface KinescopeProjectType {
    id: string; // Уникальный идентификатор проекта
    name: string; // Название проекта
    folders: KinescopeProjectFolderType[];
    privacy_type: 'anywhere' | 'nowhere' | 'custom'; // Тип приватности проекта
    privacy_domains: string[]; // Домены, которые имеют доступ к проекту (если privacy_type = 'custom')
    player_id?: string; // ID проигрывателя, связанный с проектом (опционально)
    favorite: boolean; // Флаг, указывающий, является ли проект избранным
    size: number; // Размер проекта (возможно, в байтах)
    items_count: number; // Количество элементов (видео, файлов) в проекте
    created_at: string; // Время создания проекта (ISO 8601 формат)
    updated_at: string | null; // Время последнего обновления проекта (ISO 8601 формат) или null, если не обновлялся
    encrypted: boolean; // Флаг, указывающий, зашифрован ли проект
}

export type KinescopeProjectFolderType = KinescopeFolderType & {
    parent_id: string;
    size: number;
    items_count: number;
};

export interface KinescopeFolderType {
    id: string; // Уникальный идентификатор папки
    name: string; // Название папки
    project_id: string; // ID проекта, к которому принадлежит папка
    created_at: string; // Время создания папки (ISO 8601 формат)
    updated_at: string | null; // Время последнего обновления папки (ISO 8601 формат) или null
    deleted_at: string | null; // Время удаления папки (ISO 8601 формат) или null
}

interface KinescopeAssetType {
    id: string;
    video_id: string;
    file_size: number;
    md5: string;
    filetype: string;
    quality: 'original' | '1080p' | '720p' | '480p' | '360p';
    resolution: string;
    created_at: string;
    url: string;
    download_link: string;
}

export interface KinescopeVideoType {
    id: string; // Уникальный идентификатор видео
    project_id: string; // ID проекта, к которому принадлежит видео
    folder_id?: string | null; // ID папки, где находится видео (может быть необязательным или null)
    player_id: string; // ID проигрывателя, используемого для воспроизведения видео
    version: number; // Версия видео
    title: string; // Название видео
    subtitle: string; // Подзаголовок видео (может быть пустым)
    description: string; // Описание видео
    status: 'uploading' | 'processing' | 'done' | 'error'; // Статус загрузки/обработки видео
    progress: number; // Прогресс загрузки/обработки в процентах (0-100)
    duration: number; // Длительность видео в секундах
    assets: KinescopeAssetType[]; // Массив ассетов (возможно, дополнительные файлы или ресурсы)
    chapters: object[]; // Главы видео (массив объектов)
    privacy_type: 'anywhere' | 'nowhere' | 'custom'; // Тип приватности видео
    privacy_domains: string[]; // Список доменов, которые имеют доступ к видео (если privacy_type = 'custom')
    privacy_share: Record<string, unknown>; // Настройки общей приватности (предположительно объект с настройками)
    tags: string[]; // Теги видео
    poster?: string | null; // Ссылка на постер видео (может быть необязательной или null)
    additional_materials: unknown[]; // Дополнительные материалы, связанные с видео
    additional_materials_enabled: boolean; // Флаг, указывающий, включены ли дополнительные материалы
    play_link: string; // Ссылка для просмотра видео
    embed_link: string; // Ссылка для внедрения видео
    created_at: string; // Время создания видео (ISO 8601 формат)
    updated_at?: string | null; // Время последнего обновления видео (ISO 8601 формат) или null
    subtitles: unknown[]; // Субтитры видео (массив объектов)
    subtitles_enabled: boolean; // Флаг, указывающий, включены ли субтитры
    hls_link: string; // Ссылка на HLS-стрим видео
}
