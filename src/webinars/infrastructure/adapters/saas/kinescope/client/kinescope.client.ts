import { setTimeout } from 'node:timers/promises';
import { Injectable, Logger } from '@nestjs/common';

import { EXTERNAL_API_RETRY_COUNT, EXTERNAL_API_RETRY_DELAY } from '../../../../../../configs/app.config';
import { IKinescopeConfig, kinescopeConfig } from '../kinescope.config';
import { KinescopeProjectFolderType, KinescopeProjectType, KinescopeVideoType } from './kinescope.type';

@Injectable()
export class KinescopeClient {
    private logger = new Logger(KinescopeClient.name);
    private readonly config: IKinescopeConfig;

    constructor(config = kinescopeConfig) {
        this.config = config;
    }

    /**
     * PROJECTS
     */

    public async getProjectsList(
        catalogType: 'vod' | 'live' = 'vod',
        perPage = 800,
        page = 1,
    ): Promise<KinescopeProjectType[]> {
        const url = `${this.config.apiUrl}/v1/projects?catalog_type=${catalogType}&per_page=${perPage}&page=${page}`;

        const response = await this.makeApiRequest(url, 'GET');

        const { data } = (await response.json()) as { data: KinescopeProjectType[] };
        this.logger.log(`Fetched ${data.length} projects with catalog type: ${catalogType}, page: ${page}`);
        return data;
    }

    async createProject(name: string): Promise<KinescopeProjectType> {
        const url = `${this.config.apiUrl}/v1/projects`;
        const body = {
            name,
            privacy_type: 'anywhere',
            privacy_domains: [],
        };

        const response = await this.makeApiRequest(url, 'POST', body);

        const { data } = (await response.json()) as { data: KinescopeProjectType };
        this.logger.log(`Project created with ID ${data.id}`);
        return data;
    }

    async getProject(projectId: string): Promise<KinescopeProjectType> {
        const url = `${this.config.apiUrl}/v1/projects/${projectId}`;

        const response = await this.makeApiRequest(url, 'GET');

        const { data } = (await response.json()) as { data: KinescopeProjectType };
        this.logger.log(`Fetched Kinescope project with ID ${data.id}`);
        return data;
    }

    async deleteProject(projectId: string): Promise<void> {
        const url = `${this.config.apiUrl}/v1/projects/${projectId}`;
        await this.makeApiRequest(url, 'DELETE');

        this.logger.log(`Project ${projectId} deleted successfully`);
    }

    /**
     * FOLDERS
     */

    async createFolder(projectId: string, name: string, parentId?: string): Promise<KinescopeProjectFolderType> {
        const url = `${this.config.apiUrl}/v1/projects/${projectId}/folders`;
        const body = parentId ? { name, parentId } : { name };

        const response = await this.makeApiRequest(url, 'POST', body);

        const { data } = (await response.json()) as { data: KinescopeProjectFolderType };
        return data;
    }

    async getFolder(projectId: string, folderId: string): Promise<KinescopeProjectFolderType> {
        const url = `${this.config.apiUrl}/v1/projects/${projectId}/folders/${folderId}`;

        const response = await this.makeApiRequest(url, 'GET');

        const { data } = (await response.json()) as { data: KinescopeProjectFolderType };
        this.logger.log(`Fetched folder ${folderId} in project ${projectId}`);
        return data;
    }

    async deleteFolder(projectId: string, folderId: string): Promise<void> {
        const url = `${this.config.apiUrl}/v1/projects/${projectId}/folders/${folderId}`;
        await this.makeApiRequest(url, 'DELETE');
    }

    /**
     * VIDEOS
     */
    async uploadVideoByUrl(
        parentId: string,
        video: { videoUrl: string; title: string; description?: string },
    ): Promise<void> {
        const { videoUrl, title } = video;
        const description = video.description || '';

        // В заголовках не должно быть кириллицы
        const headers = {
            Authorization: `Bearer ${this.config.accessToken}`,
            'X-Parent-ID': parentId,
            'X-Video-Title': title,
            'X-Video-Description': description,
            'X-Video-URL': videoUrl,
        };

        const options = {
            method: 'POST',
            headers,
        };

        try {
            const response = await fetch(this.config.uploaderUrl, options);
            if (!response.ok) {
                let errorBody;
                try {
                    errorBody = (await response.json()) as { message: string };
                } catch {
                    errorBody = { message: response.statusText };
                }
                throw errorBody;
            }
            return;
        } catch (error) {
            return this.handleRequestError(error);
        }
    }

    async getVideo(videoId: string): Promise<KinescopeVideoType> {
        const url = `${this.config.apiUrl}/v1/videos/${videoId}`;

        const response = await this.makeApiRequest(url, 'GET');

        const { data } = (await response.json()) as { data: KinescopeVideoType };
        this.logger.log(`Fetched video with ID ${data.id}`);
        return data;
    }

    async updateVideo(
        videoId: string,
        options: {
            title?: string;
            description?: string;
            privacy_type?: string; // e.g., 'custom'
            privacy_domains?: string[]; // e.g., ['my_domain.io']
            additional_materials_enabled?: boolean;
            tags?: string[]; // e.g., ['tag 1', 'tag 2']
        },
    ): Promise<KinescopeVideoType> {
        const url = `${this.config.apiUrl}/v1/videos/${videoId}`;

        const response = await this.makeApiRequest(url, 'PATCH', options);

        const { data } = (await response.json()) as { data: KinescopeVideoType };
        this.logger.log(`Updated video with ID ${data.id}`);
        return data;
    }

    async concatVideos(
        videoId: string,
        options: {
            append: string[];
            prepend?: string[];
            replace: boolean;
        },
    ): Promise<void> {
        if ((!options.append || options.append.length === 0) && (!options.prepend || options.prepend.length === 0)) {
            return;
        }
        const url = `${this.config.apiUrl}/v1/videos/${videoId}/concat`;

        const body = {
            append: options.append.map((videoId) => ({ video_id: videoId })),
            prepend: options.prepend ? options.prepend.map((videoId) => ({ video_id: videoId })) : [],
            replace: options.replace,
        };

        await this.makeApiRequest(url, 'POST', body);
    }

    /** Запускает процесс объединения видео в одно и возвращает ссылку на него */
    async generateVideoLink(folderId: string): Promise<string> {
        const records = await this.getVideos(folderId);
        if (records.length === 0) {
            return null;
        }

        if (records.length === 1) {
            return records[0].embed_link;
        }

        const videoIds = records
            .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
            .map((r) => r.id);

        await this.concatVideos(videoIds[0], { append: videoIds.slice(1), replace: false });
        const mergedRecords = await this.getVideos(folderId);
        const latestRecord = mergedRecords.sort(
            (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
        )[0];
        return latestRecord?.embed_link || null;
    }

    async deleteVideo(videoId: string): Promise<void> {
        const url = `${this.config.apiUrl}/v1/videos/${videoId}`;

        await this.makeApiRequest(url, 'DELETE');
    }

    async getVideos(
        parentId: string,
        options: {
            page?: number;
            perPage?: number;
            order?: string; // created_at.desc, title.asc  ==> title, description, created_at, updated_at, duration
            status?: string[]; // pending, uploading, pre-processing, processing, aborted, done, error
            folderId?: string;
            projectId?: string;
            videoIds?: string[];
            q?: string;
            withoutFolder?: boolean;
        } = {},
    ): Promise<KinescopeVideoType[]> {
        const url = `${this.config.apiUrl}/v1/videos`;

        // Формируем параметры запроса
        const queryParams: Record<string, string | number | boolean> = {
            page: options.page || 1,
            per_page: options.perPage || 100,
            order: options.order || 'created_at.desc,title.asc',
            folder_id: options.folderId || parentId,
            project_id: options.projectId || parentId,
            status: options.status?.join(',') || '',
            video_ids: options.videoIds?.join(',') || '',
            q: options.q || '',
            without_folder: options.withoutFolder || false,
        };

        Object.keys(queryParams).forEach((key) => queryParams[key] === '' && delete queryParams[key]);

        const queryString = new URLSearchParams(queryParams as Record<string, string>).toString();
        const requestUrl = `${url}?${queryString}`;

        const response = await this.makeApiRequest(requestUrl, 'GET');

        const { data } = (await response.json()) as { data: KinescopeVideoType[] };
        const uploadErrors = data.filter((item: KinescopeVideoType) => item.status === 'error');
        const uploaded = data.filter((item: KinescopeVideoType) => item.status !== 'error');
        if (uploadErrors.length) {
            this.logger.warn(
                {
                    parentId,
                    uploadErrors: uploadErrors.map(({ id, duration, play_link }) => ({ id, duration, play_link })),
                    uploaded: uploaded.map(({ id, duration, play_link }) => ({ id, duration, play_link })),
                },
                'В папке Kinescope обнаружены ошибки загрузки файлов',
            );
        }
        return uploaded;
    }

    private async makeApiRequest(
        url: string,
        method: 'POST' | 'PUT' | 'PATCH' | 'GET' | 'DELETE',
        body = null,
        retries = 0,
    ): Promise<Response> {
        const options: RequestInit = {
            method,
            headers: {
                Authorization: `Bearer ${this.config.accessToken}`,
                'Content-Type': 'application/json',
            },
        };

        if (body) {
            options.body = JSON.stringify(body);
        }

        try {
            const response = await fetch(url, options);

            if (!response.ok) {
                let errorBody;
                try {
                    errorBody = (await response.json()) as { message: string };
                } catch {
                    errorBody = { message: response.statusText };
                }
                throw errorBody;
            }

            return response;
        } catch (error) {
            if (
                retries < EXTERNAL_API_RETRY_COUNT &&
                (error as { cause?: { code?: string } }).cause?.code === 'EAI_AGAIN'
            ) {
                await setTimeout(EXTERNAL_API_RETRY_DELAY);
                return this.makeApiRequest(url, method, body, retries + 1);
            }

            return this.handleRequestError(error);
        }
    }

    private handleRequestError(error: unknown): never {
        const typedError = error as { cause?: { code?: string }; message?: string };

        if (typedError.cause?.code === 'EAI_AGAIN') {
            throw new Error('Сетевая ошибка: Не удалось разрешить имя хоста ' + this.config.apiUrl);
        }

        if (typedError.message === 'fetch failed') {
            throw new Error('Не удалось подключиться к серверу ' + this.config.apiUrl);
        }

        const errorMessage = typedError.message || 'Произошла неожиданная ошибка';
        throw new Error(`Ошибка запроса: ${errorMessage}`);
    }
}
