import { resolveStr } from '../../../../../shared/utils/environment.utils';

export const DEV_KINESCOPE_PROJECT_ID = '7221b1d1-da6a-4b3e-9058-20fee6fa20a6'; // используется только для разработки

export interface IKinescopeConfig {
    apiUrl: string;
    accessToken: string;
    uploaderUrl: string;
}

export const kinescopeConfig: IKinescopeConfig = {
    apiUrl: resolveStr('KINESCOPE_API_URL', process.env.KINESCOPE_API_URL),
    uploaderUrl: `https://uploader.kinescope.io/v2/video`,
    accessToken: resolveStr('KINESCOPE_ACCESS_TOKEN', process.env.KINESCOPE_ACCESS_TOKEN),
};
