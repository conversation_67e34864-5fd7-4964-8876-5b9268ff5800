import { Injectable, Logger } from '@nestjs/common';
import { SaasProviderEnum } from '@prisma/client';
import { CustomError, ERROR_CODE } from '@skillspace/utils';

import { DEFAULT_VIDEO_STORAGE } from '../../../../../configs/app.config';
import {
    IVideoStorageAdapter,
    VideoStorageFolderIds,
    VideoStorageFolderParams,
    VideoStorageRecordType,
} from '../../../../application/adapters/saas/video-storage-adapter.interface';
import { SchoolModel } from '../../../../domain/models/school.model';
import { StreamModel, StreamParams } from '../../../../domain/models/stream/stream.model';
import { WebinarParams } from '../../../../domain/models/webinar/webinar.model';
import { KinescopeClient } from './client/kinescope.client';
import { kinescopeConfig } from './kinescope.config';

@Injectable()
export class KinescopeVideoStorageAdapter implements IVideoStorageAdapter {
    private logger = new Logger(KinescopeVideoStorageAdapter.name);

    private readonly defaultVideoStorage = DEFAULT_VIDEO_STORAGE;
    private readonly config = kinescopeConfig;
    private readonly kinescope = new KinescopeClient(this.config);

    public async createFolderStructure(params: VideoStorageFolderParams): Promise<VideoStorageFolderIds> {
        const { school: aSchool, webinar: aWebinar, stream: aStream } = params;

        if (this.defaultVideoStorage === SaasProviderEnum.Kinescope) {
            let kinescopeWebinarId = aWebinar.kinescopeFolderId;
            let kinescopeStreamId = aStream.kinescopeFolderId;

            if (!kinescopeWebinarId) {
                const folderName = `Вебинар ${aWebinar.title} ${aWebinar.id}`;
                const webinarFolder = await this.kinescope.createFolder(
                    aSchool.kinescope.projectId,
                    folderName,
                    aSchool.kinescope.folderId,
                );
                kinescopeWebinarId = webinarFolder.id;
            }

            if (!kinescopeStreamId) {
                const folderName = `Поток ${aStream.title} ${aStream.id}`;
                const streamFolder = await this.kinescope.createFolder(
                    aSchool.kinescope.projectId,
                    folderName,
                    kinescopeWebinarId,
                );
                kinescopeStreamId = streamFolder.id;
            }
            this.logger.log(
                {
                    webinarFolderId: kinescopeWebinarId,
                    streamFolderId: kinescopeStreamId,
                },
                'Синхронизирована структура директорий для потока вебинара в kinescope',
            );
            return {
                webinarFolderId: kinescopeWebinarId,
                streamFolderId: kinescopeStreamId,
            };
        }
        throw new Error('Not implemented');
    }

    public async createProjectSubFolder(kinescopeProjectId: string, folderName: string): Promise<string> {
        try {
            const folder = await this.kinescope.createFolder(kinescopeProjectId, folderName);
            return folder.id;
        } catch (e) {
            const project = await this.kinescope.getProject(kinescopeProjectId);
            const webinarsFolder = project.folders.find((f) => f.name === folderName);
            if (!webinarsFolder && e instanceof Error) {
                throw new CustomError(`Ошибка создания папки ${folderName}: ${e.message}`, {
                    code: ERROR_CODE.INTEGRATION_ERROR,
                    cause: e,
                    details: { kinescopeProjectId },
                });
            }
            return webinarsFolder.id;
        }
    }

    public async uploadVideoByURL(aStream: StreamModel, recordUrl: string): Promise<void> {
        if (this.defaultVideoStorage === SaasProviderEnum.Kinescope) {
            try {
                const recordToUpload = {
                    videoUrl: recordUrl,
                    title: `webinar-${aStream.webinarId}`,
                    description: `stream-${aStream.id}`,
                };
                await this.kinescope.uploadVideoByUrl(aStream.kinescopeFolderId, recordToUpload);
            } catch (e) {
                if (e instanceof Error) {
                    throw new CustomError('Ошибка при загрузке видео на Kinescope', {
                        code: ERROR_CODE.INTEGRATION_ERROR,
                        cause: e,
                        details: {
                            recordUrl,
                            room: aStream.room,
                            folderId: aStream.kinescopeFolderId,
                            message: e.message,
                        },
                    });
                }
            }
            return;
        }
        throw new Error('Not implemented');
    }

    public async getRecords(
        aStream: Pick<StreamModel, 'room' | 'id' | 'kinescopeFolderId'>,
    ): Promise<VideoStorageRecordType[]> {
        const room = aStream.room;
        if (room.storage === SaasProviderEnum.Kinescope) {
            if (!aStream.kinescopeFolderId) {
                return [];
            }
            const result = await this.kinescope.getVideos(aStream.kinescopeFolderId);

            this.logger.log(
                { recordsCount: result.length, streamId: aStream.id, folderId: aStream.kinescopeFolderId },
                `Запрошены записи потока из ${room.storage}`,
            );

            return result.map((v) => ({
                id: v.id,
                title: v.title,
                description: v.description,
                createdAt: new Date(v.created_at),
                duration: v.duration,
                tags: v.tags,
                status: v.status,
                embedLink: v.embed_link,
                progress: v.progress,
                assets: v.assets.map((a) => ({
                    fileSize: a.file_size,
                    quality: a.quality,
                    createdAt: a.created_at,
                    downloadLink: a.download_link,
                })),
            }));
        }
        throw new Error('Not implemented');
    }

    public async deleteRecord(videoId: string): Promise<void> {
        await this.kinescope.deleteVideo(videoId);
    }

    /** Запускает процесс объединения видео и возвращает ссылку для встраивания в плеер */
    public async generateEmbedVideoLink(stream: StreamModel): Promise<string> {
        const storage = stream.room.storage;
        if (storage === SaasProviderEnum.Kinescope) {
            if (!stream.kinescopeFolderId) {
                return '';
            }
            const url = await this.kinescope.generateVideoLink(stream.kinescopeFolderId);
            if (!url) {
                throw new CustomError(
                    `На хостинге видео ${storage} не найдены записи в папке ${stream.kinescopeFolderId}`,
                    {
                        code: ERROR_CODE.NOT_FOUND_ERROR,
                        details: {
                            ...stream.logParams,
                            room: stream.room,
                        },
                    },
                );
            }
            return url;
        }
        throw new Error('Not implemented');
    }

    public async getDownloadVideoLink(
        stream: Pick<StreamModel, 'room' | 'id' | 'kinescopeFolderId' | 'embedRecordUrl'>,
    ): Promise<string> {
        if (!stream.embedRecordUrl) {
            this.logger.warn(
                { room: stream.room, embed: stream.embedRecordUrl },
                'Поток не имеет встроенной записи видео',
            );
            return '';
        }

        const videoRecords = await this.getRecords(stream);

        if (videoRecords.length === 0) {
            this.logger.warn({ room: stream.room }, 'При запросе ссылки на скачивание не найдены записи потока');
            return '';
        }

        const record = videoRecords.find((r) => r.embedLink === stream.embedRecordUrl);
        if (!record) {
            this.logger.warn({ room: stream.room, videoRecords }, 'Не найдена запись видео по встроенной ссылке');
            return '';
        }

        const assetOrigin = record.assets.find((a) => a.quality === 'original');
        if (assetOrigin?.downloadLink) {
            this.logger.log(
                { link: assetOrigin.downloadLink },
                'Получена ссылка на скачивание видео в качестве origin',
            );
            return assetOrigin.downloadLink;
        }

        const asset1080p = record.assets.find((a) => a.quality === '1080p');
        if (asset1080p?.downloadLink) {
            this.logger.log({ link: assetOrigin.downloadLink }, 'Получена ссылка на скачивание видео в качестве 1080p');
            return asset1080p.downloadLink;
        }

        this.logger.warn({ assets: record.assets }, 'Не найдена запись для скачивания видео');
        return '';
    }

    public async deleteStreamRecords(aSchool: SchoolModel, stream: StreamParams): Promise<void> {
        if (stream.room.storage === SaasProviderEnum.Kinescope) {
            if (!stream.kinescopeFolderId) {
                return;
            }
            try {
                await this.kinescope.deleteFolder(aSchool.kinescope.projectId, stream.kinescopeFolderId);
                this.logger.log({ folderId: stream.kinescopeFolderId }, `Удалены записи потока вебинара в Kinescope`);
                return;
            } catch (e) {
                throw new CustomError(`Ошибка удаления записей на хостинге ${stream.room.storage}`, {
                    code: ERROR_CODE.INTEGRATION_ERROR,
                    cause: e,
                    details: {
                        kinescopeFolderId: stream.kinescopeFolderId,
                        room: stream.room,
                    },
                });
            }
        }

        throw new Error('Not implemented');
    }

    public async deleteWebinarRecords(aSchool: SchoolModel, aWebinar: WebinarParams): Promise<void> {
        const streams = aWebinar.streams;
        await Promise.allSettled(streams.map((stream) => this.deleteStreamRecords(aSchool, stream)));
    }
}
