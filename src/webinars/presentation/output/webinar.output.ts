import { Field, ID, ObjectType, registerEnumType } from '@skillspace/graphql';

import {
    RegistrationTypePresentation,
    WebinarTypePresentation,
    WebinarVisibilityPresentation,
} from '../enum.presentation';
import { StreamOutput } from './stream.output';

registerEnumType(WebinarTypePresentation, { name: 'WebinarType' });
registerEnumType(WebinarVisibilityPresentation, { name: 'WebinarVisibility' });
registerEnumType(RegistrationTypePresentation, { name: 'RegistrationType' });

@ObjectType()
export class WebinarOutput {
    @Field(() => ID)
    id: string;

    @Field(() => WebinarTypePresentation)
    type: WebinarTypePresentation;

    // info

    @Field()
    title: string;

    @Field()
    description: string;

    @Field(() => String, { nullable: true }) // Временно nullable
    coverUrl: string;

    @Field()
    createdBy: string;

    // points

    @Field()
    useAttendancePoints: boolean;

    @Field(() => Number, { nullable: true })
    attendancePoints: number;

    // notifications

    @Field()
    useNotifications: boolean;

    @Field(() => Number, { nullable: true })
    studentNotifyFirst: number;

    @Field(() => Number, { nullable: true })
    studentNotifySecond: number;

    // landing

    @Field(() => Boolean, { nullable: true })
    useDefaultLanding: boolean;

    @Field(() => String, { nullable: true })
    externalLandingUrl: string;

    // access

    @Field(() => WebinarVisibilityPresentation)
    visibility: WebinarVisibilityPresentation;

    @Field(() => Number, { nullable: true })
    maxStudentCapacity: number;

    @Field()
    enableStudentLimit: boolean;

    // registration

    @Field()
    registrationType: RegistrationTypePresentation;

    @Field()
    registrationForEntry: boolean;

    @Field()
    registrationForView: boolean;

    @Field(() => String, { nullable: true })
    integrationCode: string;

    @Field(() => Boolean, { nullable: true })
    isPhoneRequiredOnRegistration: boolean;

    // web streams

    @Field(() => [StreamOutput])
    streams: StreamOutput[];

    // Counts
    @Field(() => Number, { nullable: true })
    countRegistered: number;

    @Field(() => Number, { nullable: true })
    countViewed: number;

    @Field(() => Boolean)
    canModify: boolean;

    @Field(() => Boolean)
    canRemove: boolean;
}
