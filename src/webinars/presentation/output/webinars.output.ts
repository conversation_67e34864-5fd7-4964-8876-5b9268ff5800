import { Field, ObjectType } from '@skillspace/graphql';

@ObjectType()
class Streams {
    @Field()
    id: string;

    @Field()
    date: number;

    @Field()
    duration: number;

    @Field()
    title: string;

    @Field()
    countRegistered: number;

    @Field()
    webinarId: string;
}

@ObjectType()
export class WebinarsOutput {
    @Field({ nullable: true })
    coverUrl?: string;

    @Field()
    title: string;

    @Field()
    id: string;

    @Field()
    countRegistered: number;

    @Field(() => [Streams])
    streams: Streams[];
}
