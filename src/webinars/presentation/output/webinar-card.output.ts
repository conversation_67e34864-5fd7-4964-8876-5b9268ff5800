import { Field, ID, ObjectType, registerEnumType } from '@skillspace/graphql';

import { WebinarCardQueryResult } from '../../application/queries/result/webinar-card-query.result';
import { CardsViewMode, WebinarVisibilityPresentation } from '../enum.presentation';

export interface IWebinarCardOutput extends Omit<WebinarCardQueryResult, 'date'> {
    date: number;
}

registerEnumType(CardsViewMode, { name: 'CardsViewMode' });

@ObjectType()
export class WebinarCardOutput implements IWebinarCardOutput {
    @Field(() => ID)
    streamId: string;

    @Field()
    webinarId: string;

    @Field()
    visibility: WebinarVisibilityPresentation;

    @Field()
    isPhoneRequiredOnRegistration: boolean;

    // Info

    @Field()
    title: string;

    @Field()
    description: string;

    @Field(() => String, { nullable: true })
    coverUrl: string;

    @Field(() => Boolean, { nullable: true })
    hasRecord: boolean;

    @Field()
    date: number;

    @Field()
    duration: number;

    @Field(() => Boolean)
    isRegistered: boolean;

    @Field(() => Boolean)
    isSpeaker: boolean;

    @Field(() => Boolean)
    isViewed: boolean;

    @Field(() => Boolean)
    isAllowedToJoin: boolean;

    @Field(() => Boolean)
    isLive: boolean;
}

@ObjectType()
export class WebinarCardPaginatedOutput {
    @Field(() => [WebinarCardOutput])
    data: WebinarCardOutput[];

    @Field(() => Number, { nullable: true })
    total: number;
}
