import { Field, ID, ObjectType, registerEnumType } from '@skillspace/graphql';

import { RegistrationTypePresentation, StreamTypePresentation } from '../enum.presentation';

registerEnumType(StreamTypePresentation, { name: 'StreamType' });

@ObjectType()
export class StreamOutput {
    @Field(() => ID)
    id: string;

    @Field()
    webinarId: string;

    @Field()
    webinarTitle: string;

    @Field()
    webinarDescription: string;

    @Field(() => Boolean, { nullable: true })
    isPhoneRequiredOnRegistration: boolean;

    @Field()
    title: string;

    @Field()
    date: number;

    @Field()
    duration: number;

    @Field()
    streamType: StreamTypePresentation;

    @Field(() => String, { nullable: true })
    externalStreamUrl: string;

    @Field(() => String, { nullable: true })
    coverUrl: string;

    // Records
    @Field(() => Boolean, { nullable: true })
    hasRecord = false;

    @Field(() => Boolean, { nullable: true })
    hasRecordUrl = false;

    @Field(() => String, { nullable: true })
    recordUrl: string;

    @Field(() => Boolean, { nullable: true })
    autoRecord = false;

    @Field(() => String, { nullable: true })
    savedRecordUrl: string;
    // end Records

    @Field(() => String, { nullable: true })
    integrationCode: string;

    @Field(() => RegistrationTypePresentation)
    registrationType: RegistrationTypePresentation;

    @Field(() => [String])
    speakers: string[];

    @Field(() => Number, { nullable: true })
    countRegistered: number;

    @Field(() => Number, { nullable: true })
    countViewed: number;

    @Field(() => Boolean)
    isRegistered: boolean;

    @Field(() => Boolean)
    isSpeaker: boolean;

    @Field(() => Boolean)
    canModify: boolean;

    @Field(() => Boolean)
    canRemove: boolean;

    @Field(() => Boolean)
    isAllowedToJoin: boolean;

    @Field(() => Boolean)
    isLive: boolean;

    @Field(() => [String], { nullable: true })
    pages: string[];
}
