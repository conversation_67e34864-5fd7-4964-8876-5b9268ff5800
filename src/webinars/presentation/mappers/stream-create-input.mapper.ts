import { StreamCreateParams } from '../../domain/models/stream/stream.model';
import { mapToStreamType } from '../enum.presentation';
import { IStreamCreateInput } from '../input/stream-create.input';

export const mapStreamCreateInputToParams = (
    streamCreateInput: IStreamCreateInput,
    webinarId: string,
    schoolUuid: string,
): StreamCreateParams => {
    return {
        ...streamCreateInput,
        webinarId,
        schoolUuid,
        streamType: mapToStreamType(streamCreateInput.streamType),
    };
};
