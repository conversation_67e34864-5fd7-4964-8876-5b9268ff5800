import { removeUndefinedProperties } from '../../../shared/mapper.utils';
import { WebinarUpdateParams } from '../../domain/models/webinar/webinar.model';
import { mapToRegistrationType, mapToWebinarVisibility } from '../enum.presentation';
import { WebinarExternalUpdateInput } from '../input/webinar-update.input';

export const mapExternalToUpdateInput = (input: WebinarExternalUpdateInput): WebinarUpdateParams => {
    return removeUndefinedProperties({
        ...input,
        visibility: input?.visibility ? mapToWebinarVisibility(input.visibility) : undefined,
        registrationType: input?.registrationType ? mapToRegistrationType(input.registrationType) : undefined,
    });
};
