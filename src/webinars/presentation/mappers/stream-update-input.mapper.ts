import { removeUndefinedProperties } from '../../../shared/mapper.utils';
import { StreamUpdateParams } from '../../domain/models/stream/stream.model';
import { mapToStreamType } from '../enum.presentation';
import { StreamUpdateInput } from '../input/stream-update.input';

export const mapStreamUpdateInputToParams = (updateInput: StreamUpdateInput): StreamUpdateParams => {
    return removeUndefinedProperties({
        ...updateInput,
        streamType: updateInput?.streamType ? mapToStreamType(updateInput.streamType) : undefined,
    });
};
