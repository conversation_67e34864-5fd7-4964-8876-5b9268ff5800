import { WebinarCreateParams } from '../../domain/models/webinar/webinar.model';
import { mapToRegistrationType, mapToStreamType, mapToWebinarType, mapToWebinarVisibility } from '../enum.presentation';
import { IWebinarExternalCreateInput } from '../input/webinar-create.input';

export const mapExternalToCreateInput = (
    input: Omit<IWebinarExternalCreateInput, 'cover'>,
    schoolUuid: string,
): WebinarCreateParams => {
    const { streams, ...restInput } = input;
    return {
        ...restInput,
        type: input?.type ? mapToWebinarType(input.type) : undefined,
        visibility: input?.visibility ? mapToWebinarVisibility(input.visibility) : undefined,
        registrationType: mapToRegistrationType(input.registrationType),
        streams: streams.map((stream) => ({
            ...stream,
            schoolUuid,
            webinarId: undefined,
            streamType: mapToStreamType(stream.streamType),
        })),
    };
};
