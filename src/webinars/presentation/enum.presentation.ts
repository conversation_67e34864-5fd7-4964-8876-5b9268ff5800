import { RegistrationType, StreamType, WebinarType, WebinarVisibility } from '@prisma/client';

/*
 * Cards View Mode
 */

export enum CardsViewMode {
    library = 'library',
    catalog = 'catalog',
}

/*
 * WebinarType
 */

export enum WebinarTypePresentation {
    internal = 'internal',
    external = 'external',
}

export function mapToWebinarType(type: WebinarTypePresentation): WebinarType {
    return {
        [WebinarTypePresentation.internal]: WebinarType.INTERNAL,
        [WebinarTypePresentation.external]: WebinarType.EXTERNAL,
    }[type];
}

export function mapToWebinarTypePresentation(type: WebinarType): WebinarTypePresentation {
    return {
        [WebinarType.INTERNAL]: WebinarTypePresentation.internal,
        [WebinarType.EXTERNAL]: WebinarTypePresentation.external,
    }[type];
}

/*
 * WebinarVisibility
 */

export enum WebinarVisibilityPresentation {
    draft = 'draft',
    public = 'public',
    private = 'private',
}

export function mapToWebinarVisibility(visibility: WebinarVisibilityPresentation): WebinarVisibility {
    return {
        [WebinarVisibilityPresentation.draft]: WebinarVisibility.DRAFT,
        [WebinarVisibilityPresentation.public]: WebinarVisibility.PUBLIC,
        [WebinarVisibilityPresentation.private]: WebinarVisibility.PRIVATE,
    }[visibility];
}

export function mapToWebinarVisibilityPresentation(visibility: WebinarVisibility): WebinarVisibilityPresentation {
    return {
        [WebinarVisibility.DRAFT]: WebinarVisibilityPresentation.draft,
        [WebinarVisibility.PUBLIC]: WebinarVisibilityPresentation.public,
        [WebinarVisibility.PRIVATE]: WebinarVisibilityPresentation.private,
    }[visibility];
}

/*
 * StreamType
 */

export enum StreamTypePresentation {
    internal = 'internal',
    external = 'external',
}

export function mapToStreamType(type: StreamTypePresentation): StreamType {
    return {
        [StreamTypePresentation.internal]: StreamType.INTERNAL,
        [StreamTypePresentation.external]: StreamType.EXTERNAL,
    }[type];
}

export function mapToStreamTypePresentation(type: StreamType): StreamTypePresentation {
    return {
        [StreamType.INTERNAL]: StreamTypePresentation.internal,
        [StreamType.EXTERNAL]: StreamTypePresentation.external,
    }[type];
}

/*
 * RegistrationType
 */

export enum RegistrationTypePresentation {
    byLanding = 'byLanding',
    byInvitation = 'byInvitation',
}

export function mapToRegistrationType(type: RegistrationTypePresentation): RegistrationType {
    return {
        [RegistrationTypePresentation.byLanding]: RegistrationType.LANDING,
        [RegistrationTypePresentation.byInvitation]: RegistrationType.INVITATION,
    }[type];
}

export function mapToRegistrationTypePresentation(type: RegistrationType): RegistrationTypePresentation {
    return {
        [RegistrationType.LANDING]: RegistrationTypePresentation.byLanding,
        [RegistrationType.INVITATION]: RegistrationTypePresentation.byInvitation,
    }[type];
}
