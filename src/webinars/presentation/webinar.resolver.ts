import { Inject, Logger } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { GqlSchoolId, GqlUser, UserContext } from '@skillspace/access';
import { Args, Field, FileUpload, GraphQLUpload, ID, InputType, Mutation, Query, Resolver } from '@skillspace/graphql';

import { GetFileUploadConvert, IFileUpload } from '../../shared/gql-file-upload-convert';
import { IFileStorageAdapter } from '../application/adapters/saas/file-storage-adapter.interface';
import { CreateWebinarCommand } from '../application/commands/webinars/create-webinar.command';
import { DeleteWebinarCommand } from '../application/commands/webinars/delete-webinar.command';
import { UpdateWebinarCommand } from '../application/commands/webinars/update-webinar.command';
import { GetWebinarQuery } from '../application/queries/get-webinar';
import { GetWebinarCardsQuery } from '../application/queries/get-webinar-cards';
import { GetWebinarsQuery } from '../application/queries/get-webinars';
import { GetWebinarsResult } from '../application/queries/result/get-webinars.result';
import { UserFactory } from '../domain/models/user/user.factory';
import { SchoolUuid } from '../domain/objects/school-uuid';
import { WebinarId } from '../domain/objects/webinar-id';
import { FILE_STORAGE_ADAPTER } from '../injects';
import { CardsViewMode } from './enum.presentation';
import { WebinarExternalCreateInput } from './input/webinar-create.input';
import { WebinarExternalUpdateInput } from './input/webinar-update.input';
import { mapExternalToCreateInput } from './mappers/webinar-create-input.mapper';
import { mapExternalToUpdateInput } from './mappers/webinar-update-input.mapper';
import { VoidResponse } from './output/void-response.output';
import { WebinarOutput } from './output/webinar.output';
import { WebinarCardPaginatedOutput } from './output/webinar-card.output';
import { WebinarsOutput } from './output/webinars.output';

@InputType() // TODO: добавить валидацию и вынести в отдельный файл
export class WebinarCardsFilter {
    @Field(() => Boolean, { nullable: true })
    onlyAssigned?: boolean;

    @Field(() => Boolean, { nullable: true })
    isActive?: boolean;

    @Field(() => CardsViewMode)
    mode: CardsViewMode;

    @Field(() => String, { nullable: true })
    search?: string;
}

@Resolver(() => WebinarOutput)
export class WebinarsResolver {
    private readonly logger = new Logger(WebinarsResolver.name);

    constructor(
        private readonly commandBus: CommandBus,
        private readonly queryBus: QueryBus,
        @Inject(FILE_STORAGE_ADAPTER)
        private readonly storageService: IFileStorageAdapter,
    ) {}

    @Query(() => WebinarOutput, { name: 'webinar', nullable: true })
    async getOneWebinar(
        @GqlUser() user: UserContext,
        @GqlSchoolId() schoolUuid: string,
        @Args('webinarId', { type: () => ID })
        webinarId: string,
    ): Promise<WebinarOutput | null> {
        const anUser = UserFactory.create({ ...user, schoolUuid });
        return this.queryBus.execute(new GetWebinarQuery(anUser, webinarId));
    }

    @Query(() => [WebinarsOutput], { name: 'webinars' })
    async getWebinars(@GqlSchoolId({ type: SchoolUuid }) schoolUuid: SchoolUuid): Promise<WebinarsOutput[]> {
        return (await this.queryBus.execute<GetWebinarsQuery, GetWebinarsResult>(new GetWebinarsQuery(schoolUuid)))
            .data;
    }

    @Query(() => WebinarCardPaginatedOutput, {
        name: 'webinarCards',
        nullable: true,
    })
    async getWebinarCards(
        @GqlUser() user: UserContext,
        @GqlSchoolId() schoolUuid: string,
        @Args('filter', { type: () => WebinarCardsFilter })
        filter: WebinarCardsFilter,
        // @Args('pagination', { type: () => PaginationDto, nullable: true }, ParsePaginationPipe)
        pagination = undefined,
        currentDate = new Date(),
    ): Promise<WebinarCardPaginatedOutput> {
        const anUser = UserFactory.create({ ...user, schoolUuid });
        return this.queryBus.execute(new GetWebinarCardsQuery(anUser, filter, pagination, currentDate));
    }

    @Mutation(() => WebinarOutput, { name: 'createWebinar' })
    async createWebinar(
        @GqlUser() user: UserContext,
        @GqlSchoolId() schoolUuid: string,
        @Args('createWebinarExternalInput')
        createWebinarExternalInput: WebinarExternalCreateInput,
        @Args({ name: 'cover', type: () => GraphQLUpload, nullable: true })
        cover?: { promise: Promise<FileUpload> },
    ): Promise<WebinarOutput> {
        const params = mapExternalToCreateInput(createWebinarExternalInput, schoolUuid);
        const coverImage = (await GetFileUploadConvert.convert(cover ? [await cover.promise] : []))[0];

        // Создаем вебинар
        const anUser = UserFactory.create({ ...user, schoolUuid });
        const webinarId: string = await this.commandBus.execute(new CreateWebinarCommand(anUser, params));

        if (coverImage) {
            const { key, name } = await this.storageService.uploadWebinarCover({
                webinarId,
                cover: coverImage,
            });
            await this.commandBus.execute(
                new UpdateWebinarCommand(anUser, webinarId, {
                    coverImage: { key, name },
                }),
            );
        }

        // Возвращаем результат
        return this.queryBus.execute(new GetWebinarQuery(anUser, webinarId));
    }

    @Mutation(() => WebinarOutput, { name: 'updateWebinar', nullable: true })
    async updateWebinar(
        @GqlUser() user: UserContext,
        @GqlSchoolId() schoolUuid: string,
        @Args('webinarId', { type: () => ID })
        webinarId: string,
        @Args('updateWebinarExternalInput')
        updateWebinarExternalInput: WebinarExternalUpdateInput,
        @Args({ name: 'cover', type: () => GraphQLUpload, nullable: true })
        cover?: { promise: Promise<FileUpload> },
    ): Promise<WebinarOutput> {
        // проверяем значение
        WebinarId.create(webinarId);
        // Загружаем обложку, если есть
        let updateInput;
        if (cover) {
            const oFile = await cover.promise;
            const file: IFileUpload = (await GetFileUploadConvert.convert(oFile.createReadStream ? [oFile] : []))[0];
            await this.storageService.removeWebinarCovers(webinarId);
            const { key, name } = await this.storageService.uploadWebinarCover({
                webinarId,
                cover: file,
            });
            updateInput = {
                ...updateWebinarExternalInput,
                coverImage: { key, name },
            };
        } else {
            updateInput = updateWebinarExternalInput;
        }

        const anUser = UserFactory.create({ ...user, schoolUuid });
        const params = mapExternalToUpdateInput(updateInput as WebinarExternalUpdateInput);
        await this.commandBus.execute(new UpdateWebinarCommand(anUser, webinarId, params));
        return this.queryBus.execute(new GetWebinarQuery(anUser, webinarId));
    }

    @Mutation(() => VoidResponse, { name: 'deleteWebinar', nullable: true })
    async deleteWebinar(
        @GqlUser() user: UserContext,
        @GqlSchoolId() schoolUuid: string,
        @Args('webinarId', { type: () => ID })
        webinarId: string,
    ): Promise<VoidResponse> {
        const anUser = UserFactory.create({ ...user, schoolUuid });
        const aWebinarId = WebinarId.wrap(webinarId);
        await this.commandBus.execute(new DeleteWebinarCommand(anUser, aWebinarId));
        return { success: true };
    }
}
