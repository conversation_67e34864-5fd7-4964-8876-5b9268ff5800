import { <PERSON>, Controller, Get, Header, Logger, Post } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { SaasProviderEnum } from '@prisma/client';

import { DEFAULT_VIDEO_STORAGE } from '../../configs/app.config';
import { MarkRoomAsLiveCommand } from '../application/commands/rooms/mark-room-as-live.command';
import { MarkRoomAsOfflineCommand } from '../application/commands/rooms/mark-room-as-offline.command';
import { UploadRoomRecordCommand } from '../application/commands/rooms/upload-room-record.command';
import { LiveDigitalEvent, LiveDigitalWebhookBody } from './input/live-digital-webhook.dto';

@Controller('s/webinars/webhooks')
export class WebhooksController {
    constructor(private readonly commandBus: CommandBus) {}

    private readonly logger = new Logger(WebhooksController.name);

    @Get('live-digital')
    @Header('X-Test-Header', 'isAvailable')
    getTest() {
        return 'Test response';
    }

    @Post('live-digital')
    async handleLiveDigitalWebhook(@Body() webhook, currentDate = new Date()): Promise<void> {
        const webhookBody = (webhook as { body: LiveDigitalWebhookBody }).body;
        const event = webhookBody.eventName as LiveDigitalEvent;

        this.logger.log({ event: webhookBody.eventName }, 'Получен вебхук от LiveDigital');

        if (event === LiveDigitalEvent.CALL_STARTED) {
            return this.commandBus.execute(
                new MarkRoomAsLiveCommand({ ...webhookBody, provider: DEFAULT_VIDEO_STORAGE }),
            );
        }

        if (event === LiveDigitalEvent.RECORD_FINISHED) {
            return this.commandBus.execute(
                new UploadRoomRecordCommand({
                    ...webhookBody,
                    provider: SaasProviderEnum.LiveDigital,
                } as UploadRoomRecordCommand['params']),
            );
        }

        if (event === LiveDigitalEvent.CALL_FINISHED) {
            return this.commandBus.execute(
                new MarkRoomAsOfflineCommand({ ...webhookBody, provider: DEFAULT_VIDEO_STORAGE }, currentDate),
            );
        }
    }
}
