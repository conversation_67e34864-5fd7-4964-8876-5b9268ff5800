import { Field, InputType } from '@skillspace/graphql';
import {
    ArrayNotEmpty,
    IsBoolean,
    IsEnum,
    IsNumber,
    IsOptional,
    IsString,
    IsUrl,
    <PERSON>UUID,
    MaxLength,
} from 'class-validator';

import { StreamTypePresentation } from '../enum.presentation';
import { IStreamCreateInput } from './stream-create.input';

@InputType()
export class StreamUpdateInput implements Partial<IStreamCreateInput> {
    @Field(() => StreamTypePresentation, { nullable: true })
    @IsOptional()
    @IsEnum(StreamTypePresentation)
    streamType?: StreamTypePresentation;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @MaxLength(70)
    title?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    date?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    duration?: number;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsUrl()
    externalStreamUrl?: string;

    @Field(() => Boolean, { nullable: true })
    @IsOptional()
    @IsBoolean()
    hasRecordUrl?: boolean;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsUrl()
    recordUrl?: string;

    @Field(() => Boolean, { nullable: true })
    @IsOptional()
    @IsBoolean()
    autoRecord?: boolean;

    @Field(() => [String], { nullable: true })
    @IsOptional()
    @ArrayNotEmpty()
    @IsUUID('4', { each: true })
    speakers?: string[];
}
