import { ArgsType, Field, ID } from '@skillspace/graphql';
import { IsEmail, IsMongoId, IsNotEmpty, IsOptional, IsString, IsTimeZone } from 'class-validator';

@ArgsType()
export class RegisterStudentArgs {
    @Field(() => ID)
    @IsNotEmpty()
    @IsMongoId()
    streamId: string;

    @Field(() => String)
    @IsNotEmpty()
    @IsEmail()
    email: string;

    @Field(() => String)
    @IsNotEmpty()
    @IsString()
    name: string;

    @Field(() => String)
    @IsNotEmpty()
    @IsTimeZone()
    timezone: string;

    @Field(() => String, { nullable: true })
    @IsOptional()
    phoneNumber?: string;
}
