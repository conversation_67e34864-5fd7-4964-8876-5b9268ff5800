import { Type } from 'class-transformer';
import { ValidateNested } from 'class-validator';

export enum LiveDigitalEvent {
    CALL_STARTED = 'call_started',
    CALL_FINISHED = 'call_finished',
    RECORD_FINISHED = 'record_finished',
}

export class LiveDigitalWebhookBody {
    eventName: LiveDigitalEvent | string;
    roomId: string;
    spaceId: string;
    recordId?: string; // только для record_finished
}

export class LiveDigitalWebhook {
    signature: string;

    @ValidateNested()
    @Type(() => LiveDigitalWebhookBody)
    body: LiveDigitalWebhookBody;
}
