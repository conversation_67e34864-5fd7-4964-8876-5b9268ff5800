import { Field, InputType } from '@skillspace/graphql';
import { Type } from 'class-transformer';
import {
    ArrayNotEmpty,
    IsBoolean,
    IsEnum,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
    IsUrl,
    <PERSON><PERSON>ength,
    Min,
    ValidateNested,
} from 'class-validator';

import {
    RegistrationTypePresentation,
    WebinarTypePresentation,
    WebinarVisibilityPresentation,
} from '../enum.presentation';
import { IStreamCreateInput, StreamCreateInput } from './stream-create.input';

export interface IWebinarExternalCreateInput {
    type?: WebinarTypePresentation;
    title: string;
    description: string;
    // points
    useAttendancePoints?: boolean;
    attendancePoints?: number;
    // notifications
    useNotifications?: boolean;
    studentNotifyFirst?: number;
    studentNotifySecond?: number;
    // landing
    useDefaultLanding?: boolean;
    externalLandingUrl?: string;
    // access
    visibility?: WebinarVisibilityPresentation;
    enableStudentLimit?: boolean;
    maxStudentCapacity?: number;
    // registration
    registrationType: RegistrationTypePresentation;
    registrationForEntry: boolean;
    registrationForView: boolean;
    streams: IStreamCreateInput[];
}

@InputType()
export class WebinarExternalCreateInput implements IWebinarExternalCreateInput {
    @Field({ nullable: true })
    @IsOptional()
    @IsEnum(WebinarTypePresentation)
    type?: WebinarTypePresentation;

    @Field()
    @IsString()
    @IsNotEmpty()
    @MaxLength(70)
    title: string;

    @Field()
    @IsString()
    @IsNotEmpty()
    @MaxLength(80)
    description: string;

    // Attendance points

    @Field(() => Boolean, { nullable: true })
    @IsOptional()
    @IsBoolean()
    useAttendancePoints?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    @Min(0)
    attendancePoints?: number;

    // Notifications

    @Field(() => Boolean, { nullable: true })
    @IsOptional()
    @IsBoolean()
    useNotifications?: boolean;

    @Field(() => Number, { nullable: true })
    @IsOptional()
    @Type(() => Number)
    studentNotifyFirst?: number;

    @Field(() => Number, { nullable: true })
    @IsOptional()
    @Type(() => Number)
    studentNotifySecond?: number;

    // Landing

    @Field({ nullable: true })
    @IsBoolean()
    useDefaultLanding: boolean;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsUrl()
    externalLandingUrl?: string;

    // Access

    @Field({ nullable: true })
    @IsOptional()
    @IsEnum(WebinarVisibilityPresentation)
    visibility?: WebinarVisibilityPresentation;

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    maxStudentCapacity?: number;

    @Field(() => Boolean, { nullable: true })
    @IsOptional()
    @IsBoolean()
    enableStudentLimit?: boolean;

    // Registration

    @Field()
    @IsEnum(RegistrationTypePresentation)
    registrationType: RegistrationTypePresentation;

    @Field()
    @IsOptional()
    @IsBoolean()
    registrationForEntry: boolean;

    @Field()
    @IsOptional()
    @IsBoolean()
    registrationForView: boolean;

    @Field(() => Boolean, { nullable: true })
    @IsOptional()
    @IsBoolean()
    isPhoneRequiredOnRegistration?: boolean;

    @Field(() => [StreamCreateInput])
    @ArrayNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => StreamCreateInput)
    streams: StreamCreateInput[];

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    integrationCode?: string;
}
