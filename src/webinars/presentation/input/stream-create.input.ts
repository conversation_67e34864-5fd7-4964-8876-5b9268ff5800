import { Field, InputType } from '@skillspace/graphql';
import {
    ArrayNotEmpty,
    IsBoolean,
    IsEnum,
    IsNumber,
    IsOptional,
    IsString,
    IsUrl,
    <PERSON>UUID,
    MaxLength,
} from 'class-validator';

import { StreamParams } from '../../domain/models/stream/stream.model';
import { StreamTypePresentation } from '../enum.presentation';

export type IStreamCreateInput = Omit<
    StreamParams,
    'webinarId' | 'schoolUuid' | 'date' | 'participants' | 'streamType'
> & {
    date: number;
    streamType: StreamTypePresentation;
    speakers: string[];
};

@InputType()
export class StreamCreateInput implements IStreamCreateInput {
    @Field()
    @IsString()
    @MaxLength(70)
    title: string;

    @Field()
    @IsNumber()
    date: number;

    @Field()
    @IsNumber()
    duration: number;

    @Field()
    @IsEnum(StreamTypePresentation)
    streamType: StreamTypePresentation;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsUrl()
    externalStreamUrl?: string;

    @Field(() => Boolean, { nullable: true })
    @IsOptional()
    @IsBoolean()
    hasRecordUrl?: boolean = false;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsUrl()
    recordUrl?: string;

    @Field(() => Boolean, { nullable: true })
    @IsOptional()
    @IsBoolean()
    autoRecord?: boolean = false;

    @Field(() => [String])
    @ArrayNotEmpty()
    @IsUUID('4', { each: true })
    speakers: string[] = [];
}
