import { Inject } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { GqlSchoolId, GqlUser, UserContext } from '@skillspace/access';
import { Args, FileUpload, GraphQLUpload, ID, Mutation, Query, Resolver } from '@skillspace/graphql';

import { GetFileUploadConvert } from '../../shared/gql-file-upload-convert';
import { CustomError, ERROR_CODE } from '../../shared/webinar-service.error';
import { IFileStorageAdapter } from '../application/adapters/saas/file-storage-adapter.interface';
import { CreateStreamCommand } from '../application/commands/stream/create-stream.command';
import { DeleteStreamCommand } from '../application/commands/stream/delete-stream.command';
import { UpdateStreamCommand } from '../application/commands/stream/update-stream.command';
import { GetStreamQuery } from '../application/queries/get-stream';
import { GetVideoLinkQuery } from '../application/queries/get-video-link';
import { StreamQueryResult } from '../application/queries/result/stream-query.result';
import { StreamModel } from '../domain/models/stream/stream.model';
import { UserFactory } from '../domain/models/user/user.factory';
import { StreamId } from '../domain/objects/stream-id';
import { WebinarId } from '../domain/objects/webinar-id';
import { IParticipantRepository } from '../domain/repositories/participant-repository.interface';
import { IStreamRepository } from '../domain/repositories/stream-repository.interface';
import { IWebinarRepository } from '../domain/repositories/webinar-repository.interface';
import { FILE_STORAGE_ADAPTER, PARTICIPANT_REPOSITORY, STREAM_REPOSITORY, WEBINAR_REPOSITORY } from '../injects';
import { StreamCreateInput } from './input/stream-create.input';
import { StreamUpdateInput } from './input/stream-update.input';
import { mapStreamCreateInputToParams } from './mappers/stream-create-input.mapper';
import { mapStreamUpdateInputToParams } from './mappers/stream-update-input.mapper';
import { StreamOutput } from './output/stream.output';
import { VideoDownloadLinkOutput } from './output/video-download-link.output';
import { VoidResponse } from './output/void-response.output';

@Resolver(() => StreamOutput)
export class StreamsResolver {
    constructor(
        private readonly commandBus: CommandBus,
        private readonly queryBus: QueryBus,
        @Inject(FILE_STORAGE_ADAPTER)
        private readonly storageService: IFileStorageAdapter,
        @Inject(STREAM_REPOSITORY)
        private streamRepo: IStreamRepository,
        @Inject(WEBINAR_REPOSITORY)
        private webinarRepo: IWebinarRepository,
        @Inject(PARTICIPANT_REPOSITORY)
        private participantRepo: IParticipantRepository,
    ) {}

    @Query(() => StreamOutput, { name: 'stream', nullable: true })
    async getOneStream(
        @GqlUser() user: UserContext,
        @GqlSchoolId() schoolUuid: string,
        @Args('streamId', { type: () => ID })
        streamId: string,
    ): Promise<StreamOutput | null> {
        const aStreamId = StreamId.create(streamId);
        const aStream = await this.streamRepo.getStream(aStreamId);
        const anUser = UserFactory.create({ ...user, schoolUuid });

        if (!aStream) {
            throw new CustomError('Поток вебинара не найден при запросе потока', {
                code: ERROR_CODE.NOT_FOUND_ERROR,
                details: { streamId: aStreamId.unwrap() },
            });
        }

        const aWebinar = await this.webinarRepo.getWebinar(aStream.aWebinarId);

        const studentsCount = await this.participantRepo.getStudentCountsForStream(aStreamId);
        const result = new StreamQueryResult(
            anUser,
            aWebinar,
            {
                ...aStream.params,
                id: aStream.anId.unwrap(),
                date: aStream.params.date,
                participants: [...aStream.params.participants, ...aStream.students],
            },
            studentsCount,
        );

        const isRegisteredStudent = aStream.isRegisteredEmail(user.email);

        if (anUser.isEmployee() || isRegisteredStudent) {
            return result;
        }

        return {
            ...result,
            recordUrl: null,
            externalStreamUrl: null,
        };
    }

    @Query(() => VideoDownloadLinkOutput, { name: 'getVideoDownloadLink', nullable: true })
    async getVideoDownloadLink(
        @GqlUser() user: UserContext,
        @GqlSchoolId() schoolUuid: string,
        @Args('streamId', { type: () => ID })
        streamId: string,
    ): Promise<VideoDownloadLinkOutput> {
        const query = new GetVideoLinkQuery({ streamId, userParams: { ...user, schoolUuid } });
        return this.queryBus.execute(query);
    }

    @Mutation(() => StreamOutput, { name: 'createStream' })
    async createStream(
        @GqlUser() user: UserContext,
        @GqlSchoolId() schoolUuid: string,
        @Args('webinarId', { type: () => ID })
        webinarId: string,
        @Args('createStreamInput') createStreamInput: StreamCreateInput,
    ): Promise<StreamOutput> {
        const anUser = UserFactory.create({ ...user, schoolUuid });
        const aWebinarId = WebinarId.create(webinarId);

        const createParams = mapStreamCreateInputToParams(createStreamInput, aWebinarId.unwrap(), schoolUuid);
        const aStreamId: StreamId = await this.commandBus.execute(
            new CreateStreamCommand(anUser, {
                ...createParams,
                coverImage: null,
            }),
        );
        return this.queryBus.execute(new GetStreamQuery({ ...user, schoolUuid }, aStreamId));
    }

    @Mutation(() => StreamOutput, { name: 'updateStream', nullable: true })
    async updateStream(
        @GqlUser() user: UserContext,
        @GqlSchoolId() schoolUuid: string,
        @Args('streamId', { type: () => ID })
        streamId: string,
        @Args('updateStreamInput') updateStreamInput: StreamUpdateInput,
        @Args({ name: 'cover', type: () => GraphQLUpload, nullable: true })
        cover?: { promise: Promise<FileUpload> },
    ): Promise<StreamOutput> {
        const anUser = UserFactory.create({ ...user, schoolUuid });
        const aStreamId = StreamId.create(streamId);

        const params = mapStreamUpdateInputToParams(updateStreamInput);
        const anUpdatedStream: StreamModel = await this.commandBus.execute(
            new UpdateStreamCommand(anUser, aStreamId, params),
        );

        if (cover) {
            const oFile = await cover.promise;
            const file = (await GetFileUploadConvert.convert(oFile.createReadStream ? [oFile] : []))[0];
            await this.storageService.removeStreamCovers({
                webinarId: anUpdatedStream.params.webinarId,
                streamId,
            });
            const { key, name } = await this.storageService.uploadStreamCover({
                webinarId: anUpdatedStream.params.webinarId,
                streamId,
                cover: file,
            });
            await this.commandBus.execute(
                new UpdateStreamCommand(anUser, aStreamId, {
                    coverImage: { key, name },
                }),
            );
        }

        return this.queryBus.execute(new GetStreamQuery({ ...user, schoolUuid }, aStreamId));
    }

    @Mutation(() => VoidResponse, { name: 'deleteStream', nullable: true })
    async deleteStream(
        @GqlUser() user: UserContext,
        @GqlSchoolId() schoolUuid: string,
        @Args('streamId', { type: () => ID })
        streamId: string,
    ): Promise<VoidResponse> {
        const anUser = UserFactory.create({ ...user, schoolUuid });
        const aStreamId = StreamId.create(streamId);

        await this.commandBus.execute(new DeleteStreamCommand(anUser, aStreamId));

        return { success: true };
    }
}
