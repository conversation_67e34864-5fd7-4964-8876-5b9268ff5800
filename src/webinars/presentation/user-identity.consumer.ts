import { Injectable, Logger } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import {
    AmqpSubscribe,
    RabbitPayload,
    RequestWebinarsRegistrationDetailsContract,
    WebinarsRegistrationDetailsContract,
} from '@skillspace/amqp-contracts';

import { CustomError, ERROR_CODE } from '../../shared/webinar-service.error';
import { SyncUserDataCommand } from '../application/commands/sync/sync-user-data.command';
import { SynchronizeStudentsIdentityCommand } from '../application/commands/sync/synchronize-students-identity.command';

export enum EVENT_TYPE_ENUM {
    /**
     * Предоставление UUID пользователя, который соответствует его email
     */
    STUDENT_IDS_PROVIDED = 'STUDENT_IDS_PROVIDED',
    /**
     * Предоставление измененных пользователем данных, в соответствии его UUID
     */
    STUDENTS_CHANGED_REGISTRATION_DATA = 'STUDENTS_CHANGED_REGISTRATION_DATA',
}

@Injectable()
export class UserIdentityConsumer {
    private readonly logger = new Logger(UserIdentityConsumer.name);

    constructor(private readonly commandBus: CommandBus) {}

    @AmqpSubscribe(WebinarsRegistrationDetailsContract)
    async handleMessage(
        @RabbitPayload()
        message: RequestWebinarsRegistrationDetailsContract,
    ): Promise<void> {
        try {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
            if (message.payload.type === EVENT_TYPE_ENUM.STUDENT_IDS_PROVIDED) {
                this.logger.log(
                    { studentsCount: message.payload.students.length },
                    'Синхронизация идентификаторов пользователей',
                );
                await this.commandBus.execute(new SynchronizeStudentsIdentityCommand(message.payload));
            }

            // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
            if (message.payload.type === EVENT_TYPE_ENUM.STUDENTS_CHANGED_REGISTRATION_DATA) {
                this.logger.log(
                    { studentsCount: message.payload.students.length },
                    'Синхронизация данных пользователей',
                );
                await this.commandBus.execute(new SyncUserDataCommand(message.payload));
            }
        } catch (e: unknown) {
            const errorOptions = {
                code: ERROR_CODE.BAD_REQUEST_ERROR,
                cause: e,
                details: {
                    payload: message.payload,
                },
            };
            throw new CustomError('Ошибка при синхронизации данных пользователей', errorOptions);
        }
    }
}
