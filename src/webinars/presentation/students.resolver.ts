import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { GqlSchoolId, GqlUser, UserContext } from '@skillspace/access';
import { Args, ID, Mutation, Resolver } from '@skillspace/graphql';

import { InviteStudentsCommand } from '../application/commands/activities/invite-students.command';
import { JoinWebinarCommand } from '../application/commands/activities/join-webinar.command';
import { RegisterStudentCommand } from '../application/commands/activities/register-students.command';
import { CheckRegistrationQuery } from '../application/queries/check-registrations';
import { UserFactory } from '../domain/models/user/user.factory';
import { StreamId } from '../domain/objects/stream-id';
import { CheckRegistrationArgs } from './input/check-registration.args';
import { InviteStudentsArgs } from './input/invite-students.args';
import { RegisterStudentArgs } from './input/register-student.args';
import { InvitedStudentsOutput } from './output/invited-students.output';
import { StreamOutput } from './output/stream.output';
import { StudentOutput } from './output/student.output';
import { VoidResponse } from './output/void-response.output';

@Resolver(() => VoidResponse)
export class StudentsResolver {
    constructor(
        private readonly commandBus: CommandBus,
        private readonly queryBus: QueryBus,
    ) {}

    @Mutation(() => InvitedStudentsOutput, {
        name: 'inviteStudents',
        nullable: true,
    })
    async inviteStudents(
        @GqlUser() user: UserContext,
        @GqlSchoolId() schoolUuid: string,
        @Args() args: InviteStudentsArgs,
    ): Promise<InvitedStudentsOutput> {
        const { streamId, emails } = args;
        const anUser = UserFactory.create({ ...user, schoolUuid });
        const aStreamId = StreamId.create(streamId);
        const invitedStudentsCount: number = await this.commandBus.execute(
            new InviteStudentsCommand(anUser, aStreamId, emails),
        );
        return { success: true, invitedStudentsCount };
    }

    // Ручка без обязательной авторизации
    @Mutation(() => VoidResponse, { name: 'registerStudent', nullable: true })
    async registerStudent(
        @GqlUser() user: UserContext,
        @GqlSchoolId() schoolUuid: string,
        @Args() args: RegisterStudentArgs,
        @Args('token', { nullable: true }) token?: string,
        @Args('ip', { nullable: true }) ip?: string,
    ): Promise<VoidResponse> {
        const anUser = UserFactory.create({ ...user, schoolUuid });
        const { streamId, name, email, timezone } = args;
        const aStreamId = StreamId.create(streamId);
        await this.commandBus.execute(
            new RegisterStudentCommand(anUser, {
                aStreamId,
                studentEmail: email,
                studentName: name,
                studentTimeZone: timezone,
                phoneNumber: args.phoneNumber,
                token,
                ip,
            }),
        );
        return { success: true };
    }

    // Ручка без авторизации
    @Mutation(() => StudentOutput, {
        name: 'checkRegistration',
        nullable: true,
    })
    async checkRegistration(@Args() args: CheckRegistrationArgs): Promise<StudentOutput> {
        const aStreamId = StreamId.create(args.streamId);
        return this.queryBus.execute(new CheckRegistrationQuery(aStreamId, args.userEmail));
    }

    @Mutation(() => StreamOutput, { name: 'joinWebinar', nullable: true })
    async joinWebinar(
        @GqlUser() user: UserContext,
        @GqlSchoolId() schoolUuid: string,
        @Args('streamId', { type: () => ID }) streamId: string,
        @Args('email') email: string,
        currentDate = new Date(),
    ): Promise<StreamOutput> {
        return this.commandBus.execute(new JoinWebinarCommand(user, schoolUuid, streamId, email, currentDate));
    }
}
