import { Modu<PERSON>, Provider } from '@nestjs/common';
import { applyOpentelemetryToProvider } from '@skillspace/tracing';

import { InviteStudentsHandler } from './application/commands/activities/invite-students.command';
import { Join<PERSON>ebinar<PERSON>ommandHandler } from './application/commands/activities/join-webinar.command';
import { RegisterStudentHandler } from './application/commands/activities/register-students.command';
import { MarkRoomAsLiveCommandHandler } from './application/commands/rooms/mark-room-as-live.command';
import { MarkRoomAsOfflineCommandHandler } from './application/commands/rooms/mark-room-as-offline.command';
import { UploadRoomRecordCommandHandler } from './application/commands/rooms/upload-room-record.command';
import { CreateStreamHandler } from './application/commands/stream/create-stream.command';
import { DeleteStreamHandler } from './application/commands/stream/delete-stream.command';
import { UpdateStreamHandler } from './application/commands/stream/update-stream.command';
import { SyncUserDataHandler } from './application/commands/sync/sync-user-data.command';
import { SynchronizeStudentsIdentityHandler } from './application/commands/sync/synchronize-students-identity.command';
import { CreateWebinarHandler } from './application/commands/webinars/create-webinar.command';
import { DeleteWebinarHandler } from './application/commands/webinars/delete-webinar.command';
import { UpdateWebinarHandler } from './application/commands/webinars/update-webinar.command';
import { StudentJoinedStreamEventHandler } from './application/events/activities/student-joined-stream.event';
import { StudentRegisteredEventHandler } from './application/events/activities/student-registered.event';
import { StudentReplayedRecordEventHandler } from './application/events/activities/student-replayed-record.event';
import { StudentsGotIdentifiersEventHandler } from './application/events/activities/students-got-identifiers.event';
import { StudentsInvitedEventHandler } from './application/events/activities/students-invited.event';
import { StreamRemovedEventHandler } from './application/events/streams/stream-removed.event';
import { StreamUpdatedEventHandler } from './application/events/streams/stream-upated.event';
import { StreamCreatedEventHandler } from './application/events/streams/strem-created.event';
import { WebinarRemovedEventHandler } from './application/events/webinars/webinar-removed.event';
import { CheckRegistrationQueryHandler } from './application/queries/check-registrations';
import { GetStreamHandler } from './application/queries/get-stream';
import { GetVideoLinkHandler } from './application/queries/get-video-link';
import { GetWebinarHandler } from './application/queries/get-webinar';
import { GetWebinarCardsHandler } from './application/queries/get-webinar-cards';
import { GetWebinarsHandler } from './application/queries/get-webinars';
import { ContentService } from './application/services/content.service';
import { PlansService } from './application/services/plans.service';
import { SchoolService } from './application/services/school.service';
import { TransferService } from './application/services/transfer.service';
import { EditorServiceAdapter } from './infrastructure/adapters/microservices/editor-service.adapter';
import { MonolithPlansServiceAdapter } from './infrastructure/adapters/microservices/monolith-plans-service.adapter';
import { MonolithSchoolRegistryAdapter } from './infrastructure/adapters/microservices/monolith-school-registry.adapter';
import { MonolithUserRegistryAdapter } from './infrastructure/adapters/microservices/monolith-user-registry.adapter';
import { NotificationServiceAdapter } from './infrastructure/adapters/microservices/notification-service.adapter';
import { StatisticsServiceAdapter } from './infrastructure/adapters/microservices/statistics-service.adapter';
import { KinescopeVideoStorageAdapter } from './infrastructure/adapters/saas/kinescope/kinescope-video-storage.adapter';
import { LiveDigitalAdapter } from './infrastructure/adapters/saas/live-digital/live-digital.adapter';
import { S3FileStorageAdapter } from './infrastructure/adapters/saas/s3-storage/s3-file-storage.adapter';
import { YandexCaptchaAdapter } from './infrastructure/adapters/saas/yandex-captcha/yandex-captcha.adapter';
import { ParticipantRepository } from './infrastructure/repositories/participant.repository';
import { SchoolRepository } from './infrastructure/repositories/school.repository';
import { StreamRepository } from './infrastructure/repositories/stream.repository';
import { StudentWebinarsHistoryRepository } from './infrastructure/repositories/student-webinars-history.repository';
import { WebinarRepository } from './infrastructure/repositories/webinar.repository';
import { WebinarsCron } from './infrastructure/scheduler/processing-video.cron';
import {
    CAPTCHA_SERVICE_ADAPTER,
    EDITOR_SERVICE_ADAPTER,
    FILE_STORAGE_ADAPTER,
    NOTIFICATION_SERVICE_ADAPTER,
    PARTICIPANT_REPOSITORY,
    PLANS_SERVICE_ADAPTER,
    SCHOOL_REGISTRY_ADAPTER,
    SCHOOL_REPOSITORY,
    STATISTICS_SERVICE_ADAPTER,
    STREAM_REPOSITORY,
    STUDENT_WEBINARS_HISTORY_REPOSITORY,
    USER_REGISTRY_ADAPTER,
    VIDEO_STORAGE_ADAPTER,
    WEBINAR_PROVIDER_ADAPTER,
    WEBINAR_REPOSITORY,
} from './injects';
import { StreamsResolver } from './presentation/stream.resolver';
import { StudentsResolver } from './presentation/students.resolver';
import { UserIdentityConsumer } from './presentation/user-identity.consumer';
import { WebhooksController } from './presentation/webhooks.controller';
import { WebinarsResolver } from './presentation/webinar.resolver';

const REPOSITORIES: Provider[] = [
    { provide: SCHOOL_REPOSITORY, useClass: SchoolRepository },
    { provide: WEBINAR_REPOSITORY, useClass: WebinarRepository },
    { provide: STREAM_REPOSITORY, useClass: StreamRepository },
    { provide: PARTICIPANT_REPOSITORY, useClass: ParticipantRepository },
    {
        provide: STUDENT_WEBINARS_HISTORY_REPOSITORY,
        useClass: StudentWebinarsHistoryRepository,
    },
];

const ADAPTERS: Provider[] = [
    // SAAS
    { provide: FILE_STORAGE_ADAPTER, useClass: S3FileStorageAdapter },
    { provide: VIDEO_STORAGE_ADAPTER, useClass: KinescopeVideoStorageAdapter },
    { provide: CAPTCHA_SERVICE_ADAPTER, useClass: YandexCaptchaAdapter },
    { provide: WEBINAR_PROVIDER_ADAPTER, useClass: LiveDigitalAdapter },

    // MICROSERVICES
    { provide: STATISTICS_SERVICE_ADAPTER, useClass: StatisticsServiceAdapter },
    { provide: PLANS_SERVICE_ADAPTER, useClass: MonolithPlansServiceAdapter },
    { provide: SCHOOL_REGISTRY_ADAPTER, useClass: MonolithSchoolRegistryAdapter },
    { provide: USER_REGISTRY_ADAPTER, useClass: MonolithUserRegistryAdapter },
    { provide: NOTIFICATION_SERVICE_ADAPTER, useClass: NotificationServiceAdapter },
    { provide: EDITOR_SERVICE_ADAPTER, useClass: EditorServiceAdapter },
];

const PROVIDERS: Provider[] = [WebinarsCron, SchoolService, PlansService, TransferService, ContentService];

const COMMAND_HANDLERS = [
    CreateWebinarHandler,
    CreateStreamHandler,
    UpdateWebinarHandler,
    UpdateStreamHandler,
    DeleteWebinarHandler,
    DeleteStreamHandler,
    InviteStudentsHandler,
    RegisterStudentHandler,
    JoinWebinarCommandHandler,
    SynchronizeStudentsIdentityHandler,
    SyncUserDataHandler,
    // Rooms
    MarkRoomAsLiveCommandHandler,
    UploadRoomRecordCommandHandler,
    MarkRoomAsOfflineCommandHandler,
];

const EVENT_HANDLERS = [
    StudentRegisteredEventHandler,
    StudentsInvitedEventHandler,
    StudentsGotIdentifiersEventHandler,
    StudentJoinedStreamEventHandler,
    StudentReplayedRecordEventHandler,
    WebinarRemovedEventHandler,
    StreamCreatedEventHandler,
    StreamUpdatedEventHandler,
    StreamRemovedEventHandler,
];

const QUERY_HANDLERS = [
    GetWebinarCardsHandler,
    GetWebinarHandler,
    GetStreamHandler,
    CheckRegistrationQueryHandler,
    GetWebinarsHandler,
    GetVideoLinkHandler,
];

const PRESENTATION = [WebinarsResolver, StreamsResolver, StudentsResolver, UserIdentityConsumer];

@Module({
    controllers: [WebhooksController],
    providers: [
        ...PRESENTATION,
        ...EVENT_HANDLERS,
        ...QUERY_HANDLERS,
        ...PROVIDERS,
        ...COMMAND_HANDLERS,
        ...REPOSITORIES,
        ...ADAPTERS,
    ].map((provider) => applyOpentelemetryToProvider(provider)),
})
export class WebinarModule {}
