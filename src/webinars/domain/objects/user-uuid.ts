import { Wrapper } from '@skillspace/cqrs';
import { isUUID } from 'class-validator';

import { CustomError, ERROR_CODE } from '../../../shared/webinar-service.error';

export class UserUuid extends Wrapper<string> {
    constructor(value: string) {
        super(value);
    }

    public static wrap(value: string): UserUuid {
        return new UserUuid(value);
    }

    public static create(value: string): UserUuid {
        if (!value) {
            throw new CustomError('Идентификатор пользователя не определен');
        }

        if (!isUUID(value)) {
            throw new CustomError('Некорректный UUID идентификатор пользователя', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: { uuid: value },
            });
        }

        return new UserUuid(value);
    }
}
