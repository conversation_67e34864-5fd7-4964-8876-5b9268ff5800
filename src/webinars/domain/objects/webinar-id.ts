import { Wrapper } from '@skillspace/cqrs';
import { ObjectId } from 'bson';
import { isMongoId } from 'class-validator';

import { CustomError, ERROR_CODE } from '../../../shared/webinar-service.error';

export class WebinarId extends Wrapper<string> {
    constructor(value: string) {
        super(value);
    }

    public static generate(): WebinarId {
        const id = new ObjectId().toHexString();
        return WebinarId.wrap(id);
    }

    public static wrap(value: string): WebinarId {
        return new WebinarId(value);
    }

    public static create(value: string): WebinarId {
        if (!value) {
            throw new CustomError('Идентификатор вебинара не определен');
        }

        if (!isMongoId(value)) {
            throw new CustomError('Некорректный идентификатор вебинара', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: { id: value },
            });
        }

        return new WebinarId(value);
    }
}
