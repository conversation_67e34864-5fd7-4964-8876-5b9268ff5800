import { Wrapper } from '@skillspace/cqrs';
import { isUUID } from 'class-validator';

import { CustomError, ERROR_CODE } from '../../../shared/webinar-service.error';

export class SchoolUuid extends Wrapper<string> {
    constructor(value: string) {
        super(value);
    }

    public static wrap(value: string): SchoolUuid {
        if (!value) {
            throw new Error('Идентификатор школы пользователя не определен');
        }
        return new SchoolUuid(value);
    }

    public static create(value: string): SchoolUuid {
        if (!value) {
            throw new CustomError('Идентификатор школы пользователя не определен');
        }

        if (!isUUID(value)) {
            throw new CustomError('Некорректный UUID идентификатор школы пользователя', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: { uuid: value },
            });
        }

        return new SchoolUuid(value);
    }
}
