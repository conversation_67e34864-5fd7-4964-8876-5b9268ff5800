import { Wrapper } from '@skillspace/cqrs';
import { ObjectId } from 'bson';
import { isMongoId } from 'class-validator';

import { CustomError, ERROR_CODE } from '../../../shared/webinar-service.error';

export class StreamId extends Wrapper<string> {
    constructor(value: string) {
        super(value);
    }

    public static generate(): StreamId {
        const id = new ObjectId().toHexString();
        return StreamId.wrap(id);
    }

    public static wrap(value: string): StreamId {
        return new StreamId(value);
    }

    public static create(value: string): StreamId {
        if (!value) {
            throw new CustomError('Идентификатор потока не определен');
        }

        if (!isMongoId(value)) {
            throw new CustomError('Невалидный  идентификатор потока', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: { id: value },
            });
        }
        return new StreamId(value);
    }
}
