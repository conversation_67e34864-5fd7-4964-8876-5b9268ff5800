import { S3Config } from '../../infrastructure/adapters/saas/s3-storage/s3.config';

export interface UploadedImageParams {
    key: string;
    name: string;
}

export interface CoverImageInput {
    coverImage: UploadedImageParams | null;
}

export interface CoverImageParams {
    coverImage: UploadedImageParams | null;
}

export class CoverImage {
    private readonly file: UploadedImageParams | null;

    private constructor(public readonly params: CoverImageParams) {
        this.file = params?.coverImage ? params.coverImage : null;
        Object.freeze(this.params);
    }

    get url(): string | null {
        return this.file ? `${S3Config.endpoint}/${S3Config.bucket}/${this.file.key}` : null;
    }

    static create(input: CoverImageInput) {
        return new CoverImage(input);
    }

    static from(params: CoverImageParams): CoverImage {
        return new CoverImage(params);
    }
}
