import { StreamType } from '@prisma/client';
import { UserContext } from '@skillspace/access';

import {
    ALLOWED_ADMIN_TO_JOIN_BEFORE_START,
    ALLOWED_TO_JOIN_AFTER_END,
    StreamModel,
    StreamParams,
} from '../stream/stream.model';
import { RoomStateEnum } from '../stream/stream-room';
import { WebinarModel } from '../webinar/webinar.model';

export type WebinarUserParams = Partial<UserContext> & {
    schoolUuid?: string;
};

export abstract class WebinarUser {
    protected constructor(public readonly params: WebinarUserParams) {
        Object.freeze(params);
    }

    public isStudent(): boolean {
        return false;
    }

    public isGuest(): boolean {
        return false;
    }

    public isEmployee(): boolean {
        return false;
    }

    public hasPermissionToCreate(): boolean {
        return false;
    }

    public hasPermissionToView(webinar?: WebinarModel): boolean {
        return !!webinar?.isPublic;
    }

    public isRegisteredOn(stream: StreamModel): boolean {
        void stream;
        return false;
    }

    public hasPermissionToModify(webinar: WebinarModel): boolean {
        void webinar;
        return false;
    }

    public hasPermissionToRemove(webinar: WebinarModel): boolean {
        void webinar;
        return false;
    }

    // Дублирует логику из метода потока, потому что stream result не принимает в конструктор модель потока, а только параметры
    public isAllowedToJoinStream(
        stream: Pick<StreamParams, 'date' | 'duration' | 'streamType' | 'room'>,
        currentDate = new Date(),
    ): boolean {
        // Определяем временные границы для подключения
        const visitTime = currentDate.getTime();
        const justInTime = new Date(stream.date).getTime();
        const oneHourBeforeStart = justInTime - ALLOWED_ADMIN_TO_JOIN_BEFORE_START;
        const oneHourAfterEnd = justInTime + stream.duration + ALLOWED_TO_JOIN_AFTER_END;

        if (stream.streamType === StreamType.INTERNAL) {
            const roomIsOpened = stream.room.state !== RoomStateEnum.closed;

            if (this.isEmployee()) {
                // Администраторы могут подключаться за 1 час до начала и пока открыта комната
                return oneHourBeforeStart <= visitTime && roomIsOpened;
            }

            // Все остальные могут подключаться только во время вебинара и пока открыта комната
            return justInTime <= visitTime && roomIsOpened;
        }

        if (this.isEmployee()) {
            // Администраторы могут подключаться за 1 час до начала и до 1 часа после окончания
            return oneHourBeforeStart <= visitTime && visitTime <= oneHourAfterEnd;
        }

        // Все остальные могут подключаться только во время вебинара и до 1 часа после окончания
        return justInTime <= visitTime && visitTime <= oneHourAfterEnd;
    }

    abstract toLog(): {
        role: string;
        schoolUuid?: string;
        uuid?: string;
        email?: string;
        name?: string;
    };
}
