import { WebinarUser, WebinarUserParams } from './user-abstract';
import { UserEmployee } from './user-employee';
import { UserGuest } from './user-guest';
import { UserStudent } from './user-student';

export class UserFactory {
    static create(input: WebinarUserParams): WebinarUser {
        if (input.role === 'ROLE_EMPLOYEE') {
            return UserEmployee.create(input);
        }

        if (input.role === 'ROLE_STUDENT') {
            return UserStudent.create(input);
        }

        return UserGuest.create(input);
    }
}
