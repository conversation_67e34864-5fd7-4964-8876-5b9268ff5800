import { StreamModel } from '../stream/stream.model';
import { WebinarUser, WebinarUserParams } from './user-abstract';

export class UserStudent extends WebinarUser {
    public readonly email: string;
    public readonly schoolUuid: string;

    private constructor(params: WebinarUserParams) {
        super(params);
        this.email = params.email;
        this.schoolUuid = params.schoolUuid;
    }

    public static create(params: WebinarUserParams): UserStudent {
        return new UserStudent(params);
    }

    public override isRegisteredOn(stream: StreamModel): boolean {
        return stream.isRegisteredEmail(this.email);
    }

    public isStudent(): boolean {
        return true;
    }

    public toLog(): {
        role: string;
        schoolUuid: string;
        uuid: string;
        email: string;
        name: string;
    } {
        return {
            role: 'student',
            uuid: this.params.id,
            email: this.email,
            name: this.params.name,
            schoolUuid: this.params.schoolUuid,
        };
    }
}
