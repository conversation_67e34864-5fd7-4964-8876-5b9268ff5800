import { PERMISSION } from '../../../../shared/actions.enum';
import { SchoolUuid } from '../../objects/school-uuid';
import { UserUuid } from '../../objects/user-uuid';
import { WebinarModel } from '../webinar/webinar.model';
import { WebinarUser, WebinarUserParams } from './user-abstract';

export class UserEmployee extends WebinarUser {
    private readonly anUserUuid: UserUuid;
    private readonly aSchoolUuid: SchoolUuid;

    private readonly actions: PERMISSION[];

    private constructor(public readonly params: WebinarUserParams) {
        super(params);
        this.anUserUuid = UserUuid.create(params.id);
        this.aSchoolUuid = SchoolUuid.create(params.schoolUuid);
        this.actions = params.actions as PERMISSION[];
    }

    static create(input: WebinarUserParams): WebinarUser {
        return new UserEmployee(input);
    }

    public isEmployee(): boolean {
        return true;
    }

    public hasPermissionToCreate() {
        return this.actions.includes(PERMISSION.WEBINAR_CREATE);
    }

    public hasPermissionToView(webinar?: WebinarModel) {
        if (webinar?.isPublic) {
            return true;
        }

        const hasPermissions =
            this.actions.includes(PERMISSION.WEBINAR_VIEW) || this.actions.includes(PERMISSION.WEBINAR_MODIFY);

        if (!webinar) {
            return hasPermissions;
        }

        return this.isCreator(webinar) || hasPermissions;
    }

    public hasPermissionToModify(webinar: WebinarModel) {
        return (
            this.isCreator(webinar) ||
            (this.isEntityOwnedByUserSchool(webinar) && this.actions.includes(PERMISSION.WEBINAR_MODIFY))
        );
    }

    public hasPermissionToRemove(webinar: WebinarModel) {
        return (
            this.isCreator(webinar) ||
            (this.isEntityOwnedByUserSchool(webinar) && this.actions.includes(PERMISSION.WEBINAR_REMOVE))
        );
    }

    public toLog(): {
        role: string;
        uuid: string;
        schoolUuid: string;
        email: string;
    } {
        return {
            role: 'employee',
            uuid: this.params.id,
            email: this.params.email,
            schoolUuid: this.params.schoolUuid,
        };
    }

    private isCreator(webinar: WebinarModel) {
        return webinar.createdBy.unwrap() === this.anUserUuid.unwrap();
    }

    private isEntityOwnedByUserSchool(webinar: WebinarModel) {
        const anUserSchool = this.aSchoolUuid.unwrap() || undefined;
        return webinar.aSchoolUuid.unwrap() === anUserSchool;
    }
}
