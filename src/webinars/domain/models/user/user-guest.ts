import { WebinarUser, WebinarUserParams } from './user-abstract';

export class UserGuest extends WebinarUser {
    private constructor(params: WebinarUserParams) {
        super(params);
    }

    public static create(params: WebinarUserParams): UserGuest {
        return new UserGuest(params);
    }

    public isGuest(): boolean {
        return true;
    }

    public toLog(): { role: string } {
        return {
            role: 'guest',
        };
    }
}
