import { StreamId } from '../objects/stream-id';
import { UserUuid } from '../objects/user-uuid';

export interface SpeakerParams {
    streamId: string;
    userUuid: string;
}

export type SpeakerCreateInput = SpeakerParams;

export class Speaker {
    readonly aStreamId: StreamId;
    readonly anUserUuid: UserUuid;

    private constructor(public readonly params: SpeakerParams) {
        this.aStreamId = StreamId.wrap(params.streamId);
        this.anUserUuid = UserUuid.wrap(params.userUuid);
        Object.freeze(this.params);
    }

    static create(input: SpeakerCreateInput): Speaker {
        const { streamId, userUuid } = input;
        return new Speaker({ streamId, userUuid });
    }

    static from(params: SpeakerParams): Speaker {
        return new Speaker(params);
    }
}
