import { SaasProviderEnum, StorageStateEnum, StreamType } from '@prisma/client';

import { CustomError, ERROR_CODE } from '../../../../shared/webinar-service.error';

export enum RoomStateEnum {
    // комната открыта
    ready = 'ready',

    // трансляция началась
    started = 'started',

    // трансляция закончена
    finished = 'finished',

    // комната закрыта
    closed = 'closed',
}

export interface RoomParams {
    state: RoomStateEnum;
    provider: SaasProviderEnum;
    spaceId: string;
    roomId: string;
    uploadedRecordIds: string[];
    storage: SaasProviderEnum;
}

export interface StreamPlaceParams {
    streamType: StreamType;
    externalStreamUrl?: string;
    room?: RoomParams;
    autoRecord?: boolean;
    savedRecordUrl?: string;
    storageState?: StorageStateEnum;
}

export type StreamPlaceCreateInput = StreamPlaceParams;
export type StreamPlaceUpdateInput = Partial<StreamPlaceCreateInput>;

export class StreamPlace {
    public readonly room: RoomParams;
    public readonly savedRecordUrl: string;

    private constructor(public readonly params: StreamPlaceParams) {
        this.savedRecordUrl = params.savedRecordUrl;
        this.room = params.room;
    }

    static create(input: StreamPlaceCreateInput): StreamPlace {
        const createParams: StreamPlaceParams = {
            streamType: input.streamType,
            externalStreamUrl: input.externalStreamUrl,
            room: input.room,
            autoRecord: input.autoRecord,
        };

        StreamPlace.validate(createParams);
        return new StreamPlace(createParams);
    }

    public update(input: StreamPlaceUpdateInput): StreamPlace {
        const updateParams: StreamPlaceParams = {
            streamType: input.streamType || this.params.streamType,
            externalStreamUrl: input.externalStreamUrl || this.params.externalStreamUrl,
            room: input.room || this.params.room,
            savedRecordUrl: input.savedRecordUrl || this.params.savedRecordUrl,
            autoRecord: input.autoRecord ?? this.params.autoRecord,
            storageState: input.storageState || this.params.storageState,
        };

        StreamPlace.validate(updateParams);
        return new StreamPlace(updateParams);
    }

    public getUrl(): string {
        if (this.params.streamType === StreamType.EXTERNAL) {
            return this.params.externalStreamUrl;
        }
        throw new CustomError('Для внутреннего потока необходимо реализовать получение url через API провайдера');
    }

    public get isInternal(): boolean {
        return this.params.streamType === StreamType.INTERNAL;
    }

    static from(params: StreamPlaceParams): StreamPlace {
        return new StreamPlace({
            streamType: params.streamType,
            externalStreamUrl: params.externalStreamUrl,
            room: params.room,
        });
    }

    static validate(input: StreamPlaceParams): void {
        if (input.streamType === StreamType.EXTERNAL && !input.externalStreamUrl) {
            throw new CustomError('Для внешнего потока необходимо указать URL', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: input,
            });
        }

        if (input.streamType === StreamType.INTERNAL && !input.room) {
            throw new Error('Не указана комната вебинара');
        }

        if (
            input.streamType === StreamType.INTERNAL &&
            (!input.room.provider || !input.room.spaceId || !input.room.roomId)
        ) {
            throw new Error('Все параметры комнаты  вебинара должны быть заполнены');
        }
    }
}
