import { AggregateRoot } from '@nestjs/cqrs';

import { StreamId } from '../../objects/stream-id';
import { UserUuid } from '../../objects/user-uuid';
import { WebinarModel } from '../webinar/webinar.model';

export interface StudentStreamHistoryRecord {
    id: string;
    streamId: StreamId;
    userUuid: UserUuid;
    isViewed?: boolean;
    attendancePoints: number;
    changed: boolean;
}

export class StudentWebinarHistory extends AggregateRoot {
    private constructor(
        public readonly studentEmail: string,
        public readonly aWebinar: WebinarModel,
        public readonly webinarHistory: StudentStreamHistoryRecord[],
    ) {
        super();
        Object.freeze(this.webinarHistory);
    }

    public getEarnedPoints(): number {
        const isFirstVisit = !this.hasVisitedWebinar;
        const { useAttendancePoints, attendancePoints } = this.aWebinar.params;
        return useAttendancePoints && isFirstVisit ? attendancePoints : 0;
    }

    static from(
        studentEmail: string,
        webinar: WebinarModel,
        history: StudentStreamHistoryRecord[],
    ): StudentWebinarHistory {
        return new StudentWebinarHistory(studentEmail, webinar, history);
    }

    private get hasVisitedWebinar(): boolean {
        return this.webinarHistory.filter((stream) => stream.isViewed).length > 0;
    }
}
