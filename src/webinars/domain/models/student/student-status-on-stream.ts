import { StatisticRecordType } from '@prisma/client';

export interface StreamStatisticRecordParams {
    type: StatisticRecordType;
    date: Date;
    score?: number;
}

export interface StudentStatusOnStreamParams {
    id: string;
    streamId: string;
    email: string;
    statistics: StreamStatisticRecordParams[];
}

export class StudentStatusOnStream {
    public readonly uuid: string;
    public readonly name: string;
    public readonly timezone: string;
    public readonly email: string;
    public readonly phoneNumber: string;

    protected constructor(
        public readonly params: StudentStatusOnStreamParams,
        private readonly student: {
            uuid?: string;
            name?: string;
            timezone?: string;
            phoneNumber?: string;
        },
    ) {
        this.timezone = student?.timezone || null;
        this.name = student?.name || null;
        this.uuid = student?.uuid || null;
        this.phoneNumber = student?.phoneNumber || null;
    }

    public static from(
        params: StudentStatusOnStreamParams,
        student: { uuid?: string; name?: string; timezone?: string; phoneNumber?: string },
    ) {
        return new StudentStatusOnStream(params, student);
    }

    public get hasInvited(): boolean {
        return this.params.statistics.some((record) => record.type === StatisticRecordType.INVITED);
    }

    public get hasRegistered(): boolean {
        // ожидается, что к этому моменту у пользователя будут email, name, timezone
        return this.params.statistics.some((record) => record.type === StatisticRecordType.REGISTERED);
    }

    public get hasAttended(): boolean {
        return this.params.statistics.some((record) => record.type === StatisticRecordType.ATTENDED);
    }

    public get hasViewedRecord(): boolean {
        return this.params.statistics.some((record) => record.type === StatisticRecordType.REPLAYED);
    }

    public get currentStatus(): StatisticRecordType {
        if (this.hasViewedRecord) {
            return StatisticRecordType.REPLAYED;
        }
        if (this.hasAttended) {
            return StatisticRecordType.ATTENDED;
        }
        if (this.hasRegistered) {
            return StatisticRecordType.REGISTERED;
        }
        return StatisticRecordType.INVITED;
    }
}
