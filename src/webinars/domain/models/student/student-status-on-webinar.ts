import { StatisticRecordType } from '@prisma/client';

import { StreamId } from '../../objects/stream-id';

export interface WebinarStatisticRecordParams {
    streamId: string;
    type: StatisticRecordType;
    date: Date;
    score?: number;
}

export interface StudentStatusOnWebinarParams {
    webinarId: string;
    email: string;
    statistics: WebinarStatisticRecordParams[];
}

export class StudentStatusOnWebinar {
    public readonly uuid: string;
    public readonly name: string;
    public readonly timezone: string;
    public readonly email: string;

    protected constructor(
        public readonly params: StudentStatusOnWebinarParams,
        private readonly student: {
            uuid?: string;
            name?: string;
            timezone?: string;
        },
    ) {
        this.timezone = student?.timezone || null;
        this.name = student?.name || null;
        this.uuid = student?.uuid || null;
    }

    public static from(
        params: StudentStatusOnWebinarParams,
        student: { uuid?: string; name?: string; timezone?: string },
    ) {
        return new StudentStatusOnWebinar(params, student);
    }

    public isInvited(aStreamId: StreamId): boolean {
        return this.hasRecordType(aStreamId, StatisticRecordType.INVITED);
    }

    public isRegistered(aStreamId: StreamId): boolean {
        return this.hasRecordType(aStreamId, StatisticRecordType.REGISTERED);
    }

    public isAttended(aStreamId: StreamId): boolean {
        return this.hasRecordType(aStreamId, StatisticRecordType.ATTENDED);
    }

    public get hasVisitedWebinar(): boolean {
        return this.params.statistics.some((record) => record.type === StatisticRecordType.ATTENDED);
    }

    private hasRecordType(aStreamId: StreamId, type: StatisticRecordType): boolean {
        const streamId = aStreamId.unwrap();
        return this.params.statistics.some((record) => record.streamId === streamId && record.type === type);
    }
}
