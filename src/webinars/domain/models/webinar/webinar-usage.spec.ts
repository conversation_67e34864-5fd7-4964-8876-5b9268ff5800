import { StreamUsageRecord, WebinarUsageModel } from './webinar-usage';

const schoolUuid = 'school-123';
const currentDate = new Date('2025-04-01');

const asRecord = (dateStr: string, streamId: string): StreamUsageRecord => {
    return {
        createdAt: new Date(dateStr),
        streamId,
    };
};

describe('WebinarUsageModel', () => {
    it('должен проверить превышение лимита', () => {
        const model = WebinarUsageModel.create({
            schoolUuid,
            usageTracking: [asRecord('2025-02-25', 'old-stream')],
        });

        expect(model.isMonthlyLimitExceeded(1, currentDate)).toBe(false);

        const updatedModel = model.addRecord('new-stream', currentDate);
        expect(updatedModel.isMonthlyLimitExceeded(1, currentDate)).toBe(true);
    });

    it('должен отфильтровать актуальные записи при добавлении', () => {
        const model = WebinarUsageModel.create({
            schoolUuid,
            usageTracking: [
                asRecord('2025-02-28', 'old-stream'), // вне окна
                asRecord('2025-04-15', 'recent-stream'),
            ],
        });

        const updatedModel = model.addRecord('new-stream', currentDate);

        expect(updatedModel.params.usageTracking).toHaveLength(2);
        expect(updatedModel.params.usageTracking.map((r) => r.streamId)).toEqual(['recent-stream', 'new-stream']);
    });

    it('должен удалить запись по ID и отфильтровать неактуальные записи', () => {
        const model = WebinarUsageModel.create({
            schoolUuid,
            usageTracking: [
                asRecord('2025-02-28', 'old-stream'),
                asRecord('2025-04-03', 'stream-1'),
                asRecord('2025-04-09', 'stream-2'),
            ],
        });

        const updatedModel = model.removeRecord('stream-1', currentDate);

        expect(updatedModel.params.usageTracking).toHaveLength(1);
        expect(updatedModel.params.usageTracking[0].streamId).toBe('stream-2');
    });

    it('должен сообщить что лимит превышен', () => {
        const records = [
            asRecord('2025-03-30', 's1'),
            asRecord('2025-04-01', 's2'),
            asRecord('2025-04-02', 's3'),
            asRecord('2025-04-03', 's4'),
            asRecord('2025-04-04', 's5'),
        ];
        const model = WebinarUsageModel.create({
            schoolUuid,
            usageTracking: records,
        });

        expect(model.isMonthlyLimitExceeded(4, currentDate)).toBe(true);
    });

    it('должен сообщить что лимит не превышен', () => {
        const model = WebinarUsageModel.create({
            schoolUuid,
            usageTracking: [asRecord('2025-04-01', 's1'), asRecord('2025-04-02', 's2')],
        });

        expect(model.isMonthlyLimitExceeded(3, currentDate)).toBe(false);
    });

    it('должен вернуть количество вебинаров за последние 30 дней', () => {
        const records = [
            asRecord('2025-02-28', 'too-old'), // вне окна
            asRecord('2025-04-01', 's1'),
            asRecord('2025-04-15', 's2'),
        ];

        const model = WebinarUsageModel.create({
            schoolUuid,
            usageTracking: records,
        });

        expect(model.used(currentDate)).toBe(2);
    });

    it.each([
        ['2025-02-28', '2025-01-28'],
        ['2025-04-01', '2025-03-01'],
        ['2025-04-15', '2025-03-15'],
        ['2025-05-31', '2025-04-30'], // дата корректируется автоматически date-fns
    ])('должен посчитать дату месяц назад для %s', (dateStr, expected) => {
        const model = WebinarUsageModel.create({
            schoolUuid,
            usageTracking: [],
        });

        const resultDate = model.getMonthAgoDate(new Date(dateStr));
        const expectedDate = new Date(expected);

        expect(resultDate).toEqual(expectedDate);
    });
});
