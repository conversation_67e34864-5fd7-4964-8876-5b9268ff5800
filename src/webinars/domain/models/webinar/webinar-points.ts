import { CustomError, ERROR_CODE } from '../../../../shared/webinar-service.error';

export interface WebinarPointsParams {
    useAttendancePoints: boolean;
    attendancePoints: number;
}

export type WebinarPointsCreateInput = Partial<WebinarPointsParams>;
export type WebinarPointsUpdateInput = Partial<WebinarPointsCreateInput>;

export class WebinarPoints {
    private constructor(public readonly params: WebinarPointsParams) {}

    static create(input: WebinarPointsCreateInput) {
        const createInput = {
            useAttendancePoints: input.useAttendancePoints ?? false,
            attendancePoints: input.attendancePoints ?? 0,
        };
        WebinarPoints.validate(createInput);
        return new WebinarPoints(createInput);
    }

    public update(input: WebinarPointsUpdateInput) {
        const updateParams = {
            useAttendancePoints: input.useAttendancePoints ?? this.params.useAttendancePoints,
            attendancePoints: input.attendancePoints ?? this.params.attendancePoints,
        };
        WebinarPoints.validate(updateParams);
        return new WebinarPoints(updateParams);
    }

    static from(params: WebinarPointsParams) {
        return new WebinarPoints({
            useAttendancePoints: params.useAttendancePoints,
            attendancePoints: params.attendancePoints,
        });
    }

    static validate(input: WebinarPointsParams) {
        if (input.useAttendancePoints && input.attendancePoints === 0) {
            throw new CustomError('Для использования поинтов, необходимо указать количество', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: input,
            });
        }
    }
}
