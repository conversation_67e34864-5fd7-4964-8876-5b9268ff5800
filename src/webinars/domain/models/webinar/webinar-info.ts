import { WebinarType, WebinarVisibility } from '@prisma/client';

export interface WebinarInfoParams {
    type: WebinarType;
    title: string;
    description: string;
    visibility: WebinarVisibility;
}

export interface WebinarInfoCreateInput {
    type?: WebinarType;
    title: string;
    description: string;
    // schoolUuid: string;
    // createdBy: string;
    visibility?: WebinarVisibility;
}

export interface WebinarInfoUpdateInput {
    title?: string;
    description?: string;
    visibility?: WebinarVisibility;
}

export class WebinarInfo {
    private constructor(public readonly params: WebinarInfoParams) {}

    static create(input: WebinarInfoCreateInput): WebinarInfo {
        return new WebinarInfo({
            type: input?.type ? input.type : WebinarType.EXTERNAL,
            title: input.title,
            description: input.description,
            visibility: input?.visibility ? input.visibility : WebinarVisibility.DRAFT,
        });
    }

    public update(input: WebinarInfoUpdateInput): WebinarInfo {
        return new WebinarInfo({
            type: this.params.type,
            title: input.title || this.params.title,
            description: input.description || this.params.description,
            visibility: input.visibility || this.params.visibility,
        });
    }

    static from(params: WebinarInfoParams) {
        return new WebinarInfo({
            type: params.type,
            title: params.title,
            description: params.description,
            visibility: params.visibility,
        });
    }
}
