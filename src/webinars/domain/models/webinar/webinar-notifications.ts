const FIFTEEN_MINUTES_MS = 15 * 60 * 1000;

export interface WebinarNotificationsParams {
    useNotifications: boolean;
    notifications: number[];
}

export interface WebinarNotificationsCreateParams {
    useNotifications?: boolean;
    studentNotifyFirst?: number;
    studentNotifySecond?: number;
}

export type WebinarNotificationsUpdateParams = Partial<WebinarNotificationsCreateParams>;

export class WebinarNotifications {
    private constructor(public readonly params: WebinarNotificationsParams) {}

    static create(input: WebinarNotificationsCreateParams) {
        return new WebinarNotifications({
            useNotifications: input?.useNotifications || true,
            notifications: input?.studentNotifyFirst
                ? [input?.studentNotifyFirst, input?.studentNotifySecond].filter(Boolean)
                : [FIFTEEN_MINUTES_MS],
        });
    }

    public update(input: WebinarNotificationsUpdateParams) {
        const studentNotifyFirst = input?.studentNotifyFirst || this.params.notifications[0];
        const studentNotifySecond = input?.studentNotifySecond || this.params.notifications[1];
        return new WebinarNotifications({
            useNotifications: input?.useNotifications ?? this.params.useNotifications,
            notifications: [studentNotifyFirst, studentNotifySecond].filter(Boolean),
        });
    }

    static from(params: WebinarNotificationsParams) {
        return new WebinarNotifications({
            useNotifications: params.useNotifications,
            notifications: params.notifications,
        });
    }
}
