import { subMonths } from 'date-fns';

export interface StreamUsageRecord {
    createdAt: Date;
    streamId: string;
}

export interface WebinarUsageParams {
    schoolUuid: string;
    usageTracking: StreamUsageRecord[];
}

export type WebinarUsageCreateParams = WebinarUsageParams;

export class WebinarUsageModel {
    private constructor(public readonly params: WebinarUsageParams) {}

    static create(params: WebinarUsageCreateParams): WebinarUsageModel {
        return new WebinarUsageModel(params);
    }

    static from(params: WebinarUsageParams): WebinarUsageModel {
        return new WebinarUsageModel(params);
    }

    public addRecord(streamId: string, now: Date): WebinarUsageModel {
        const updatedUsageRecords = [
            ...this.params.usageTracking,
            {
                createdAt: now,
                streamId,
            },
        ];

        return new WebinarUsageModel({
            ...this.params,
            usageTracking: this.filterOldRecords(updatedUsageRecords, now),
        });
    }

    // Удаляет запись по ID вебинара
    public removeRecord(streamId: string, now: Date): WebinarUsageModel {
        const updatedUsageRecords = this.params.usageTracking.filter((record) => record.streamId !== streamId);

        return new WebinarUsageModel({
            ...this.params,
            usageTracking: this.filterOldRecords(updatedUsageRecords, now),
        });
    }

    // Проверяет, превышает ли школа лимит за последние 30 дней
    public isMonthlyLimitExceeded(limit: number, now: Date): boolean {
        const thirtyDaysAgo = this.getMonthAgoDate(now);
        const count = this.params.usageTracking.filter((record) => record.createdAt >= thirtyDaysAgo).length;
        return count >= limit;
    }

    public used(now = new Date()): number {
        const thirtyDaysAgo = this.getMonthAgoDate(now);
        return this.params.usageTracking.filter((record) => record.createdAt >= thirtyDaysAgo).length;
    }

    // фильтрует старые записи
    private filterOldRecords(records: StreamUsageRecord[], now: Date): StreamUsageRecord[] {
        const thirtyDaysAgo = this.getMonthAgoDate(now);
        return records.filter((record) => record.createdAt >= thirtyDaysAgo);
    }

    public getMonthAgoDate(now: Date): Date {
        return subMonths(now, 1);
    }
}
