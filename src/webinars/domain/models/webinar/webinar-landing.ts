import { CustomError, ERROR_CODE } from '../../../../shared/webinar-service.error';

export interface WebinarLandingParams {
    useDefaultLanding: boolean;
    externalLandingUrl?: string;
}

export type WebinarLandingCreateInput = Partial<WebinarLandingParams>;
export type WebinarLandingUpdateInput = Partial<WebinarLandingCreateInput>;

export class WebinarLanding {
    private constructor(public readonly params: WebinarLandingParams) {}

    static create(input: WebinarLandingCreateInput) {
        const createParams = {
            useDefaultLanding: input.useDefaultLanding ?? true,
            externalLandingUrl: input.externalLandingUrl ?? null,
        };
        WebinarLanding.validate(createParams);
        return new WebinarLanding(createParams);
    }

    public update(input: WebinarLandingUpdateInput) {
        const updateParams = {
            useDefaultLanding: input.useDefaultLanding || this.params.useDefaultLanding,
            externalLandingUrl: input.externalLandingUrl || this.params.externalLandingUrl,
        };
        WebinarLanding.validate(updateParams);
        return new WebinarLanding(updateParams);
    }

    static from(params: WebinarLandingParams) {
        return new WebinarLanding({
            useDefaultLanding: params.useDefaultLanding,
            externalLandingUrl: params.externalLandingUrl,
        });
    }

    static validate(input: WebinarLandingParams): void {
        if (!input.useDefaultLanding && !input.externalLandingUrl) {
            throw new CustomError('Для использования внешнего лендинга необходимо указать ссылку', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: input,
            });
        }
    }
}
