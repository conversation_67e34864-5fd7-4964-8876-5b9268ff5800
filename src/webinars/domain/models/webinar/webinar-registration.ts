import { RegistrationType } from '@prisma/client';

import { CustomError, ERROR_CODE } from '../../../../shared/webinar-service.error';

export interface WebinarRegistrationParams {
    enableStudentLimit: boolean;
    maxStudentCapacity: number;
    registrationType: RegistrationType;
    registrationForEntry: boolean;
    registrationForView: boolean;
    isPhoneRequiredOnRegistration: boolean;
}

export type WebinarRegistrationCreateInput = Partial<WebinarRegistrationParams>;

export type WebinarRegistrationUpdateInput = Partial<WebinarRegistrationCreateInput>;

/**
 * Данные для регистрации (лимиты, настройки входа, настройки просмотра).
 */
export class WebinarRegistration {
    private constructor(public readonly params: WebinarRegistrationParams) {}

    /**
     * Создает новый объект WebinarRegistration с параметрами по умолчанию и входными данными.
     */
    static create(input: WebinarRegistrationCreateInput) {
        const createInput = {
            enableStudentLimit: input.enableStudentLimit ?? false,
            maxStudentCapacity: input.maxStudentCapacity ?? 0,
            registrationType: input.registrationType ?? RegistrationType.LANDING,
            registrationForEntry: input.registrationForEntry ?? false,
            registrationForView: input.registrationForView ?? true,
            isPhoneRequiredOnRegistration: input.isPhoneRequiredOnRegistration ?? false,
        };
        WebinarRegistration.validate(createInput);
        return new WebinarRegistration(createInput as WebinarRegistrationParams);
    }

    /**
     * Обновляет текущие параметры WebinarRegistration новыми значениями.
     */
    public update(input: WebinarRegistrationUpdateInput) {
        const updateInput = {
            enableStudentLimit: input.enableStudentLimit ?? this.params.enableStudentLimit,
            maxStudentCapacity: input.maxStudentCapacity ?? this.params.maxStudentCapacity,
            registrationType: input.registrationType ?? this.params.registrationType,
            registrationForEntry: input.registrationForEntry ?? this.params.registrationForEntry,
            registrationForView: input.registrationForView ?? this.params.registrationForView,
            isPhoneRequiredOnRegistration:
                input.isPhoneRequiredOnRegistration ?? this.params.isPhoneRequiredOnRegistration,
        };
        WebinarRegistration.validate(updateInput);
        return new WebinarRegistration(updateInput as WebinarRegistrationParams);
    }

    /**
     * Создает объект WebinarRegistration из переданных параметров.
     */
    static from(params: WebinarRegistrationParams) {
        WebinarRegistration.validate(params);
        return new WebinarRegistration({
            enableStudentLimit: params.enableStudentLimit,
            maxStudentCapacity: params.maxStudentCapacity,
            registrationType: params.registrationType,
            registrationForEntry: params.registrationForEntry,
            registrationForView: params.registrationForView,
            isPhoneRequiredOnRegistration: params.isPhoneRequiredOnRegistration,
        });
    }

    /**
     * Валидация параметров WebinarRegistration.
     */
    static validate(input: WebinarRegistrationParams) {
        if (input.enableStudentLimit && input.maxStudentCapacity <= 0) {
            throw new CustomError('Максимальное количество участников должно быть больше нуля, если лимит включен.', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: input,
            });
        }
    }
}
