import { RegistrationType, StreamType, WebinarVisibility } from '@prisma/client';

import { DATE_NOW, TWO_HOURS } from '../../../../../tests/integration/__data__/test-constants';
import { ADMIN_CONTEXT, ALL_SPEAKER_UUIDS, SCHOOL_UUID } from '../../../../../tests/integration/__data__/webinar.data';
import { UserFactory } from '../user/user.factory';
import { WebinarModel } from './webinar.model';

export const streamCreateInput = {
    title: 'Stream Title',
    date: DATE_NOW,
    duration: TWO_HOURS,
    streamType: StreamType.EXTERNAL,
    externalStreamUrl: 'https://example.com/stream',
    speakers: ALL_SPEAKER_UUIDS,
    webinarId: 'some-webinar-id',
    schoolUuid: SCHOOL_UUID,
};

const webinarCreateInputMandatory = {
    title: 'Webinar Title',
    description: 'Webinar Description',
    registrationType: RegistrationType.LANDING,
    registrationForEntry: false,
    registrationForView: true,
    useDefaultLanding: true,
    streams: [streamCreateInput],
};

let anWebinar: WebinarModel;
const ADMIN = UserFactory.create({ ...ADMIN_CONTEXT, schoolUuid: SCHOOL_UUID });

describe('Webinar Model', () => {
    beforeEach(() => {
        anWebinar = WebinarModel.create(ADMIN, {
            ...webinarCreateInputMandatory,
            isPhoneRequiredOnRegistration: true,
        });
    });

    it('Не должен сбрасывать isPhoneRequiredOnRegistration при публикации вебинара', () => {
        expect(anWebinar.params.visibility).toEqual(WebinarVisibility.DRAFT);
        expect(anWebinar.params.isPhoneRequiredOnRegistration).toEqual(true);

        const updatedWebinar = anWebinar.update(ADMIN, { visibility: WebinarVisibility.PUBLIC });

        expect(updatedWebinar.params.visibility).toEqual(WebinarVisibility.PUBLIC);
        expect(updatedWebinar.params.isPhoneRequiredOnRegistration).toEqual(true);
    });
});
