import { AggregateRoot } from '@nestjs/cqrs';
import { WebinarVisibility } from '@prisma/client';

import { CustomError, ERROR_CODE } from '../../../../shared/webinar-service.error';
import { StreamCreatedEvent } from '../../../application/events/streams/strem-created.event';
import { UploadedImageParams } from '../../objects/cover-image';
import { SchoolUuid } from '../../objects/school-uuid';
import { UserUuid } from '../../objects/user-uuid';
import { WebinarId } from '../../objects/webinar-id';
import { StreamCreateParams, StreamModel, StreamParams } from '../stream/stream.model';
import { WebinarUser } from '../user/user-abstract';
import { WebinarInfo, WebinarInfoCreateInput, WebinarInfoParams, WebinarInfoUpdateInput } from './webinar-info';
import {
    WebinarLanding,
    WebinarLandingCreateInput,
    WebinarLandingParams,
    WebinarLandingUpdateInput,
} from './webinar-landing';
import {
    WebinarNotifications,
    WebinarNotificationsCreateParams,
    WebinarNotificationsParams,
    WebinarNotificationsUpdateParams,
} from './webinar-notifications';
import {
    WebinarPoints,
    WebinarPointsCreateInput,
    WebinarPointsParams,
    WebinarPointsUpdateInput,
} from './webinar-points';
import {
    WebinarRegistration,
    WebinarRegistrationCreateInput,
    WebinarRegistrationParams,
    WebinarRegistrationUpdateInput,
} from './webinar-registration';

export type WebinarParams = WebinarInfoParams &
    WebinarPointsParams &
    WebinarLandingParams &
    WebinarNotificationsParams &
    WebinarRegistrationParams & {
        schoolUuid: string;
        createdBy: string;
        integrationCode?: string;
        coverImage?: UploadedImageParams;
        streams?: (StreamParams & { id: string })[];
        kinescopeFolderId?: string;
    };

export type WebinarCreateParams = WebinarInfoCreateInput &
    WebinarRegistrationCreateInput &
    WebinarPointsCreateInput &
    WebinarNotificationsCreateParams &
    WebinarLandingCreateInput & {
        integrationCode?: string;
        coverImage?: UploadedImageParams;
        streams: StreamCreateParams[];
    };

export type WebinarUpdateParams = WebinarInfoUpdateInput &
    WebinarRegistrationUpdateInput &
    WebinarPointsUpdateInput &
    WebinarNotificationsUpdateParams &
    WebinarLandingUpdateInput & {
        integrationCode?: string;
        coverImage?: UploadedImageParams;
        kinescopeFolderId?: string;
    };

export class WebinarModel extends AggregateRoot {
    public readonly createdBy: UserUuid;
    public readonly aSchoolUuid: SchoolUuid;
    public readonly schoolUuid: string;
    public readonly kinescopeFolderId: string;
    public readonly title: string;

    public readonly streams: (StreamParams & { id: string })[];

    public readonly info: WebinarInfo;
    public readonly registration: WebinarRegistration;
    public readonly points: WebinarPoints;
    public readonly landing: WebinarLanding;
    public readonly notifications: WebinarNotifications;

    constructor(
        public readonly id: string,
        public readonly params: WebinarParams,
    ) {
        super();
        Object.freeze(this.params);
        this.createdBy = UserUuid.wrap(params.createdBy);
        this.aSchoolUuid = SchoolUuid.wrap(params.schoolUuid);
        this.schoolUuid = params.schoolUuid;
        this.kinescopeFolderId = params.kinescopeFolderId;
        this.title = params.title;

        this.streams = params.streams;

        this.info = WebinarInfo.from(params);
        this.registration = WebinarRegistration.from(params);
        this.points = WebinarPoints.from(params);
        this.landing = WebinarLanding.from(params);
        this.notifications = WebinarNotifications.from(params);
    }

    static create(anUser: WebinarUser, input: WebinarCreateParams): WebinarModel {
        if (!anUser.hasPermissionToCreate()) {
            throw new CustomError('Недостаточно прав для создания вебинара', {
                code: ERROR_CODE.FORBIDDEN_ERROR,
                details: { user: anUser.toLog() },
            });
        }

        const anWebinarId = WebinarId.generate();
        const { id: uuid, schoolUuid } = anUser.params;
        const { streams, ...rest } = input;

        const anCreatedStreams = streams.map((streamCreateInput) =>
            StreamModel.create({
                ...streamCreateInput,
                webinarId: anWebinarId.unwrap(),
                schoolUuid,
            }),
        );

        const aCreatedWebinar = new WebinarModel(anWebinarId.unwrap(), {
            createdBy: uuid,
            schoolUuid,
            ...WebinarInfo.create(rest).params,
            ...WebinarRegistration.create(rest).params,
            ...WebinarPoints.create(rest).params,
            ...WebinarLanding.create(rest).params,
            ...WebinarNotifications.create(rest).params,
            integrationCode: rest.integrationCode || null,
            coverImage: rest.coverImage || null,
            streams: anCreatedStreams.map((aStream) => ({
                ...aStream.params,
                id: aStream.anId.unwrap(),
            })),
        });

        /**
         * Generate Events
         */
        anCreatedStreams.forEach((aStream) => {
            aCreatedWebinar.apply(new StreamCreatedEvent(aStream));
        });
        return aCreatedWebinar;
    }

    public update(anUser: WebinarUser, input: WebinarUpdateParams): WebinarModel {
        if (!anUser.hasPermissionToModify(this)) {
            throw new CustomError('Недостаточно прав для изменения параметров вебинара', {
                code: ERROR_CODE.FORBIDDEN_ERROR,
                details: { user: anUser.toLog() },
            });
        }

        const updatedParams = {
            createdBy: this.createdBy.unwrap(),
            schoolUuid: this.aSchoolUuid.unwrap(),
            ...this.info.update(input).params,
            ...this.registration.update(input).params,
            ...this.landing.update(input).params,
            ...this.points.update(input).params,
            ...this.notifications.update(input).params,
            integrationCode: input.integrationCode ?? this.params.integrationCode,
            coverImage: input.coverImage ?? this.params.coverImage,
            streams: null, // обновляются только параметры вебинара, стримы обновляются отдельно
            kinescopeFolderId: input.kinescopeFolderId ?? this.params.kinescopeFolderId,
        };

        const updatedWebinar = new WebinarModel(this.id, updatedParams);
        return updatedWebinar;
    }

    static from(id: WebinarId, webinarParams: WebinarParams): WebinarModel {
        return new WebinarModel(id.unwrap(), webinarParams);
    }

    public get isPublic(): boolean {
        return this.params.visibility === WebinarVisibility.PUBLIC;
    }

    public get isPhoneRequiredOnRegistration(): boolean {
        return this.registration.params.isPhoneRequiredOnRegistration;
    }
}
