import { KinescopeSchoolMeta } from '@prisma/client';

export interface SchoolModelParams {
    uuid: string;
    kinescope: KinescopeSchoolMeta;
}

export class SchoolModel {
    public readonly uuid: string;
    public readonly kinescopeFolderId: string;
    public readonly kinescope: KinescopeSchoolMeta;

    private constructor(public readonly params: SchoolModelParams) {
        this.uuid = params.uuid;
        this.kinescopeFolderId = params.kinescope.folderId;
        this.kinescope = params.kinescope;
    }

    static create(input: SchoolModelParams): SchoolModel {
        return new SchoolModel({
            uuid: input.uuid,
            kinescope: input?.kinescope,
        });
    }

    static from(params: SchoolModelParams): SchoolModel {
        return new SchoolModel({
            uuid: params.uuid,
            kinescope: params?.kinescope,
        });
    }

    public update(params: { kinescope: KinescopeSchoolMeta }): SchoolModel {
        return new SchoolModel({ uuid: this.uuid, kinescope: params.kinescope });
    }
}
