import { StudentsCount } from '../../infrastructure/repositories/participant.repository';
import { StudentStatusOnStream } from '../models/student/student-status-on-stream';
import { StreamId } from '../objects/stream-id';
import { UserUuid } from '../objects/user-uuid';

export interface IParticipantRepository {
    updateSpeakers(streamId: StreamId, speakerIds: UserUuid[]): Promise<void>;
    getExistingSpeakerUuids(streamId: StreamId): Promise<UserUuid[]>;
    getStudentInfoOrNull(streamId: string, email: string): Promise<{ userUuid: string; phoneNumber: string } | null>;
    deleteParticipants(params: { streamId?: StreamId; userUuids?: UserUuid[] }): Promise<void>;
    getStudentCountsForStream(aStreamId: StreamId): Promise<StudentsCount>;
    getStudentStatusOnStreamOrNull(aStreamId: StreamId, studentEmail: string): Promise<StudentStatusOnStream>;
}
