import { StreamModel } from '../models/stream/stream.model';
import { StreamId } from '../objects/stream-id';

export interface IStreamRepository {
    create(stream: StreamModel): Promise<StreamId>;
    update(stream: StreamModel): Promise<void>;
    getStream(id: StreamId, studentEmail?: string): Promise<StreamModel | null>;
    delete(id: StreamId): Promise<void>;
    deleteByWebinarId(webinarId: string): Promise<void>;
    findStreamByRoomId(roomId: string, details?: object): Promise<StreamModel>;
    findStreamIdsToCloseRoom(currentDate: Date): Promise<string[]>;
    findStreamIdsToUpdateRecordUrl(): Promise<string[]>;
    findStreamIdsToRemoveDraftVideos(): Promise<string[]>;
}
