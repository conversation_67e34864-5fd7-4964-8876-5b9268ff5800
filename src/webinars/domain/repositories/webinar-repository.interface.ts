import { UserFactory } from '../models/user/user.factory';
import { WebinarModel } from '../models/webinar/webinar.model';
import { WebinarId } from '../objects/webinar-id';

export interface IWebinarRepository {
    create(webinar: WebinarModel): Promise<void>;
    update(webinar: WebinarModel): Promise<void>;
    getWebinar(webinarId: WebinarId, anUser?: UserFactory): Promise<WebinarModel | null>;
    getWebinarParams(id: WebinarId): Promise<WebinarModel | null>;
    delete(webinarId: WebinarId): Promise<void>;
    deleteAllFromSchool(schoolUuid: WebinarId): Promise<void>;
}
